using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class StockTakeDetail : BaseEntity
{
    public int StockTakeHeaderId { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal SystemQuantity { get; set; }
    public decimal CountedQuantity { get; set; }
    public decimal Variance { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual StockTakeHeader StockTakeHeader { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
    public virtual Batch? Batch { get; set; }
    public virtual Unit? Unit { get; set; }
}
