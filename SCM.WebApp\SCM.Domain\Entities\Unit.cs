using SCM.Domain.Common;
using System;

namespace SCM.Domain.Entities
{
    public class Unit : BaseEntity
    {
        public string Name { get; set; }
        public string Abbreviation { get; set; }
        public int UnitGroupId { get; set; }
        public UnitGroup UnitGroup { get; set; }
        public decimal ConversionFactor { get; set; }
        public decimal BaseConversionFactor { get; set; }
        public bool IsBaseUnit { get; set; }
    }
}
