using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class ProductsController : ApiControllerBase
{
    private readonly IProductService _productService;

    public ProductsController(IProductService productService)
    {
        _productService = productService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ProductDto>>> GetAll()
    {
        var products = await _productService.GetAllProductsAsync();
        return Ok(products);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ProductDto>> GetById(int id)
    {
        var product = await _productService.GetProductByIdAsync(id);
        if (product == null)
            return NotFound();

        return Ok(product);
    }

    [HttpGet("code/{code}")]
    public async Task<ActionResult<ProductDto>> GetByCode(string code)
    {
        var product = await _productService.GetProductByCodeAsync(code);
        if (product == null)
            return NotFound();

        return Ok(product);
    }

    [HttpGet("department/{departmentId}")]
    public async Task<ActionResult<IEnumerable<ProductDto>>> GetByDepartmentId(int departmentId)
    {
        var products = await _productService.GetProductsByDepartmentIdAsync(departmentId);
        return Ok(products);
    }

    [HttpGet("group/{groupId}")]
    public async Task<ActionResult<IEnumerable<ProductDto>>> GetByGroupId(int groupId)
    {
        var products = await _productService.GetProductsByGroupIdAsync(groupId);
        return Ok(products);
    }

    [HttpGet("subgroup/{subGroupId}")]
    public async Task<ActionResult<IEnumerable<ProductDto>>> GetBySubGroupId(int subGroupId)
    {
        var products = await _productService.GetProductsBySubGroupIdAsync(subGroupId);
        return Ok(products);
    }

    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<ProductDto>>> Search([FromQuery] string term)
    {
        if (string.IsNullOrWhiteSpace(term))
            return BadRequest("Search term is required");

        var products = await _productService.SearchProductsAsync(term);
        return Ok(products);
    }

    [HttpPost]
    public async Task<ActionResult<ProductDto>> Create(CreateProductDto createProductDto)
    {
        var product = await _productService.CreateProductAsync(createProductDto);
        return CreatedAtAction(nameof(GetById), new { id = product.Id }, product);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateProductDto updateProductDto)
    {
        if (id != updateProductDto.Id)
            return BadRequest();

        try
        {
            await _productService.UpdateProductAsync(updateProductDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _productService.DeleteProductAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
