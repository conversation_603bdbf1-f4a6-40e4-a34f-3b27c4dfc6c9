import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DepartmentService } from '../../../core/services/department.service';
import { Department, CreateDepartment, UpdateDepartment } from '../../../core/models/department.model';
import { PermissionsService } from '../../../core/services/permissions.service';

@Component({
  selector: 'app-departments',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatSnackBarModule,
    MatDialogModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './departments.component.html',
  styleUrls: ['./departments.component.scss']
})
export class DepartmentsComponent implements OnInit {
  departmentForm!: FormGroup;
  departments: Department[] = [];
  isLoading = false;
  isEditing = false;
  currentDepartmentId: number | null = null;
  
  displayedColumns: string[] = ['id', 'name', 'description', 'isActive', 'actions'];
  
  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private permissionsService: PermissionsService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) { }
  
  ngOnInit(): void {
    this.initForm();
    this.loadDepartments();
  }
  
  initForm(): void {
    this.departmentForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', Validators.maxLength(500)],
      isActive: [true]
    });
  }
  
  loadDepartments(): void {
    this.isLoading = true;
    this.departmentService.getAll().subscribe({
      next: (departments) => {
        this.departments = departments;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading departments', error);
        this.snackBar.open('Error loading departments. Please try again.', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }
  
  onSubmit(): void {
    if (this.departmentForm.invalid) {
      return;
    }
    
    this.isLoading = true;
    
    if (this.isEditing && this.currentDepartmentId) {
      // Update existing department
      const updateDepartment: UpdateDepartment = {
        id: this.currentDepartmentId,
        ...this.departmentForm.value
      };
      
      this.departmentService.update(this.currentDepartmentId, updateDepartment).subscribe({
        next: () => {
          this.snackBar.open('Department updated successfully', 'Close', {
            duration: 3000
          });
          this.resetForm();
          this.loadDepartments();
        },
        error: (error) => {
          console.error('Error updating department', error);
          this.snackBar.open('Error updating department. Please try again.', 'Close', {
            duration: 3000
          });
          this.isLoading = false;
        }
      });
    } else {
      // Create new department
      const createDepartment: CreateDepartment = this.departmentForm.value;
      
      this.departmentService.create(createDepartment).subscribe({
        next: () => {
          this.snackBar.open('Department created successfully', 'Close', {
            duration: 3000
          });
          this.resetForm();
          this.loadDepartments();
        },
        error: (error) => {
          console.error('Error creating department', error);
          this.snackBar.open('Error creating department. Please try again.', 'Close', {
            duration: 3000
          });
          this.isLoading = false;
        }
      });
    }
  }
  
  editDepartment(department: Department): void {
    this.isEditing = true;
    this.currentDepartmentId = department.id;
    
    this.departmentForm.patchValue({
      name: department.name,
      description: department.description || '',
      isActive: department.isActive
    });
  }
  
  deleteDepartment(id: number): void {
    if (confirm('Are you sure you want to delete this department?')) {
      this.isLoading = true;
      
      this.departmentService.delete(id).subscribe({
        next: () => {
          this.snackBar.open('Department deleted successfully', 'Close', {
            duration: 3000
          });
          this.loadDepartments();
        },
        error: (error) => {
          console.error('Error deleting department', error);
          this.snackBar.open('Error deleting department. Please try again.', 'Close', {
            duration: 3000
          });
          this.isLoading = false;
        }
      });
    }
  }
  
  resetForm(): void {
    this.departmentForm.reset({
      name: '',
      description: '',
      isActive: true
    });
    this.isEditing = false;
    this.currentDepartmentId = null;
    this.isLoading = false;
  }
  
  canEdit(): boolean {
    return this.permissionsService.hasPermission('config.department.edit');
  }
  
  canDelete(): boolean {
    return this.permissionsService.hasPermission('config.department.delete');
  }
}
