export interface PurchaseOrder {
  id: number;
  documentNumber: string;
  supplierId: number;
  supplierName?: string;
  costCenterId: number;
  costCenterName?: string;
  orderDate: Date;
  expectedDeliveryDate?: Date;
  status: string;
  notes?: string;
  totalAmount: number;
  createdBy?: string;
  createdAt?: Date;
  updatedBy?: string;
  updatedAt?: Date;
  details?: PurchaseOrderDetail[];
}

export interface PurchaseOrderDetail {
  id: number;
  purchaseOrderId: number;
  productId: number;
  productCode?: string;
  productName?: string;
  unitId?: number;
  unitName?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
}

export interface CreatePurchaseOrder {
  supplierId: number;
  costCenterId: number;
  orderDate: Date;
  expectedDeliveryDate?: Date;
  notes?: string;
  details: CreatePurchaseOrderDetail[];
}

export interface CreatePurchaseOrderDetail {
  productId: number;
  quantity: number;
  unitPrice: number;
  notes?: string;
}

export interface UpdatePurchaseOrder {
  id: number;
  supplierId: number;
  costCenterId: number;
  orderDate: Date;
  expectedDeliveryDate?: Date;
  status?: string;
  notes?: string;
}

export interface UpdatePurchaseOrderDetail {
  id: number;
  quantity: number;
  unitPrice: number;
  notes?: string;
}

export interface PurchaseOrderListItem {
  id: number;
  documentNumber: string;
  supplierName: string;
  costCenterName: string;
  orderDate: Date;
  expectedDeliveryDate?: Date;
  status: string;
  totalAmount: number;
}
