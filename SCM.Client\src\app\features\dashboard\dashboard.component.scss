.dashboard-container {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.date-display {
  font-size: 14px;
  color: #757575;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.summary-card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.summary-card.warning {
  border-left: 4px solid #ff9800;
}

.summary-card.info {
  border-left: 4px solid #2196f3;
}

.summary-card.success {
  border-left: 4px solid #4caf50;
}

.summary-card mat-card-content {
  display: flex;
  align-items: center;
  padding: 16px;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f5f5f5;
  margin-right: 16px;
}

.summary-card.warning .card-icon {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.summary-card.info .card-icon {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.summary-card.success .card-icon {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.card-data {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #757575;
}

.dashboard-actions {
  margin-bottom: 20px;
}

.dashboard-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.dashboard-section {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-section mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.dashboard-section mat-card-content {
  padding: 0;
  overflow-x: auto;
}

.no-data-message {
  padding: 20px;
  text-align: center;
  color: #757575;
}

table {
  width: 100%;
}

.mat-mdc-header-row {
  background-color: #f5f5f5;
}

.mat-mdc-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.status-completed {
  color: #4caf50;
  font-weight: 500;
}

.status-pending {
  color: #ff9800;
  font-weight: 500;
}

.stock-level-warning {
  color: #ff9800;
  font-weight: 500;
}

// Responsive adjustments
@media (max-width: 992px) {
  .dashboard-sections {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .summary-cards {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .date-display {
    margin-top: 8px;
  }
}
