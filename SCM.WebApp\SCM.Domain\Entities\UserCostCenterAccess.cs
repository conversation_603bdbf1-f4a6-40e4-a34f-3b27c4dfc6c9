using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class UserCostCenterAccess : BaseEntity
{
    public int UserId { get; set; }
    public int CostCenterId { get; set; }
    public bool CanView { get; set; } = true;
    public bool CanCreate { get; set; } = false;
    public bool CanEdit { get; set; } = false;
    public bool CanDelete { get; set; } = false;
    public bool CanApprove { get; set; } = false;
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual CostCenter CostCenter { get; set; } = null!;
}
