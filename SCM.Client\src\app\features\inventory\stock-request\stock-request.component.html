<div class="page-container">
  <div class="page-header">
    <h1>Stock Request</h1>
    <div class="header-actions">
      <button mat-button (click)="open()">Open</button>
      <button mat-button (click)="save()" [disabled]="isLoading">Save</button>
      <button mat-raised-button color="primary" (click)="submit()" [disabled]="isLoading || !canSubmit()">Submit</button>
      <button mat-button (click)="discard()" [disabled]="isLoading">Discard</button>
    </div>
  </div>

  <div class="spinner-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <form [formGroup]="stockRequestForm">
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="picker" formControlName="requestDate">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>

      <div class="spacer"></div>

      <div class="reference-number">
        <span>Stock Request No.</span>
        <span class="reference-value">{{stockRequestNo}}</span>
      </div>
    </div>

    <div class="form-row two-columns">
      <mat-form-field appearance="outline">
        <mat-label>From Cost Center</mat-label>
        <mat-select formControlName="fromCostCenterId">
          <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
            {{costCenter.name}}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="stockRequestForm.get('fromCostCenterId')?.hasError('required')">
          From Cost Center is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>To Cost Center</mat-label>
        <mat-select formControlName="toCostCenterId">
          <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
            {{costCenter.name}}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="stockRequestForm.get('toCostCenterId')?.hasError('required')">
          To Cost Center is required
        </mat-error>
        <mat-error *ngIf="stockRequestForm.get('toCostCenterId')?.hasError('sameCostCenter')">
          From and To Cost Centers cannot be the same
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Remarks</mat-label>
        <textarea matInput formControlName="remarks" rows="2"></textarea>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Ref No.</mat-label>
        <input matInput formControlName="refNo">
      </mat-form-field>
    </div>

    <div class="table-container mat-elevation-z2">
      <table mat-table [dataSource]="items.controls">
        <!-- Product Code Column -->
        <ng-container matColumnDef="productCode">
          <th mat-header-cell *matHeaderCellDef>Product Code</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productCode')"
                     [matAutocomplete]="auto" placeholder="Enter product code">
              <mat-autocomplete #auto="matAutocomplete">
                <mat-option *ngFor="let product of filteredProducts | async"
                           [value]="product.code"
                           (click)="selectProduct(i, product)">
                  {{product.code}} - {{product.name}}
                </mat-option>
              </mat-autocomplete>
              <mat-error *ngIf="item.get('productCode')?.hasError('required')">
                Product code is required
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Product Name Column -->
        <ng-container matColumnDef="productName">
          <th mat-header-cell *matHeaderCellDef>Product Name</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productName')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Unit Name Column -->
        <ng-container matColumnDef="unitName">
          <th mat-header-cell *matHeaderCellDef>Unit</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('unitName')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Quantity Column -->
        <ng-container matColumnDef="quantity">
          <th mat-header-cell *matHeaderCellDef>Quantity</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('quantity')">
              <mat-error *ngIf="item.get('quantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="item.get('quantity')?.hasError('min')">
                Quantity must be greater than 0
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Price Column -->
        <ng-container matColumnDef="price">
          <th mat-header-cell *matHeaderCellDef>Price</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('price')">
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Total Column -->
        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef>Total</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('total')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Delivery Date Column -->
        <ng-container matColumnDef="deliveryDate">
          <th mat-header-cell *matHeaderCellDef>Delivery Date</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [matDatepicker]="deliveryPicker" [formControl]="item.get('deliveryDate')">
              <mat-datepicker-toggle matSuffix [for]="deliveryPicker"></mat-datepicker-toggle>
              <mat-datepicker #deliveryPicker></mat-datepicker>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let item; let i = index">
            <button mat-icon-button color="warn" (click)="removeItem(i)" [disabled]="isLoading">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <div class="table-actions">
        <button mat-button color="primary" (click)="addItem()" [disabled]="isLoading">
          <mat-icon>add</mat-icon> Add Item
        </button>
      </div>
    </div>

    <div class="totals-section">
      <div class="totals-row">
        <span>Sub Total:</span>
        <span class="total-value">{{calculateSubTotal() | currency}}</span>
      </div>
      <div class="totals-row">
        <span>Tax:</span>
        <span class="total-value">{{calculateTax() | currency}}</span>
      </div>
      <div class="totals-row">
        <span>Total:</span>
        <span class="total-value">{{calculateTotal() | currency}}</span>
      </div>
    </div>
  </form>
</div>
