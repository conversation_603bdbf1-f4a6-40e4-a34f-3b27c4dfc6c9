using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionProcess : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int TransactionTypeId { get; set; }
    public bool RequiresApproval { get; set; } = false;
    public bool AllowsNegativeStock { get; set; } = false;
    public bool UpdatesCost { get; set; } = false;
    public string? ReferenceNumberPrefix { get; set; }
    
    // Navigation properties
    public virtual TransactionType TransactionType { get; set; } = null!;
    public virtual ICollection<TransactionStageType> StageTypes { get; set; } = new List<TransactionStageType>();
    public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; } = new List<TransactionHeader>();
}
