using AutoMapper;
using SCM.Application.DTOs;
using SCM.Domain.Entities;

namespace SCM.Application.Mappings;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // Department mappings
        CreateMap<Department, DepartmentDto>();
        CreateMap<CreateDepartmentDto, Department>();
        CreateMap<UpdateDepartmentDto, Department>();

        // UnitGroup mappings
        CreateMap<UnitGroup, UnitGroupDto>();
        CreateMap<CreateUnitGroupDto, UnitGroup>();
        CreateMap<UpdateUnitGroupDto, UnitGroup>();

        // Unit mappings
        CreateMap<Unit, UnitDto>()
            .ForMember(dest => dest.UnitGroupName, opt => opt.MapFrom(src => src.UnitGroup != null ? src.UnitGroup.Name : null));
        CreateMap<CreateUnitDto, Unit>();
        CreateMap<UpdateUnitDto, Unit>();

        // Product Group mappings
        CreateMap<ProductGroup, ProductGroupDto>()
            .ForMember(dest => dest.DepartmentName, opt => opt.MapFrom(src => src.Department != null ? src.Department.Name : null));
        CreateMap<CreateProductGroupDto, ProductGroup>();
        CreateMap<UpdateProductGroupDto, ProductGroup>();

        // Product SubGroup mappings
        CreateMap<ProductSubGroup, ProductSubGroupDto>()
            .ForMember(dest => dest.GroupName, opt => opt.MapFrom(src => src.Group != null ? src.Group.Name : null));
        CreateMap<CreateProductSubGroupDto, ProductSubGroup>();
        CreateMap<UpdateProductSubGroupDto, ProductSubGroup>();

        // Brand mappings
        CreateMap<Brand, BrandDto>();
        CreateMap<CreateBrandDto, Brand>();
        CreateMap<UpdateBrandDto, Brand>();

        // Tax mappings
        CreateMap<Tax, TaxDto>();
        CreateMap<CreateTaxDto, Tax>();
        CreateMap<UpdateTaxDto, Tax>();

        // Barcode mappings
        CreateMap<Barcode, BarcodeDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<CreateBarcodeDto, Barcode>();
        CreateMap<UpdateBarcodeDto, Barcode>();

        // Product mappings
        CreateMap<Product, ProductDto>()
            .ForMember(dest => dest.BrandName, opt => opt.MapFrom(src => src.Brand != null ? src.Brand.Name : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null))
            .ForMember(dest => dest.UnitGroupName, opt => opt.MapFrom(src => src.Unit != null && src.Unit.UnitGroup != null ? src.Unit.UnitGroup.Name : null))
            .ForMember(dest => dest.DepartmentName, opt => opt.MapFrom(src => src.Department != null ? src.Department.Name : null))
            .ForMember(dest => dest.GroupName, opt => opt.MapFrom(src => src.Group != null ? src.Group.Name : null))
            .ForMember(dest => dest.SubGroupName, opt => opt.MapFrom(src => src.SubGroup != null ? src.SubGroup.Name : null))
            .ForMember(dest => dest.TaxName, opt => opt.MapFrom(src => src.Tax != null ? src.Tax.Name : null))
            .ForMember(dest => dest.TaxRate, opt => opt.MapFrom(src => src.Tax != null ? (decimal?)src.Tax.Rate : null))
            .ForMember(dest => dest.SalesUnitName, opt => opt.MapFrom(src => src.SalesUnit != null ? src.SalesUnit.Name : null));
        CreateMap<CreateProductDto, Product>();
        CreateMap<UpdateProductDto, Product>();

        // Store mappings
        CreateMap<Store, StoreDto>();
        CreateMap<CreateStoreDto, Store>();
        CreateMap<UpdateStoreDto, Store>();

        // Location mappings
        CreateMap<Location, LocationDto>()
            .ForMember(dest => dest.StoreName, opt => opt.MapFrom(src => src.Store != null ? src.Store.Name : null));
        CreateMap<CreateLocationDto, Location>();
        CreateMap<UpdateLocationDto, Location>();

        // CostCenterType mappings
        CreateMap<CostCenterType, CostCenterTypeDto>();
        CreateMap<CreateCostCenterTypeDto, CostCenterType>();
        CreateMap<UpdateCostCenterTypeDto, CostCenterType>();

        // CostCenter mappings
        CreateMap<CostCenter, CostCenterDto>()
            .ForMember(dest => dest.StoreName, opt => opt.MapFrom(src => src.Store != null ? src.Store.Name : null))
            .ForMember(dest => dest.LocationName, opt => opt.MapFrom(src => src.Location != null ? src.Location.Name : null))
            .ForMember(dest => dest.TypeName, opt => opt.MapFrom(src => src.Type != null ? src.Type.Name : src.TypeName));
        CreateMap<CreateCostCenterDto, CostCenter>();
        CreateMap<UpdateCostCenterDto, CostCenter>();

        // ProductCostCenterLink mappings
        CreateMap<ProductCostCenterLink, ProductCostCenterLinkDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter.Name));
        CreateMap<CreateProductCostCenterLinkDto, ProductCostCenterLink>();
        CreateMap<UpdateProductCostCenterLinkDto, ProductCostCenterLink>();

        // Batch mappings
        CreateMap<Batch, BatchDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<CreateBatchDto, Batch>();
        CreateMap<UpdateBatchDto, Batch>();

        // StockOnHand mappings
        CreateMap<StockOnHand, StockOnHandDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter.Name))
            .ForMember(dest => dest.BatchNumber, opt => opt.MapFrom(src => src.Batch.BatchNumber))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null))
            .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.Batch.ExpiryDate));

        // StockTake mappings
        CreateMap<StockTakeHeader, StockTakeHeaderDto>()
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter.Name))
            .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedBy != null ? $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}".Trim() : null))
            .ForMember(dest => dest.CompletedByName, opt => opt.MapFrom(src => src.CompletedBy != null ? $"{src.CompletedBy.FirstName} {src.CompletedBy.LastName}".Trim() : null));
        CreateMap<CreateStockTakeHeaderDto, StockTakeHeader>();
        CreateMap<UpdateStockTakeHeaderDto, StockTakeHeader>();

        CreateMap<StockTakeDetail, StockTakeDetailDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.BatchNumber, opt => opt.MapFrom(src => src.Batch != null ? src.Batch.BatchNumber : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<CreateStockTakeDetailDto, StockTakeDetail>()
            .ForMember(dest => dest.Variance, opt => opt.MapFrom(src => src.CountedQuantity - src.SystemQuantity));
        CreateMap<UpdateStockTakeDetailDto, StockTakeDetail>()
            .ForMember(dest => dest.Variance, opt => opt.MapFrom(src => src.CountedQuantity - src.SystemQuantity));

        // Purchase Order mappings
        CreateMap<PurchaseOrder, PurchaseOrderDto>()
            .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : null))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter != null ? src.CostCenter.Name : null));
        CreateMap<PurchaseOrder, PurchaseOrderListDto>()
            .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : null))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter != null ? src.CostCenter.Name : null));
        CreateMap<CreatePurchaseOrderDto, PurchaseOrder>();
        CreateMap<UpdatePurchaseOrderDto, PurchaseOrder>();

        // Purchase Order Detail mappings
        CreateMap<PurchaseOrderDetail, PurchaseOrderDetailDto>()
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product != null ? src.Product.Code : null))
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : null))
            .ForMember(dest => dest.UnitId, opt => opt.MapFrom(src => src.Product != null ? src.Product.UnitId : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Product != null && src.Product.Unit != null ? src.Product.Unit.Name : null));
        CreateMap<CreatePurchaseOrderDetailDto, PurchaseOrderDetail>();
        CreateMap<UpdatePurchaseOrderDetailDto, PurchaseOrderDetail>();
    }
}
