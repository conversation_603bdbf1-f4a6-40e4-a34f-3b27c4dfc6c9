using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class UnitService : IUnitService
{
    private readonly IRepository<Unit> _unitRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public UnitService(
        IRepository<Unit> unitRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _unitRepository = unitRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<UnitDto>> GetAllUnitsAsync()
    {
        var units = await _dbContext.Units
            .Include(u => u.UnitGroup)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<UnitDto>>(units);
    }

    public async Task<UnitDto?> GetUnitByIdAsync(int id)
    {
        var unit = await _dbContext.Units
            .Include(u => u.UnitGroup)
            .FirstOrDefaultAsync(u => u.Id == id);
            
        return unit != null ? _mapper.Map<UnitDto>(unit) : null;
    }

    public async Task<IEnumerable<UnitDto>> GetUnitsByUnitGroupIdAsync(int unitGroupId)
    {
        var units = await _dbContext.Units
            .Include(u => u.UnitGroup)
            .Where(u => u.UnitGroupId == unitGroupId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<UnitDto>>(units);
    }

    public async Task<UnitDto> CreateUnitAsync(CreateUnitDto createUnitDto)
    {
        var unit = _mapper.Map<Unit>(createUnitDto);
        await _unitRepository.AddAsync(unit);
        
        // Reload the unit with the unit group
        var createdUnit = await _dbContext.Units
            .Include(u => u.UnitGroup)
            .FirstOrDefaultAsync(u => u.Id == unit.Id);
            
        return _mapper.Map<UnitDto>(createdUnit);
    }

    public async Task UpdateUnitAsync(UpdateUnitDto updateUnitDto)
    {
        var unit = await _unitRepository.GetByIdAsync(updateUnitDto.Id);
        if (unit == null)
            throw new KeyNotFoundException($"Unit with ID {updateUnitDto.Id} not found.");

        _mapper.Map(updateUnitDto, unit);
        await _unitRepository.UpdateAsync(unit);
    }

    public async Task DeleteUnitAsync(int id)
    {
        var unit = await _unitRepository.GetByIdAsync(id);
        if (unit == null)
            throw new KeyNotFoundException($"Unit with ID {id} not found.");

        // Check if there are any products using this unit
        var hasProducts = await _dbContext.Products.AnyAsync(p => p.UnitId == id || p.SalesUnitId == id);
        if (hasProducts)
            throw new InvalidOperationException($"Cannot delete Unit with ID {id} because it is being used by one or more products.");

        // Check if there are any recipes using this unit
        var hasRecipes = await _dbContext.Recipes.AnyAsync(r => r.UnitId == id);
        if (hasRecipes)
            throw new InvalidOperationException($"Cannot delete Unit with ID {id} because it is being used by one or more recipes.");

        await _unitRepository.DeleteAsync(unit);
    }
}
