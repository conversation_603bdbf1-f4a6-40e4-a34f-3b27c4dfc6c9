/* You can add global styles to this file, and also import other style files */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

/* Material Icons */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* Custom SCM styles */
.mat-card {
  margin: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.dashboard-card {
  margin-bottom: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.dashboard-card-content {
  padding: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
}

.card-value {
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 8px;
}

.card-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.page-container {
  padding: 16px;
}

.table-container {
  overflow-x: auto;
  margin: 16px 0;
}

.mat-mdc-table {
  width: 100%;
}

.mat-column-actions {
  width: 120px;
}

.action-button {
  margin-right: 8px;
}

.form-field-full-width {
  width: 100%;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.form-col {
  flex: 1;
  padding: 0 8px;
  min-width: 200px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  margin: 0;
}

.low-stock {
  color: #f44336;
}

.warning-stock {
  color: #ff9800;
}

.good-stock {
  color: #4caf50;
}
