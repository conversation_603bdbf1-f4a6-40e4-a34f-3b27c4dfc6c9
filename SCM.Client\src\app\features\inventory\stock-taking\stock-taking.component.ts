import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { StockService } from '../../../core/services/stock.service';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { DepartmentService } from '../../../core/services/department.service';
import { ProductService } from '../../../core/services/product.service';
import { StockTakeHeader, StockTakeDetail, CreateStockTakeHeader, UpdateStockTakeDetail } from '../../../core/models/stock.model';
import { forkJoin, finalize, catchError, of } from 'rxjs';

interface Product {
  id: string;
  name: string;
  category: string;
  unitSize: string;
  systemStock: number;
  lastCountDate: Date | null;
}

interface StockCount {
  id: string;
  date: Date;
  costCenter: string;
  status: string;
  countedBy: string;
  approvedBy: string | null;
}

@Component({
  selector: 'app-stock-taking',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatTabsModule,
    MatCheckboxModule,
    MatDialogModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './stock-taking.component.html',
  styleUrls: ['./stock-taking.component.scss']
})
export class StockTakingComponent implements OnInit {
  stockCountForm!: FormGroup;
  countNo: string = '';
  currentDate: Date = new Date();
  isLoading: boolean = false;

  displayedColumns: string[] = [
    'select',
    'productId',
    'productName',
    'category',
    'unitSize',
    'systemStock',
    'countedStock',
    'difference',
    'notes'
  ];

  historyColumns: string[] = [
    'id',
    'date',
    'costCenter',
    'status',
    'countedBy',
    'approvedBy',
    'actions'
  ];

  products: Product[] = [];
  stockCounts: StockTakeHeader[] = [];

  filteredProducts: Product[] = [];
  selectedCategory: string = '';

  categories: string[] = ['All Categories'];
  costCenters: any[] = [];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private stockService: StockService,
    private costCenterService: CostCenterService,
    private departmentService: DepartmentService,
    private productService: ProductService
  ) {
    this.filteredProducts = [];
  }

  ngOnInit(): void {
    this.isLoading = true;

    // Load reference data
    forkJoin({
      costCenters: this.costCenterService.getAllCostCenters().pipe(catchError(error => {
        console.error('Error loading cost centers', error);
        return of([]);
      })),
      departments: this.departmentService.getAll().pipe(catchError(error => {
        console.error('Error loading departments', error);
        return of([]);
      })),
      stockTakes: this.stockService.getAllStockTakes().pipe(catchError(error => {
        console.error('Error loading stock takes', error);
        return of([]);
      }))
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe({
      next: (data) => {
        // Set cost centers
        this.costCenters = data.costCenters;

        // Set categories from departments
        this.categories = ['All Categories', ...data.departments.map(d => d.name)];

        // Set stock counts
        this.stockCounts = data.stockTakes;

        // Generate new count number
        this.generateCountNumber();

        // Initialize form
        this.initForm();
      },
      error: (error) => {
        console.error('Error loading reference data', error);
        this.snackBar.open('Error loading reference data. Please try again later.', 'Close', {
          duration: 5000
        });
        this.initForm();
      }
    });
  }

  generateCountNumber(): void {
    // Generate a count number based on the current date and existing counts
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');

    // Count the number of stock takes for today
    const todayStockTakes = this.stockCounts.filter(count => {
      const countDate = new Date(count.startDate);
      return countDate.getFullYear() === today.getFullYear() &&
             countDate.getMonth() === today.getMonth() &&
             countDate.getDate() === today.getDate();
    });

    const countNumber = todayStockTakes.length + 1;
    this.countNo = `COUNT-${year}${month}${day}-${String(countNumber).padStart(3, '0')}`;
  }

  initForm(): void {
    this.stockCountForm = this.fb.group({
      countDate: [this.currentDate, Validators.required],
      costCenter: ['', Validators.required],
      countedBy: ['', Validators.required],
      notes: [''],
      items: this.fb.array([])
    });
  }

  get items(): FormArray {
    return this.stockCountForm.get('items') as FormArray;
  }

  addItem(product: Product): void {
    const itemForm = this.fb.group({
      selected: [true],
      productId: [product.id],
      productName: [product.name],
      category: [product.category],
      unitSize: [product.unitSize],
      systemStock: [product.systemStock],
      countedStock: [0, [Validators.required, Validators.min(0)]],
      difference: [{ value: -product.systemStock, disabled: true }],
      notes: ['']
    });

    // Auto-calculate difference when counted stock changes
    itemForm.get('countedStock')?.valueChanges.subscribe(() => {
      this.calculateDifference(itemForm);
    });

    this.items.push(itemForm);
  }

  calculateDifference(itemForm: FormGroup): void {
    const systemStock = itemForm.get('systemStock')?.value || 0;
    const countedStock = itemForm.get('countedStock')?.value || 0;
    const difference = countedStock - systemStock;

    itemForm.get('difference')?.setValue(difference);
  }

  filterByCategory(): void {
    this.isLoading = true;

    // Get the selected cost center
    const costCenterId = this.stockCountForm.get('costCenter')?.value;

    if (!costCenterId) {
      this.snackBar.open('Please select a cost center first', 'Close', {
        duration: 3000
      });
      this.isLoading = false;
      return;
    }

    // Get stock on hand for the selected cost center
    this.stockService.getStockOnHand(costCenterId)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (stockItems) => {
          // Reset products array
          this.products = [];

          // Create a map of product IDs to fetch product details
          const productIds = new Set(stockItems.map(item => item.productId));

          // Fetch product details for each product ID
          const productRequests = Array.from(productIds).map(productId =>
            this.productService.getById(productId).pipe(
              catchError(error => {
                console.error(`Error loading product ${productId}`, error);
                return of(null);
              })
            )
          );

          forkJoin(productRequests).subscribe(products => {
            // Create a map of product ID to product details
            const productMap = new Map();
            products.forEach(product => {
              if (product) {
                productMap.set(product.id, product);
              }
            });

            // Map stock items to product view models
            stockItems.forEach(stockItem => {
              const product = productMap.get(stockItem.productId);
              if (product) {
                // Only include products in the selected category or all if "All Categories" is selected
                if (this.selectedCategory === '' ||
                    this.selectedCategory === 'All Categories' ||
                    product.departmentName === this.selectedCategory) {

                  this.products.push({
                    id: product.code,
                    name: product.name,
                    category: product.departmentName || 'Uncategorized',
                    unitSize: `${stockItem.unitName || ''}`,
                    systemStock: stockItem.quantity,
                    lastCountDate: null // We'll get this from stock take history in a real app
                  });
                }
              }
            });

            // Update filtered products
            this.filteredProducts = [...this.products];

            // Reset the items form array
            while (this.items.length !== 0) {
              this.items.removeAt(0);
            }

            // Add items for filtered products
            this.filteredProducts.forEach(product => {
              this.addItem(product);
            });
          });
        },
        error: (error) => {
          console.error('Error loading stock items', error);
          this.snackBar.open('Error loading stock items. Please try again later.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  saveStockCount(): void {
    if (this.stockCountForm.valid) {
      this.isLoading = true;

      // Get only selected items
      const selectedItems = this.items.controls
        .filter(control => control.get('selected')?.value)
        .map(control => control.getRawValue());

      if (selectedItems.length === 0) {
        this.snackBar.open('Please select at least one product', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
        return;
      }

      const formValue = this.stockCountForm.getRawValue();

      // Create stock take header
      const createStockTake: CreateStockTakeHeader = {
        costCenterId: formValue.costCenter,
        notes: formValue.notes
      };

      // Create stock take
      this.stockService.createStockTake(createStockTake)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (stockTake) => {
            // Process each selected item to update the stock take details
            const updateRequests = selectedItems.map(item => {
              // Find the stock take detail for this product
              const detail = stockTake.details?.find(d => d.productCode === item.productId);

              if (detail) {
                const updateDetail: UpdateStockTakeDetail = {
                  id: detail.id,
                  actualQuantity: item.countedStock,
                  notes: item.notes
                };

                return this.stockService.updateStockTakeDetail(detail.id, updateDetail).pipe(
                  catchError(error => {
                    console.error(`Error updating stock take detail ${detail.id}`, error);
                    return of(null);
                  })
                );
              }

              return of(null);
            });

            // Execute all update requests
            forkJoin(updateRequests).subscribe({
              next: () => {
                this.snackBar.open('Stock count saved successfully', 'Close', {
                  duration: 3000
                });

                // Reload stock takes
                this.stockService.getAllStockTakes().subscribe(stockTakes => {
                  this.stockCounts = stockTakes;
                });

                // Reset form
                this.initForm();
                this.filteredProducts = [];
                this.selectedCategory = '';
              },
              error: (error) => {
                console.error('Error updating stock take details', error);
                this.snackBar.open('Error updating stock take details. Please try again later.', 'Close', {
                  duration: 5000
                });
              }
            });
          },
          error: (error) => {
            console.error('Error creating stock take', error);
            this.snackBar.open('Error creating stock take. Please try again later.', 'Close', {
              duration: 5000
            });
          }
        });
    } else {
      this.markFormGroupTouched(this.stockCountForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  viewStockCount(count: StockTakeHeader): void {
    this.isLoading = true;

    this.stockService.getStockTakeById(count.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (stockTake) => {
          console.log('Stock take details:', stockTake);
          // In a real app, we would open a dialog or navigate to a details page
          this.snackBar.open(`Viewing stock take ${stockTake.documentNumber}`, 'Close', {
            duration: 3000
          });
        },
        error: (error) => {
          console.error('Error loading stock take details', error);
          this.snackBar.open('Error loading stock take details. Please try again later.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
