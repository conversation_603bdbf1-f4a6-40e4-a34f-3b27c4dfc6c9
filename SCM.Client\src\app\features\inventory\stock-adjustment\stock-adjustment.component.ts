import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Observable, map, startWith } from 'rxjs';

interface Product {
  id: string;
  name: string;
  unitSize: string;
  currentStock: number;
}

@Component({
  selector: 'app-stock-adjustment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule
  ],
  templateUrl: './stock-adjustment.component.html',
  styleUrls: ['./stock-adjustment.component.scss']
})
export class StockAdjustmentComponent implements OnInit {
  adjustmentForm!: FormGroup;
  adjustmentNo: string = 'ADJ-00001';
  currentDate: Date = new Date();
  
  displayedColumns: string[] = [
    'productId', 
    'productName', 
    'currentStock', 
    'adjustmentQty', 
    'newStock', 
    'reason', 
    'actions'
  ];
  
  products: Product[] = [
    { id: 'P001', name: 'Rice', unitSize: '25kg', currentStock: 120 },
    { id: 'P002', name: 'Flour', unitSize: '10kg', currentStock: 85 },
    { id: 'P003', name: 'Soft Drinks', unitSize: '24x330ml', currentStock: 45 },
    { id: 'P004', name: 'Cleaning Liquid', unitSize: '5L', currentStock: 32 },
    { id: 'P005', name: 'Light Bulbs', unitSize: '10pcs', currentStock: 15 },
    { id: 'P006', name: 'Cigarettes', unitSize: '200pcs', currentStock: 25 },
    { id: 'P007', name: 'Paper Towels', unitSize: '12 rolls', currentStock: 40 },
    { id: 'P008', name: 'Coffee', unitSize: '1kg', currentStock: 18 }
  ];
  
  filteredProducts: Observable<Product[]>[] = [];
  
  adjustmentReasons: string[] = [
    'Stock Count',
    'Damaged',
    'Expired',
    'Lost',
    'Found',
    'System Error',
    'Other'
  ];
  
  costCenters = [
    { id: 1, name: '1 - Food Store' },
    { id: 2, name: '2 - Beverage Store' },
    { id: 3, name: '3 - General Store' },
    { id: 4, name: '4 - Engineering Store' },
    { id: 5, name: '5 - S.O.E. Store' },
    { id: 6, name: '6 - Tobacco' },
    { id: 7, name: '7 - Food Main Kitchen' },
    { id: 8, name: '8 - Main Restaurant' }
  ];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.adjustmentForm = this.fb.group({
      adjustmentDate: [this.currentDate, Validators.required],
      costCenter: ['', Validators.required],
      notes: [''],
      items: this.fb.array([])
    });
    
    // Add an empty row by default
    this.addItem();
  }

  get items(): FormArray {
    return this.adjustmentForm.get('items') as FormArray;
  }

  addItem(): void {
    const itemForm = this.fb.group({
      productId: ['', Validators.required],
      productName: [''],
      currentStock: [{ value: 0, disabled: true }],
      adjustmentQty: [0, Validators.required],
      newStock: [{ value: 0, disabled: true }],
      reason: ['', Validators.required]
    });
    
    // Set up product autocomplete filtering
    const index = this.items.length;
    this.setupProductAutocomplete(itemForm, index);
    
    // Auto-calculate new stock when adjustment quantity changes
    itemForm.get('adjustmentQty')?.valueChanges.subscribe(() => {
      this.calculateNewStock(itemForm);
    });
    
    this.items.push(itemForm);
  }

  setupProductAutocomplete(itemForm: FormGroup, index: number): void {
    const productIdControl = itemForm.get('productId');
    
    this.filteredProducts[index] = productIdControl!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterProducts(value || ''))
    );
    
    productIdControl?.valueChanges.subscribe(productId => {
      const product = this.products.find(p => p.id === productId);
      if (product) {
        itemForm.patchValue({
          productName: product.name,
          currentStock: product.currentStock
        });
        this.calculateNewStock(itemForm);
      } else {
        itemForm.patchValue({
          productName: '',
          currentStock: 0
        });
      }
    });
  }

  private _filterProducts(value: string): Product[] {
    const filterValue = value.toLowerCase();
    return this.products.filter(product => 
      product.id.toLowerCase().includes(filterValue) || 
      product.name.toLowerCase().includes(filterValue)
    );
  }

  calculateNewStock(itemForm: FormGroup): void {
    const currentStock = itemForm.get('currentStock')?.value || 0;
    const adjustmentQty = itemForm.get('adjustmentQty')?.value || 0;
    const newStock = currentStock + adjustmentQty;
    
    itemForm.get('newStock')?.setValue(newStock);
  }

  removeItem(index: number): void {
    this.items.removeAt(index);
    this.filteredProducts.splice(index, 1);
  }

  displayProductFn(productId: string): string {
    if (!productId) return '';
    const product = this.products.find(p => p.id === productId);
    return product ? `${product.id} - ${product.name}` : '';
  }

  saveAdjustment(): void {
    if (this.adjustmentForm.valid) {
      console.log('Adjustment data:', this.adjustmentForm.getRawValue());
      
      this.snackBar.open('Stock adjustment saved successfully', 'Close', {
        duration: 3000
      });
      
      // Reset form after successful save
      this.initForm();
    } else {
      this.markFormGroupTouched(this.adjustmentForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
