-- Insert default roles
USE [InventoryManagement]
GO

-- Check if User role exists
IF NOT EXISTS (
    SELECT * FROM [dbo].[Role] 
    WHERE [Name] = 'User'
)
BEGIN
    -- Insert User role
    INSERT INTO [dbo].[Role] ([Name], [Description], [Permissions], [CreatedAt], [IsActive])
    VALUES ('User', 'Regular user with basic permissions', '{"canView":true,"canCreate":true,"canEdit":true,"canDelete":false}', GETDATE(), 1)
    
    PRINT 'User role added.'
END
ELSE
BEGIN
    PRINT 'User role already exists.'
END

-- Check if Admin role exists
IF NOT EXISTS (
    SELECT * FROM [dbo].[Role] 
    WHERE [Name] = 'Admin'
)
BEGIN
    -- Insert Admin role
    INSERT INTO [dbo].[Role] ([Name], [Description], [Permissions], [CreatedAt], [IsActive])
    VALUES ('Admin', 'Administrator with full permissions', '{"canView":true,"canCreate":true,"canEdit":true,"canDelete":true,"canApprove":true}', GETDATE(), 1)
    
    PRINT 'Admin role added.'
END
ELSE
BEGIN
    PRINT 'Admin role already exists.'
END
GO
