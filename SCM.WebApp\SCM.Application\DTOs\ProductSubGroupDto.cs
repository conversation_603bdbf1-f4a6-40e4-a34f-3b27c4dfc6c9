namespace SCM.Application.DTOs;

public class ProductSubGroupDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? GroupId { get; set; }
    public string? GroupName { get; set; }
    public string? DepartmentName { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateProductSubGroupDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? GroupId { get; set; }
}

public class UpdateProductSubGroupDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? GroupId { get; set; }
    public bool IsActive { get; set; }
}
