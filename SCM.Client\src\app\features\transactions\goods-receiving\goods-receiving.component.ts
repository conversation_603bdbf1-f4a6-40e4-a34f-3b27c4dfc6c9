import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCheckboxModule } from '@angular/material/checkbox';

interface PurchaseOrderItem {
  productId: string;
  productName: string;
  unitSize: string;
  orderedQty: number;
  receivedQty: number;
  unitPrice: number;
}

interface PurchaseOrder {
  id: string;
  date: Date;
  supplier: string;
  supplierName: string;
  costCenter: string;
  costCenterName: string;
  items: PurchaseOrderItem[];
}

interface GoodsReceipt {
  id: string;
  date: Date;
  poNumber: string;
  supplier: string;
  costCenter: string;
  receivedBy: string;
  status: string;
}

@Component({
  selector: 'app-goods-receiving',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatTabsModule,
    MatCheckboxModule
  ],
  templateUrl: './goods-receiving.component.html',
  styleUrls: ['./goods-receiving.component.scss']
})
export class GoodsReceivingComponent implements OnInit {
  grForm!: FormGroup;
  grNo: string = 'GR-00001';
  currentDate: Date = new Date();
  
  displayedColumns: string[] = [
    'select',
    'productId', 
    'productName', 
    'unitSize',
    'orderedQty', 
    'receivedQty', 
    'unitPrice',
    'total',
    'notes'
  ];
  
  historyColumns: string[] = [
    'id',
    'date',
    'poNumber',
    'supplier',
    'costCenter',
    'receivedBy',
    'status',
    'actions'
  ];
  
  purchaseOrders: PurchaseOrder[] = [
    {
      id: 'PO-00001',
      date: new Date(2025, 4, 5),
      supplier: '1',
      supplierName: 'ABC Supplies',
      costCenter: '1',
      costCenterName: '1 - Food Store',
      items: [
        { productId: 'P001', productName: 'Rice', unitSize: '25kg', orderedQty: 10, receivedQty: 0, unitPrice: 45.50 },
        { productId: 'P002', productName: 'Flour', unitSize: '10kg', orderedQty: 15, receivedQty: 0, unitPrice: 22.75 },
        { productId: 'P007', productName: 'Paper Towels', unitSize: '12 rolls', orderedQty: 20, receivedQty: 0, unitPrice: 24.50 }
      ]
    },
    {
      id: 'PO-00002',
      date: new Date(2025, 4, 3),
      supplier: '2',
      supplierName: 'XYZ Foods',
      costCenter: '2',
      costCenterName: '2 - Beverage Store',
      items: [
        { productId: 'P003', productName: 'Soft Drinks', unitSize: '24x330ml', orderedQty: 25, receivedQty: 0, unitPrice: 36.00 },
        { productId: 'P008', productName: 'Coffee', unitSize: '1kg', orderedQty: 10, receivedQty: 0, unitPrice: 65.00 }
      ]
    }
  ];
  
  goodsReceipts: GoodsReceipt[] = [
    { id: 'GR-00001', date: new Date(2025, 4, 6), poNumber: 'PO-00001', supplier: 'ABC Supplies', costCenter: '1 - Food Store', receivedBy: 'John Doe', status: 'Completed' },
    { id: 'GR-00002', date: new Date(2025, 4, 4), poNumber: 'PO-00002', supplier: 'XYZ Foods', costCenter: '2 - Beverage Store', receivedBy: 'Jane Smith', status: 'Completed' }
  ];
  
  selectedPO: PurchaseOrder | null = null;
  
  costCenters = [
    { id: 1, name: '1 - Food Store' },
    { id: 2, name: '2 - Beverage Store' },
    { id: 3, name: '3 - General Store' },
    { id: 4, name: '4 - Engineering Store' },
    { id: 5, name: '5 - S.O.E. Store' },
    { id: 6, name: '6 - Tobacco' }
  ];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.grForm = this.fb.group({
      grDate: [this.currentDate, Validators.required],
      poNumber: ['', Validators.required],
      supplier: [{ value: '', disabled: true }],
      costCenter: [{ value: '', disabled: true }],
      receivedBy: ['', Validators.required],
      invoiceNumber: [''],
      notes: [''],
      items: this.fb.array([])
    });
    
    // Listen for PO number changes
    this.grForm.get('poNumber')?.valueChanges.subscribe(poNumber => {
      this.loadPurchaseOrderDetails(poNumber);
    });
  }

  get items(): FormArray {
    return this.grForm.get('items') as FormArray;
  }

  loadPurchaseOrderDetails(poNumber: string): void {
    // Clear existing items
    while (this.items.length !== 0) {
      this.items.removeAt(0);
    }
    
    // Find the selected PO
    this.selectedPO = this.purchaseOrders.find(po => po.id === poNumber) || null;
    
    if (this.selectedPO) {
      // Set supplier and cost center
      this.grForm.patchValue({
        supplier: this.selectedPO.supplierName,
        costCenter: this.selectedPO.costCenterName
      });
      
      // Add items from the PO
      this.selectedPO.items.forEach(item => {
        this.addItem(item);
      });
    } else {
      this.grForm.patchValue({
        supplier: '',
        costCenter: ''
      });
    }
  }

  addItem(item: PurchaseOrderItem): void {
    const itemForm = this.fb.group({
      selected: [true],
      productId: [item.productId],
      productName: [item.productName],
      unitSize: [item.unitSize],
      orderedQty: [item.orderedQty],
      receivedQty: [item.orderedQty, [Validators.required, Validators.min(0), Validators.max(item.orderedQty)]],
      unitPrice: [item.unitPrice],
      total: [{ value: item.orderedQty * item.unitPrice, disabled: true }],
      notes: ['']
    });
    
    // Auto-calculate total when received quantity changes
    itemForm.get('receivedQty')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(itemForm);
    });
    
    this.items.push(itemForm);
  }

  calculateItemTotal(itemForm: FormGroup): void {
    const receivedQty = itemForm.get('receivedQty')?.value || 0;
    const unitPrice = itemForm.get('unitPrice')?.value || 0;
    const total = receivedQty * unitPrice;
    
    itemForm.get('total')?.setValue(total);
    this.calculateGrandTotal();
  }

  calculateGrandTotal(): number {
    let total = 0;
    for (const item of this.items.controls) {
      if (item.get('selected')?.value) {
        total += item.get('total')?.value || 0;
      }
    }
    return total;
  }

  saveGoodsReceipt(): void {
    if (this.grForm.valid) {
      // Get only selected items
      const selectedItems = this.items.controls
        .filter(control => control.get('selected')?.value)
        .map(control => control.getRawValue());
      
      const formValue = this.grForm.getRawValue();
      formValue.items = selectedItems;
      
      console.log('Goods Receipt data:', formValue);
      
      this.snackBar.open('Goods Receipt saved successfully', 'Close', {
        duration: 3000
      });
      
      // Reset form after successful save
      this.initForm();
    } else {
      this.markFormGroupTouched(this.grForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  viewGoodsReceipt(gr: GoodsReceipt): void {
    console.log('View goods receipt:', gr);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
