namespace SCM.Application.DTOs;

public class StockTakeHeaderDto
{
    public int Id { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public DateTime StockTakeDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = string.Empty;
    public int? CreatedById { get; set; }
    public string? CreatedByName { get; set; }
    public int? CompletedById { get; set; }
    public string? CompletedByName { get; set; }
    public DateTime? CompletedAt { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<StockTakeDetailDto>? Details { get; set; }
}

public class CreateStockTakeHeaderDto
{
    public int CostCenterId { get; set; }
    public DateTime StockTakeDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
}

public class UpdateStockTakeHeaderDto
{
    public int Id { get; set; }
    public int CostCenterId { get; set; }
    public DateTime StockTakeDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = "Draft";
}

public class CompleteStockTakeDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class StockTakeDetailDto
{
    public int Id { get; set; }
    public int StockTakeHeaderId { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public int? BatchId { get; set; }
    public string? BatchNumber { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal SystemQuantity { get; set; }
    public decimal CountedQuantity { get; set; }
    public decimal Variance { get; set; }
    public string? Notes { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateStockTakeDetailDto
{
    public int StockTakeHeaderId { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal SystemQuantity { get; set; }
    public decimal CountedQuantity { get; set; }
    public string? Notes { get; set; }
}

public class UpdateStockTakeDetailDto
{
    public int Id { get; set; }
    public int StockTakeHeaderId { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal SystemQuantity { get; set; }
    public decimal CountedQuantity { get; set; }
    public string? Notes { get; set; }
}
