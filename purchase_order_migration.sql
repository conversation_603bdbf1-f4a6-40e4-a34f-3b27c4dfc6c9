-- Purchase Order Migration Script
-- This script adds the Purchase Order tables to the database

USE [InventoryManagement]
GO

-- Check if PurchaseOrder table exists
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PurchaseOrder')
BEGIN
    -- Create PurchaseOrder table
    CREATE TABLE [dbo].[PurchaseOrder](
        [PurchaseOrderId] [int] IDENTITY(1,1) NOT NULL,
        [DocumentNumber] [nvarchar](50) NOT NULL,
        [SupplierId] [int] NOT NULL,
        [CostCenterId] [int] NOT NULL,
        [OrderDate] [datetime2] NOT NULL,
        [ExpectedDeliveryDate] [datetime2] NULL,
        [Status] [nvarchar](20) NOT NULL DEFAULT 'Draft', -- Draft, Pending, Approved, Rejected, Completed, Cancelled
        [Notes] [nvarchar](500) NULL,
        [TotalAmount] [decimal](18, 4) NOT NULL DEFAULT 0,
        [CreatedBy] [nvarchar](100) NULL,
        [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
        [UpdatedBy] [nvarchar](100) NULL,
        [UpdatedAt] [datetime2] NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        CONSTRAINT [PK_PurchaseOrder] PRIMARY KEY CLUSTERED ([PurchaseOrderId] ASC),
        CONSTRAINT [UQ_PurchaseOrder_DocumentNumber] UNIQUE ([DocumentNumber]),
        CONSTRAINT [FK_PurchaseOrder_Supplier] FOREIGN KEY ([SupplierId]) REFERENCES [dbo].[Supplier] ([SupplierId]) ON DELETE NO ACTION,
        CONSTRAINT [FK_PurchaseOrder_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]) ON DELETE NO ACTION
    )
    
    PRINT 'PurchaseOrder table created successfully.'
END
ELSE
BEGIN
    PRINT 'PurchaseOrder table already exists.'
END
GO

-- Check if PurchaseOrderDetail table exists
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PurchaseOrderDetail')
BEGIN
    -- Create PurchaseOrderDetail table
    CREATE TABLE [dbo].[PurchaseOrderDetail](
        [PurchaseOrderDetailId] [int] IDENTITY(1,1) NOT NULL,
        [PurchaseOrderId] [int] NOT NULL,
        [ProductId] [int] NOT NULL,
        [Quantity] [decimal](18, 4) NOT NULL,
        [UnitPrice] [decimal](18, 4) NOT NULL,
        [TotalPrice] [decimal](18, 4) NOT NULL,
        [Notes] [nvarchar](500) NULL,
        [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
        [UpdatedAt] [datetime2] NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        CONSTRAINT [PK_PurchaseOrderDetail] PRIMARY KEY CLUSTERED ([PurchaseOrderDetailId] ASC),
        CONSTRAINT [FK_PurchaseOrderDetail_PurchaseOrder] FOREIGN KEY ([PurchaseOrderId]) REFERENCES [dbo].[PurchaseOrder] ([PurchaseOrderId]) ON DELETE CASCADE,
        CONSTRAINT [FK_PurchaseOrderDetail_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]) ON DELETE NO ACTION
    )
    
    PRINT 'PurchaseOrderDetail table created successfully.'
END
ELSE
BEGIN
    PRINT 'PurchaseOrderDetail table already exists.'
END
GO

-- Insert sample data for testing
-- Check if there are any suppliers
IF EXISTS (SELECT TOP 1 * FROM [dbo].[Supplier]) AND EXISTS (SELECT TOP 1 * FROM [dbo].[CostCenter]) AND EXISTS (SELECT TOP 1 * FROM [dbo].[Product])
BEGIN
    -- Insert sample purchase order if none exists
    IF NOT EXISTS (SELECT TOP 1 * FROM [dbo].[PurchaseOrder])
    BEGIN
        DECLARE @SupplierId INT
        DECLARE @CostCenterId INT
        
        -- Get first supplier and cost center
        SELECT TOP 1 @SupplierId = SupplierId FROM [dbo].[Supplier] WHERE IsActive = 1
        SELECT TOP 1 @CostCenterId = CostCenterId FROM [dbo].[CostCenter] WHERE IsActive = 1
        
        -- Insert sample purchase order
        INSERT INTO [dbo].[PurchaseOrder] (
            [DocumentNumber], 
            [SupplierId], 
            [CostCenterId], 
            [OrderDate], 
            [ExpectedDeliveryDate], 
            [Status], 
            [Notes], 
            [TotalAmount], 
            [CreatedBy], 
            [IsActive]
        )
        VALUES (
            'PO-' + CONVERT(NVARCHAR(8), GETDATE(), 112) + '-0001',
            @SupplierId,
            @CostCenterId,
            GETDATE(),
            DATEADD(DAY, 7, GETDATE()),
            'Draft',
            'Sample purchase order for testing',
            0,
            'System',
            1
        )
        
        DECLARE @PurchaseOrderId INT
        SET @PurchaseOrderId = SCOPE_IDENTITY()
        
        -- Insert sample purchase order details
        DECLARE @ProductId INT
        DECLARE @UnitPrice DECIMAL(18, 4)
        DECLARE @Quantity DECIMAL(18, 4)
        DECLARE @TotalAmount DECIMAL(18, 4) = 0
        
        -- Get first 3 products
        DECLARE product_cursor CURSOR FOR
        SELECT TOP 3 ProductId, CostPrice FROM [dbo].[Product] WHERE IsActive = 1 ORDER BY ProductId
        
        OPEN product_cursor
        FETCH NEXT FROM product_cursor INTO @ProductId, @UnitPrice
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            SET @Quantity = ROUND(RAND() * 10 + 1, 0)
            
            INSERT INTO [dbo].[PurchaseOrderDetail] (
                [PurchaseOrderId],
                [ProductId],
                [Quantity],
                [UnitPrice],
                [TotalPrice],
                [Notes],
                [IsActive]
            )
            VALUES (
                @PurchaseOrderId,
                @ProductId,
                @Quantity,
                @UnitPrice,
                @Quantity * @UnitPrice,
                'Sample detail',
                1
            )
            
            SET @TotalAmount = @TotalAmount + (@Quantity * @UnitPrice)
            
            FETCH NEXT FROM product_cursor INTO @ProductId, @UnitPrice
        END
        
        CLOSE product_cursor
        DEALLOCATE product_cursor
        
        -- Update purchase order total amount
        UPDATE [dbo].[PurchaseOrder]
        SET [TotalAmount] = @TotalAmount
        WHERE [PurchaseOrderId] = @PurchaseOrderId
        
        PRINT 'Sample purchase order created successfully.'
    END
    ELSE
    BEGIN
        PRINT 'Sample purchase order already exists.'
    END
END
ELSE
BEGIN
    PRINT 'Cannot create sample purchase order: Missing suppliers, cost centers, or products.'
END
GO

PRINT 'Purchase order migration completed successfully.'
GO
