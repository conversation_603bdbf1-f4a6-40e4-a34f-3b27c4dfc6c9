using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class PurchaseOrdersController : ApiControllerBase
{
    private readonly IPurchaseOrderService _purchaseOrderService;

    public PurchaseOrdersController(IPurchaseOrderService purchaseOrderService)
    {
        _purchaseOrderService = purchaseOrderService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<PurchaseOrderListDto>>> GetAll()
    {
        var purchaseOrders = await _purchaseOrderService.GetAllPurchaseOrdersAsync();
        return Ok(purchaseOrders);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<PurchaseOrderDto>> GetById(int id)
    {
        var purchaseOrder = await _purchaseOrderService.GetPurchaseOrderByIdAsync(id);
        if (purchaseOrder == null)
            return NotFound();

        return Ok(purchaseOrder);
    }

    [HttpGet("supplier/{supplierId}")]
    public async Task<ActionResult<IEnumerable<PurchaseOrderListDto>>> GetBySupplier(int supplierId)
    {
        var purchaseOrders = await _purchaseOrderService.GetPurchaseOrdersBySupplierAsync(supplierId);
        return Ok(purchaseOrders);
    }

    [HttpGet("costcenter/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<PurchaseOrderListDto>>> GetByCostCenter(int costCenterId)
    {
        var purchaseOrders = await _purchaseOrderService.GetPurchaseOrdersByCostCenterAsync(costCenterId);
        return Ok(purchaseOrders);
    }

    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<PurchaseOrderListDto>>> GetByStatus(string status)
    {
        var purchaseOrders = await _purchaseOrderService.GetPurchaseOrdersByStatusAsync(status);
        return Ok(purchaseOrders);
    }

    [HttpPost]
    public async Task<ActionResult<PurchaseOrderDto>> Create(CreatePurchaseOrderDto createPurchaseOrderDto)
    {
        try
        {
            var purchaseOrder = await _purchaseOrderService.CreatePurchaseOrderAsync(createPurchaseOrderDto);
            return CreatedAtAction(nameof(GetById), new { id = purchaseOrder.Id }, purchaseOrder);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdatePurchaseOrderDto updatePurchaseOrderDto)
    {
        if (id != updatePurchaseOrderDto.Id)
            return BadRequest("ID mismatch");

        try
        {
            await _purchaseOrderService.UpdatePurchaseOrderAsync(updatePurchaseOrderDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _purchaseOrderService.DeletePurchaseOrderAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPut("{id}/approve")]
    public async Task<IActionResult> Approve(int id)
    {
        try
        {
            await _purchaseOrderService.ApprovePurchaseOrderAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPut("{id}/reject")]
    public async Task<IActionResult> Reject(int id, [FromBody] RejectReasonDto rejectReasonDto)
    {
        try
        {
            await _purchaseOrderService.RejectPurchaseOrderAsync(id, rejectReasonDto.Reason);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPut("{id}/cancel")]
    public async Task<IActionResult> Cancel(int id, [FromBody] CancelReasonDto cancelReasonDto)
    {
        try
        {
            await _purchaseOrderService.CancelPurchaseOrderAsync(id, cancelReasonDto.Reason);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("details")]
    public async Task<ActionResult<PurchaseOrderDetailDto>> AddDetail(CreatePurchaseOrderDetailDto createDetailDto)
    {
        try
        {
            var detail = await _purchaseOrderService.AddPurchaseOrderDetailAsync(createDetailDto);
            return Ok(detail);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("details/{id}")]
    public async Task<IActionResult> UpdateDetail(int id, UpdatePurchaseOrderDetailDto updateDetailDto)
    {
        if (id != updateDetailDto.Id)
            return BadRequest("ID mismatch");

        try
        {
            await _purchaseOrderService.UpdatePurchaseOrderDetailAsync(updateDetailDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("details/{id}")]
    public async Task<IActionResult> DeleteDetail(int id)
    {
        try
        {
            await _purchaseOrderService.DeletePurchaseOrderDetailAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}

public class RejectReasonDto
{
    public string Reason { get; set; } = string.Empty;
}

public class CancelReasonDto
{
    public string Reason { get; set; } = string.Empty;
}
