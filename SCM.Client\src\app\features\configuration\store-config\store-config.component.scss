.page-container {
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.location-label {
  font-weight: bold;
  color: #1976d2;
}

.branch-details-header {
  background-color: #f5f5f5;
  padding: 10px;
  margin-bottom: 20px;
  text-align: center;
  border-radius: 4px;
}

.branch-details-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.store-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-container {
  display: flex;
  gap: 30px;
}

.form-section {
  flex: 1;
}

.form-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1976d2;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.form-row {
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.image-upload-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.image-placeholder {
  width: 150px;
  height: 150px;
  border: 2px dashed #ccc;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.image-placeholder:hover {
  border-color: #1976d2;
  background-color: rgba(25, 118, 210, 0.05);
}

.image-placeholder span {
  margin-bottom: 8px;
  color: #757575;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

// Responsive adjustments
@media (max-width: 768px) {
  .form-container {
    flex-direction: column;
  }
}
