using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class PaymentDetail : BaseEntity
{
    public int TransactionHeaderId { get; set; }
    public int PaymentMethodId { get; set; }
    public decimal Amount { get; set; }
    public string? ReferenceNumber { get; set; }
    public DateTime PaymentDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual TransactionHeader TransactionHeader { get; set; } = null!;
    public virtual PaymentMethod PaymentMethod { get; set; } = null!;
}
