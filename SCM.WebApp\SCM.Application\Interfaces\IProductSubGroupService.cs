using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IProductSubGroupService
{
    Task<IEnumerable<ProductSubGroupDto>> GetAllProductSubGroupsAsync();
    Task<ProductSubGroupDto?> GetProductSubGroupByIdAsync(int id);
    Task<IEnumerable<ProductSubGroupDto>> GetProductSubGroupsByGroupIdAsync(int groupId);
    Task<ProductSubGroupDto> CreateProductSubGroupAsync(CreateProductSubGroupDto createProductSubGroupDto);
    Task UpdateProductSubGroupAsync(UpdateProductSubGroupDto updateProductSubGroupDto);
    Task DeleteProductSubGroupAsync(int id);
}
