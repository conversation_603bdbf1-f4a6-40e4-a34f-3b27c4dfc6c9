using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IStockService
{
    Task<IEnumerable<StockOnHandDto>> GetStockOnHandAsync();
    Task<IEnumerable<StockOnHandDto>> GetStockOnHandByProductIdAsync(int productId);
    Task<IEnumerable<StockOnHandDto>> GetStockOnHandByCostCenterIdAsync(int costCenterId);
    Task<StockOnHandDto?> GetStockOnHandAsync(int productId, int costCenterId, int batchId);
    Task<IEnumerable<StockOnHandSummaryDto>> GetStockSummaryByCostCenterIdAsync(int costCenterId);
    Task<IEnumerable<StockOnHandSummaryDto>> GetLowStockItemsAsync(int? costCenterId = null);
    Task<IEnumerable<StockOnHandSummaryDto>> GetOverStockItemsAsync(int? costCenterId = null);
    Task<IEnumerable<StockOnHandSummaryDto>> GetReorderItemsAsync(int? costCenterId = null);
    Task<IEnumerable<StockOnHandDto>> GetExpiringStockAsync(int daysToExpiry, int? costCenterId = null);
    Task AdjustStockAsync(StockAdjustmentDto stockAdjustmentDto);
}
