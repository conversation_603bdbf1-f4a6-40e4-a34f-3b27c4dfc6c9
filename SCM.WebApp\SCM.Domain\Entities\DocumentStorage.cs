using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class DocumentStorage : BaseEntity
{
    public string FileName { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FilePath { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public int EntityId { get; set; }
    public string? Description { get; set; }
    public int UploadedById { get; set; }
    
    // Navigation properties
    public virtual User UploadedBy { get; set; } = null!;
}
