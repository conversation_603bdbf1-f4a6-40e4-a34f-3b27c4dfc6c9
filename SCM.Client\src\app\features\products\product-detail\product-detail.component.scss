.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.product-form {
  max-width: 1200px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-row mat-form-field {
  flex: 1;
  min-width: 200px;
}

.checkbox-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.checkbox-row mat-checkbox {
  margin-bottom: 8px;
}

.full-width {
  width: 100%;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    margin-top: 10px;
  }

  .form-row {
    flex-direction: column;
  }

  .form-row mat-form-field {
    width: 100%;
  }

  .checkbox-row {
    flex-direction: column;
    gap: 8px;
  }
}
