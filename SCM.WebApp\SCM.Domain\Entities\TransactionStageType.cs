using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionStageType : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int TransactionProcessId { get; set; }
    public int Sequence { get; set; }
    public bool AffectsStock { get; set; } = false;
    public bool IsFinalStage { get; set; } = false;
    public bool RequiresApproval { get; set; } = false;
    
    // Navigation properties
    public virtual TransactionProcess TransactionProcess { get; set; } = null!;
    public virtual ICollection<TransactionStage> TransactionStages { get; set; } = new List<TransactionStage>();
}
