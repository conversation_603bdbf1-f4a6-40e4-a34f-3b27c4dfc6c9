using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IBatchService
{
    Task<IEnumerable<BatchDto>> GetAllBatchesAsync();
    Task<BatchDto?> GetBatchByIdAsync(int id);
    Task<BatchDto?> GetBatchByBatchNumberAsync(string batchNumber);
    Task<IEnumerable<BatchDto>> GetBatchesByProductIdAsync(int productId);
    Task<IEnumerable<BatchDto>> GetActiveBatchesByProductIdAsync(int productId);
    Task<IEnumerable<BatchDto>> GetExpiringBatchesAsync(int daysToExpiry);
    Task<BatchDto> CreateBatchAsync(CreateBatchDto createBatchDto);
    Task UpdateBatchAsync(UpdateBatchDto updateBatchDto);
    Task DeleteBatchAsync(int id);
}
