using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class User : BaseEntity
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string? LastName { get; set; }
    public string? Phone { get; set; }
    public int? RoleId { get; set; }
    public bool IsAdmin { get; set; } = false;
    public DateTime? LastLogin { get; set; }
    public bool MustChangePassword { get; set; } = false;
    public bool IsLocked { get; set; } = false;
    public int FailedLoginAttempts { get; set; } = 0;
    
    // Navigation properties
    public virtual Role? Role { get; set; }
    public virtual ICollection<UserCostCenterAccess> CostCenterAccesses { get; set; } = new List<UserCostCenterAccess>();
    public virtual ICollection<TransactionHeader> CreatedTransactions { get; set; } = new List<TransactionHeader>();
    public virtual ICollection<TransactionHeader> ApprovedTransactions { get; set; } = new List<TransactionHeader>();
    public virtual ICollection<TransactionHeader> CompletedTransactions { get; set; } = new List<TransactionHeader>();
    public virtual ICollection<TransactionStage> CompletedStages { get; set; } = new List<TransactionStage>();
    public virtual ICollection<StockTakeHeader> CreatedStockTakes { get; set; } = new List<StockTakeHeader>();
    public virtual ICollection<StockTakeHeader> CompletedStockTakes { get; set; } = new List<StockTakeHeader>();
    public virtual ICollection<UserPreference> Preferences { get; set; } = new List<UserPreference>();
    public virtual ICollection<Notification> Notifications { get; set; } = new List<Notification>();
    public virtual ICollection<ApiKey> ApiKeys { get; set; } = new List<ApiKey>();
    public virtual ICollection<DocumentStorage> UploadedDocuments { get; set; } = new List<DocumentStorage>();
    public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
}
