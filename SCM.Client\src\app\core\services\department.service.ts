import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Department, CreateDepartment, UpdateDepartment } from '../models/department.model';

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly path = 'departments';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<Department[]> {
    return this.apiService.get<Department[]>(this.path);
  }

  getById(id: number): Observable<Department> {
    return this.apiService.get<Department>(`${this.path}/${id}`);
  }

  create(department: CreateDepartment): Observable<Department> {
    return this.apiService.post<Department, CreateDepartment>(this.path, department);
  }

  update(id: number, department: UpdateDepartment): Observable<void> {
    return this.apiService.put<void, UpdateDepartment>(`${this.path}/${id}`, department);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
