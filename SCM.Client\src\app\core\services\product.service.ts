import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Product, CreateProduct, UpdateProduct } from '../models/product.model';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private readonly path = 'products';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<Product[]> {
    return this.apiService.get<Product[]>(this.path);
  }

  getById(id: number): Observable<Product> {
    return this.apiService.get<Product>(`${this.path}/${id}`);
  }

  getByCode(code: string): Observable<Product> {
    return this.apiService.get<Product>(`${this.path}/code/${code}`);
  }

  getByDepartmentId(departmentId: number): Observable<Product[]> {
    return this.apiService.get<Product[]>(`${this.path}/department/${departmentId}`);
  }

  getByGroupId(groupId: number): Observable<Product[]> {
    return this.apiService.get<Product[]>(`${this.path}/group/${groupId}`);
  }

  getBySubGroupId(subGroupId: number): Observable<Product[]> {
    return this.apiService.get<Product[]>(`${this.path}/subgroup/${subGroupId}`);
  }

  search(term: string): Observable<Product[]> {
    const params = new HttpParams().set('term', term);
    return this.apiService.get<Product[]>(`${this.path}/search`, params);
  }

  create(product: CreateProduct): Observable<Product> {
    return this.apiService.post<Product, CreateProduct>(this.path, product);
  }

  update(id: number, product: UpdateProduct): Observable<void> {
    return this.apiService.put<void, UpdateProduct>(`${this.path}/${id}`, product);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
