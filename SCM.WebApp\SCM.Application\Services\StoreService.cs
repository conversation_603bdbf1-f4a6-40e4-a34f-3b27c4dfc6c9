using Mapster;
using MapsterMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SCM.Application.Services
{
    public class StoreService : IStoreService
    {
        private readonly IRepository<Store> _storeRepository;
        private readonly ApplicationDbContext _dbContext;
        private readonly IMapper _mapper;

        public StoreService(IRepository<Store> storeRepository, ApplicationDbContext dbContext, IMapper mapper)
        {
            _storeRepository = storeRepository ?? throw new ArgumentNullException(nameof(storeRepository));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public async Task<IEnumerable<StoreDto>> GetAllStoresAsync()
        {
            var stores = await _storeRepository.GetAllAsync();
            return stores.Adapt<IEnumerable<StoreDto>>();
        }

        public async Task<StoreDto?> GetStoreByIdAsync(int id)
        {
            var store = await _storeRepository.GetByIdAsync(id);
            return store?.Adapt<StoreDto>();
        }

        public async Task<StoreDto?> GetStoreWithLocationsAsync(int id)
        {
            var store = await _dbContext.Stores
                .Include(s => s.Locations)
                .FirstOrDefaultAsync(s => s.Id == id);

            return store?.Adapt<StoreDto>();
        }

        public async Task<StoreDto?> GetStoreWithCostCentersAsync(int id)
        {
            var store = await _dbContext.Stores
                .Include(s => s.CostCenters)
                .FirstOrDefaultAsync(s => s.Id == id);

            return store?.Adapt<StoreDto>();
        }

        public async Task<StoreDto> CreateStoreAsync(CreateStoreDto createStoreDto)
        {
            var store = createStoreDto.Adapt<Store>();
            store.CreatedAt = DateTime.UtcNow;
            store.IsActive = true;

            await _storeRepository.AddAsync(store);

            return store.Adapt<StoreDto>();
        }

        public async Task UpdateStoreAsync(UpdateStoreDto updateStoreDto)
        {
            var store = await _storeRepository.GetByIdAsync(updateStoreDto.Id);
            if (store == null)
                throw new KeyNotFoundException($"Store with ID {updateStoreDto.Id} not found.");

            updateStoreDto.Adapt(store);
            store.UpdatedAt = DateTime.UtcNow;

            await _storeRepository.UpdateAsync(store);
        }

        public async Task DeleteStoreAsync(int id)
        {
            var store = await _storeRepository.GetByIdAsync(id);
            if (store == null)
                throw new KeyNotFoundException($"Store with ID {id} not found.");

            await _storeRepository.DeleteAsync(store);
        }
    }
}
