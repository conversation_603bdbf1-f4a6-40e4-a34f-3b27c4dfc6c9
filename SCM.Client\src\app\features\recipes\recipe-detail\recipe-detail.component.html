<div class="page-container">
  <div class="page-header">
    <h1>{{ isEditMode ? 'Edit Recipe' : 'Add Recipe' }}</h1>
    <div class="header-actions">
      <button mat-button (click)="cancel()">Cancel</button>
      <button mat-raised-button color="primary" (click)="saveRecipe()">Save</button>
    </div>
  </div>
  
  <form [formGroup]="recipeForm" class="recipe-form">
    <mat-tab-group>
      <mat-tab label="Basic Information">
        <div class="tab-content">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Recipe ID</mat-label>
              <input matInput formControlName="id">
              <mat-error *ngIf="recipeForm.get('id')?.hasError('required')">
                Recipe ID is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Recipe Name</mat-label>
              <input matInput formControlName="name">
              <mat-error *ngIf="recipeForm.get('name')?.hasError('required')">
                Recipe Name is required
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Category</mat-label>
              <mat-select formControlName="category">
                <mat-option *ngFor="let category of categories" [value]="category">
                  {{category}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="recipeForm.get('category')?.hasError('required')">
                Category is required
              </mat-error>
            </mat-form-field>
            
            <div class="yield-container">
              <mat-form-field appearance="outline">
                <mat-label>Yield</mat-label>
                <input matInput type="number" formControlName="yield">
                <mat-error *ngIf="recipeForm.get('yield')?.hasError('required')">
                  Yield is required
                </mat-error>
                <mat-error *ngIf="recipeForm.get('yield')?.hasError('min')">
                  Yield must be greater than 0
                </mat-error>
              </mat-form-field>
              
              <mat-form-field appearance="outline">
                <mat-label>Unit</mat-label>
                <mat-select formControlName="yieldUnit">
                  <mat-option *ngFor="let unit of yieldUnits" [value]="unit">
                    {{unit}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="recipeForm.get('yieldUnit')?.hasError('required')">
                  Unit is required
                </mat-error>
              </mat-form-field>
            </div>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" rows="3"></textarea>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-checkbox formControlName="isActive">Active</mat-checkbox>
          </div>
        </div>
      </mat-tab>
      
      <mat-tab label="Ingredients">
        <div class="tab-content">
          <div class="table-container mat-elevation-z2">
            <table mat-table [dataSource]="ingredients_.controls">
              <!-- Ingredient Column -->
              <ng-container matColumnDef="ingredient">
                <th mat-header-cell *matHeaderCellDef>Ingredient</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('ingredient')" [matAutocomplete]="auto">
                    <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayIngredientFn">
                      <mat-option *ngFor="let ingredient of filteredIngredients[i] | async" [value]="ingredient.id">
                        {{ingredient.id}} - {{ingredient.name}} ({{ingredient.unitSize}})
                      </mat-option>
                    </mat-autocomplete>
                    <mat-error *ngIf="item.get('ingredient')?.hasError('required')">
                      Ingredient is required
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('quantity')">
                    <mat-error *ngIf="item.get('quantity')?.hasError('required')">
                      Required
                    </mat-error>
                    <mat-error *ngIf="item.get('quantity')?.hasError('min')">
                      Min 0.01
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Unit Column -->
              <ng-container matColumnDef="unit">
                <th mat-header-cell *matHeaderCellDef>Unit</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <mat-select [formControl]="item.get('unit')">
                      <mat-option *ngFor="let unit of units" [value]="unit">
                        {{unit}}
                      </mat-option>
                    </mat-select>
                    <mat-error *ngIf="item.get('unit')?.hasError('required')">
                      Required
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Unit Cost Column -->
              <ng-container matColumnDef="unitCost">
                <th mat-header-cell *matHeaderCellDef>Unit Cost</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('unitCost')" readonly>
                    <span matTextPrefix>$&nbsp;</span>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Total Cost Column -->
              <ng-container matColumnDef="totalCost">
                <th mat-header-cell *matHeaderCellDef>Total Cost</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('totalCost')" readonly>
                    <span matTextPrefix>$&nbsp;</span>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <button mat-icon-button color="warn" (click)="removeIngredient(i)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
            
            <div class="table-actions">
              <button mat-button color="primary" (click)="addIngredient()">
                <mat-icon>add</mat-icon> Add Ingredient
              </button>
            </div>
          </div>
          
          <div class="cost-summary">
            <div class="cost-row">
              <span>Total Cost:</span>
              <span class="cost-value">{{calculateTotalCost() | currency}}</span>
            </div>
            <div class="cost-row">
              <span>Cost Per {{recipeForm.get('yieldUnit')?.value}}:</span>
              <span class="cost-value">{{calculateCostPerUnit() | currency}}</span>
            </div>
          </div>
        </div>
      </mat-tab>
      
      <mat-tab label="Instructions">
        <div class="tab-content">
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Preparation Instructions</mat-label>
              <textarea matInput formControlName="instructions" rows="15"></textarea>
            </mat-form-field>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </form>
</div>
