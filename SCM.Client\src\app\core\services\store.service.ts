import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Store, CreateStore, UpdateStore } from '../models/cost-center.model';

@Injectable({
  providedIn: 'root'
})
export class StoreService {
  private readonly path = 'stores';

  constructor(private apiService: ApiService) { }

  getAllStores(): Observable<Store[]> {
    return this.apiService.get<Store[]>(this.path);
  }

  getStoreById(id: number): Observable<Store> {
    return this.apiService.get<Store>(`${this.path}/${id}`);
  }

  createStore(store: CreateStore): Observable<Store> {
    return this.apiService.post<Store, CreateStore>(this.path, store);
  }

  updateStore(id: number, store: UpdateStore): Observable<void> {
    return this.apiService.put<void, UpdateStore>(`${this.path}/${id}`, store);
  }

  deleteStore(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
