using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class UnitsController : ApiControllerBase
{
    private readonly IUnitService _unitService;

    public UnitsController(IUnitService unitService)
    {
        _unitService = unitService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<UnitDto>>> GetAll()
    {
        var units = await _unitService.GetAllUnitsAsync();
        return Ok(units);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<UnitDto>> GetById(int id)
    {
        var unit = await _unitService.GetUnitByIdAsync(id);
        if (unit == null)
            return NotFound();

        return Ok(unit);
    }

    [HttpGet("by-unit-group/{unitGroupId}")]
    public async Task<ActionResult<IEnumerable<UnitDto>>> GetByUnitGroupId(int unitGroupId)
    {
        var units = await _unitService.GetUnitsByUnitGroupIdAsync(unitGroupId);
        return Ok(units);
    }

    [HttpPost]
    public async Task<ActionResult<UnitDto>> Create(CreateUnitDto createUnitDto)
    {
        var unit = await _unitService.CreateUnitAsync(createUnitDto);
        return CreatedAtAction(nameof(GetById), new { id = unit.Id }, unit);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateUnitDto updateUnitDto)
    {
        if (id != updateUnitDto.Id)
            return BadRequest();

        try
        {
            await _unitService.UpdateUnitAsync(updateUnitDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _unitService.DeleteUnitAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
