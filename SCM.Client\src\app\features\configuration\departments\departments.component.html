<div class="departments-container">
  <h1>Department Management</h1>
  
  <div class="departments-content">
    <!-- Department Form -->
    <mat-card class="form-card">
      <mat-card-header>
        <mat-card-title>{{ isEditing ? 'Edit Department' : 'Add Department' }}</mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <form [formGroup]="departmentForm" (ngSubmit)="onSubmit()">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Department Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter department name">
            <mat-error *ngIf="departmentForm.get('name')?.hasError('required')">
              Department name is required
            </mat-error>
            <mat-error *ngIf="departmentForm.get('name')?.hasError('maxlength')">
              Department name cannot exceed 100 characters
            </mat-error>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Description</mat-label>
            <textarea matInput formControlName="description" placeholder="Enter description" rows="3"></textarea>
            <mat-error *ngIf="departmentForm.get('description')?.hasError('maxlength')">
              Description cannot exceed 500 characters
            </mat-error>
          </mat-form-field>
          
          <div class="form-field-checkbox">
            <label>Status:</label>
            <mat-checkbox formControlName="isActive">Active</mat-checkbox>
          </div>
          
          <div class="form-actions">
            <button mat-raised-button color="primary" type="submit" [disabled]="departmentForm.invalid">
              {{ isEditing ? 'Update' : 'Save' }}
            </button>
            <button mat-button type="button" (click)="resetForm()">
              Cancel
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
    
    <!-- Departments Table -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>Departments</mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <div class="spinner-container" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
        
        <table mat-table [dataSource]="departments" class="full-width" matSort>
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
            <td mat-cell *matCellDef="let department">{{ department.id }}</td>
          </ng-container>
          
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let department">{{ department.name }}</td>
          </ng-container>
          
          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef>Description</th>
            <td mat-cell *matCellDef="let department">{{ department.description }}</td>
          </ng-container>
          
          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let department">
              <span [ngClass]="department.isActive ? 'status-active' : 'status-inactive'">
                {{ department.isActive ? 'Active' : 'Inactive' }}
              </span>
            </td>
          </ng-container>
          
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let department">
              <button mat-icon-button color="primary" (click)="editDepartment(department)" 
                      [disabled]="!canEdit()" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteDepartment(department.id)" 
                      [disabled]="!canDelete()" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>
          
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
        
        <div class="no-data-message" *ngIf="departments.length === 0 && !isLoading">
          No departments found. Please add a department.
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
