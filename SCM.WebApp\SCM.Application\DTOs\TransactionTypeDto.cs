namespace SCM.Application.DTOs;

public class TransactionTypeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Direction { get; set; } = string.Empty;
    public bool AffectsStock { get; set; }
    public bool RequiresApproval { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<TransactionProcessDto>? Processes { get; set; }
}

public class CreateTransactionTypeDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Direction { get; set; } = "In"; // In, Out, Transfer
    public bool AffectsStock { get; set; } = true;
    public bool RequiresApproval { get; set; } = false;
}

public class UpdateTransactionTypeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Direction { get; set; } = "In"; // In, Out, Transfer
    public bool AffectsStock { get; set; }
    public bool RequiresApproval { get; set; }
    public bool IsActive { get; set; }
}
