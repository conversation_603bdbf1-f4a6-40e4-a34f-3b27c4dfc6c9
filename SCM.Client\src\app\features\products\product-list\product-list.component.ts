import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProductService } from '../../../core/services/product.service';
import { StockService } from '../../../core/services/stock.service';
import { DepartmentService } from '../../../core/services/department.service';
import { Product } from '../../../core/models/product.model';
import { forkJoin, finalize, of, catchError } from 'rxjs';

interface ProductViewModel {
  id: string;
  name: string;
  category: string;
  unitSize: string;
  unitPrice: number;
  stockOnHand: number;
  reorderLevel: number;
  status: string;
}

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss']
})
export class ProductListComponent implements OnInit {
  displayedColumns: string[] = ['id', 'name', 'category', 'unitSize', 'unitPrice', 'stockOnHand', 'reorderLevel', 'status', 'actions'];
  products: ProductViewModel[] = [];
  filteredProducts: ProductViewModel[] = [];
  searchTerm: string = '';
  selectedCategory: string = '';
  isLoading: boolean = false;

  categories: string[] = [
    'Food',
    'Beverage',
    'Cleaning Supplies',
    'Office Supplies',
    'Engineering',
    'Tobacco'
  ];

  constructor(
    private productService: ProductService,
    private stockService: StockService,
    private departmentService: DepartmentService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadProducts();
  }

  loadProducts(): void {
    this.isLoading = true;

    // Load departments to populate categories dropdown
    this.departmentService.getAll()
      .pipe(catchError(error => {
        console.error('Error loading departments', error);
        return of([]);
      }))
      .subscribe(departments => {
        this.categories = departments.map(d => d.name);
      });

    // Get all products
    this.productService.getAll()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (products) => {
          // Create a map to store stock information for each product
          const productStockMap = new Map<number, number>();

          // Create an array of observables for stock requests
          const stockRequests = products.map(product =>
            this.stockService.getProductStock(product.id).pipe(
              catchError(error => {
                console.error(`Error loading stock for product ${product.id}`, error);
                return of([]);
              })
            )
          );

          // Execute all stock requests in parallel
          forkJoin(stockRequests).subscribe(stockResults => {
            // Process stock results
            stockResults.forEach((stockItems, index) => {
              const productId = products[index].id;
              const totalStock = stockItems.reduce((sum, item) => sum + item.quantity, 0);
              productStockMap.set(productId, totalStock);
            });

            // Map products to view model with stock information
            this.products = products.map(p => this.mapToViewModel(p, productStockMap.get(p.id) || 0));
            this.filteredProducts = [...this.products];
          });
        },
        error: (error) => {
          console.error('Error loading products', error);
          this.snackBar.open('Error loading products. Please try again later.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  mapToViewModel(product: Product, stockOnHand: number = 0): ProductViewModel {
    // Determine status based on stock level
    let status = 'Active';
    if (!product.isActive) {
      status = 'Inactive';
    } else if (product.minStock && product.reorderPoint) {
      if (stockOnHand <= product.minStock) {
        status = 'Low Stock';
      }
    }

    return {
      id: product.code,
      name: product.name,
      category: product.departmentName || 'Uncategorized',
      unitSize: product.unitName || '',
      unitPrice: product.salesPrice || 0,
      stockOnHand: stockOnHand,
      reorderLevel: product.reorderPoint || 0,
      status: status
    };
  }

  applyFilter(): void {
    this.filteredProducts = this.products.filter(product => {
      const matchesSearch = this.searchTerm === '' ||
        product.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        product.id.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory = this.selectedCategory === '' ||
        product.category === this.selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedCategory = '';
    this.filteredProducts = [...this.products];
  }

  addProduct(): void {
    this.router.navigate(['/products/new']);
  }

  editProduct(product: ProductViewModel): void {
    this.router.navigate([`/products/${product.id}`]);
  }

  deleteProduct(product: ProductViewModel): void {
    if (confirm(`Are you sure you want to delete ${product.name}?`)) {
      // Find the product in the backend by code
      this.productService.getByCode(product.id)
        .subscribe({
          next: (apiProduct) => {
            this.productService.delete(apiProduct.id)
              .subscribe({
                next: () => {
                  this.snackBar.open('Product deleted successfully', 'Close', {
                    duration: 3000
                  });
                  this.loadProducts(); // Reload the products
                },
                error: (error) => {
                  console.error('Error deleting product', error);
                  this.snackBar.open('Error deleting product. Please try again later.', 'Close', {
                    duration: 5000
                  });
                }
              });
          },
          error: (error) => {
            console.error('Error finding product', error);
            this.snackBar.open('Error finding product. Please try again later.', 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  viewProductDetails(product: ProductViewModel): void {
    this.router.navigate([`/products/${product.id}`]);
  }
}
