using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Location : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? StoreId { get; set; }
    
    // Navigation properties
    public virtual Store? Store { get; set; }
    public virtual ICollection<CostCenter> CostCenters { get; set; } = new List<CostCenter>();
}
