import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  CostCenter, 
  CreateCostCenter, 
  UpdateCostCenter,
  CostCenterType,
  CreateCostCenterType,
  UpdateCostCenterType
} from '../models/cost-center.model';

@Injectable({
  providedIn: 'root'
})
export class CostCenterService {
  private readonly path = 'costcenters';
  private readonly typePath = 'costcentertypes';

  constructor(private apiService: ApiService) { }

  // Cost Centers
  getAllCostCenters(): Observable<CostCenter[]> {
    return this.apiService.get<CostCenter[]>(this.path);
  }

  getCostCenterById(id: number): Observable<CostCenter> {
    return this.apiService.get<CostCenter>(`${this.path}/${id}`);
  }

  getCostCentersByStoreId(storeId: number): Observable<CostCenter[]> {
    return this.apiService.get<CostCenter[]>(`${this.path}/store/${storeId}`);
  }

  getCostCentersByTypeId(typeId: number): Observable<CostCenter[]> {
    return this.apiService.get<CostCenter[]>(`${this.path}/type/${typeId}`);
  }

  createCostCenter(costCenter: CreateCostCenter): Observable<CostCenter> {
    return this.apiService.post<CostCenter, CreateCostCenter>(this.path, costCenter);
  }

  updateCostCenter(id: number, costCenter: UpdateCostCenter): Observable<void> {
    return this.apiService.put<void, UpdateCostCenter>(`${this.path}/${id}`, costCenter);
  }

  deleteCostCenter(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  // Cost Center Types
  getAllCostCenterTypes(): Observable<CostCenterType[]> {
    return this.apiService.get<CostCenterType[]>(this.typePath);
  }

  getCostCenterTypeById(id: number): Observable<CostCenterType> {
    return this.apiService.get<CostCenterType>(`${this.typePath}/${id}`);
  }

  createCostCenterType(costCenterType: CreateCostCenterType): Observable<CostCenterType> {
    return this.apiService.post<CostCenterType, CreateCostCenterType>(this.typePath, costCenterType);
  }

  updateCostCenterType(id: number, costCenterType: UpdateCostCenterType): Observable<void> {
    return this.apiService.put<void, UpdateCostCenterType>(`${this.typePath}/${id}`, costCenterType);
  }

  deleteCostCenterType(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.typePath}/${id}`);
  }
}
