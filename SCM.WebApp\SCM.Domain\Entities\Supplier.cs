using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Supplier : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? ContactPerson { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public string? PostalCode { get; set; }
    public string? TaxNumber { get; set; }
    public string? Notes { get; set; }
    public string? BankAccount { get; set; }
    public string? BankName { get; set; }
    
    // Navigation properties
    public virtual ICollection<TransactionHeader> Transactions { get; set; } = new List<TransactionHeader>();
}
