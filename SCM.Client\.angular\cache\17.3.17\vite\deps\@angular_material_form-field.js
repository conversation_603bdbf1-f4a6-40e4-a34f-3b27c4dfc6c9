import {
  MAT_ERROR,
  MAT_FORM_FIELD,
  MAT_FORM_FIELD_DEFAULT_OPTIONS,
  MAT_PREFIX,
  MAT_SUFFIX,
  Mat<PERSON>rror,
  MatFormField,
  MatFormFieldControl,
  MatFormFieldModule,
  MatHint,
  Mat<PERSON><PERSON><PERSON>,
  MatPrefix,
  MatSuffix,
  getMatFormFieldDuplicatedHintError,
  getMatFormFieldMissingControlError,
  getMatFormFieldPlaceholderConflictError,
  matFormFieldAnimations
} from "./chunk-KMQJ76RC.js";
import "./chunk-26JACEMX.js";
import "./chunk-26U3ZGYY.js";
import "./chunk-XTHKPVIX.js";
import "./chunk-APQJ6POP.js";
import "./chunk-IGJZNA3K.js";
import "./chunk-CONQKHOI.js";
import "./chunk-V4GYEGQC.js";
import "./chunk-GC5FLHL6.js";
export {
  MAT_ERROR,
  MAT_FORM_FIELD,
  MAT_FORM_FIELD_DEFAULT_OPTIONS,
  MAT_PREFIX,
  MAT_SUFFIX,
  MatError,
  MatFormField,
  MatFormFieldControl,
  MatFormFieldModule,
  MatHint,
  MatLabel,
  MatPrefix,
  MatSuffix,
  getMatFormFieldDuplicatedHintError,
  getMatFormFieldMissingControlError,
  getMatFormFieldPlaceholderConflictError,
  matFormFieldAnimations
};
//# sourceMappingURL=@angular_material_form-field.js.map
