import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

interface User {
  id: number;
  username: string;
  fullName: string;
  email: string;
  role: string;
  department: string;
  lastLogin: Date | null;
  status: string;
}

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatChipsModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent implements OnInit {
  displayedColumns: string[] = ['id', 'username', 'fullName', 'email', 'role', 'department', 'lastLogin', 'status', 'actions'];
  users: User[] = [];
  filteredUsers: User[] = [];
  searchTerm: string = '';
  selectedRole: string = '';
  selectedStatus: string = '';
  
  roles: string[] = [
    'Administrator',
    'Manager',
    'Supervisor',
    'Inventory Clerk',
    'Purchasing Officer',
    'Sales Representative',
    'Kitchen Staff',
    'Viewer'
  ];
  
  statuses: string[] = [
    'Active',
    'Inactive',
    'Locked'
  ];
  
  departments: string[] = [
    'Management',
    'Finance',
    'Purchasing',
    'Inventory',
    'Sales',
    'Kitchen',
    'IT'
  ];

  constructor() {
    // Mock data for demonstration
    this.users = [
      {
        id: 1,
        username: 'admin',
        fullName: 'System Administrator',
        email: '<EMAIL>',
        role: 'Administrator',
        department: 'IT',
        lastLogin: new Date(2025, 4, 10),
        status: 'Active'
      },
      {
        id: 2,
        username: 'jsmith',
        fullName: 'John Smith',
        email: '<EMAIL>',
        role: 'Manager',
        department: 'Management',
        lastLogin: new Date(2025, 4, 9),
        status: 'Active'
      },
      {
        id: 3,
        username: 'mjohnson',
        fullName: 'Mary Johnson',
        email: '<EMAIL>',
        role: 'Inventory Clerk',
        department: 'Inventory',
        lastLogin: new Date(2025, 4, 8),
        status: 'Active'
      },
      {
        id: 4,
        username: 'rwilliams',
        fullName: 'Robert Williams',
        email: '<EMAIL>',
        role: 'Purchasing Officer',
        department: 'Purchasing',
        lastLogin: new Date(2025, 4, 7),
        status: 'Active'
      },
      {
        id: 5,
        username: 'jbrown',
        fullName: 'Jennifer Brown',
        email: '<EMAIL>',
        role: 'Sales Representative',
        department: 'Sales',
        lastLogin: new Date(2025, 4, 6),
        status: 'Active'
      },
      {
        id: 6,
        username: 'mdavis',
        fullName: 'Michael Davis',
        email: '<EMAIL>',
        role: 'Kitchen Staff',
        department: 'Kitchen',
        lastLogin: new Date(2025, 4, 5),
        status: 'Active'
      },
      {
        id: 7,
        username: 'lmiller',
        fullName: 'Lisa Miller',
        email: '<EMAIL>',
        role: 'Supervisor',
        department: 'Inventory',
        lastLogin: null,
        status: 'Inactive'
      }
    ];
    
    this.filteredUsers = [...this.users];
  }

  ngOnInit(): void {
  }

  applyFilter(): void {
    this.filteredUsers = this.users.filter(user => {
      const matchesSearch = this.searchTerm === '' || 
        user.username.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        user.fullName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesRole = this.selectedRole === '' || 
        user.role === this.selectedRole;
      
      const matchesStatus = this.selectedStatus === '' || 
        user.status === this.selectedStatus;
      
      return matchesSearch && matchesRole && matchesStatus;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedRole = '';
    this.selectedStatus = '';
    this.filteredUsers = [...this.users];
  }

  addUser(): void {
    console.log('Add user clicked');
  }

  editUser(user: User): void {
    console.log('Edit user clicked', user);
  }

  deleteUser(user: User): void {
    console.log('Delete user clicked', user);
  }

  viewUserDetails(user: User): void {
    console.log('View user details clicked', user);
  }

  resetPassword(user: User): void {
    console.log('Reset password clicked', user);
  }

  toggleUserStatus(user: User): void {
    console.log('Toggle user status clicked', user);
    const index = this.users.findIndex(u => u.id === user.id);
    if (index !== -1) {
      this.users[index].status = this.users[index].status === 'Active' ? 'Inactive' : 'Active';
      this.filteredUsers = [...this.filteredUsers]; // Trigger change detection
    }
  }
}
