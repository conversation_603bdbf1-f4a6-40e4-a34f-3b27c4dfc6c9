<div class="page-container">
  <div class="page-header">
    <h1>{{ isEditMode ? 'Edit Product' : 'Add Product' }}</h1>
    <div class="header-actions">
      <button mat-button (click)="cancel()">Cancel</button>
      <button mat-raised-button color="primary" (click)="saveProduct()" [disabled]="isLoading">
        <mat-icon *ngIf="isLoading">sync</mat-icon>
        Save
      </button>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <form [formGroup]="productForm" class="product-form" *ngIf="!isLoading">
    <mat-card>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Product Code</mat-label>
            <input matInput formControlName="code">
            <mat-error *ngIf="productForm.get('code')?.hasError('required')">
              Product Code is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Product Name</mat-label>
            <input matInput formControlName="name">
            <mat-error *ngIf="productForm.get('name')?.hasError('required')">
              Product Name is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Department</mat-label>
            <mat-select formControlName="departmentId">
              <mat-option [value]="null">None</mat-option>
              <mat-option *ngFor="let department of departments" [value]="department.id">
                {{department.name}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Brand</mat-label>
            <mat-select formControlName="brandId">
              <mat-option [value]="null">None</mat-option>
              <mat-option *ngFor="let brand of brands" [value]="brand.id">
                {{brand.name}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Unit</mat-label>
            <mat-select formControlName="unitId">
              <mat-option [value]="null">None</mat-option>
              <mat-option *ngFor="let unit of units" [value]="unit.id">
                {{unit.name}} ({{unit.abbreviation}})
              </mat-option>
            </mat-select>
            <mat-error *ngIf="productForm.get('unitId')?.hasError('required')">
              Unit is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Sales Unit</mat-label>
            <mat-select formControlName="salesUnitId">
              <mat-option [value]="null">Same as Base Unit</mat-option>
              <mat-option *ngFor="let unit of units" [value]="unit.id">
                {{unit.name}} ({{unit.abbreviation}})
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" *ngIf="productForm.get('salesUnitId')?.value">
            <mat-label>Conversion Factor</mat-label>
            <input matInput type="number" formControlName="salesUnitConversionFactor">
            <mat-hint>Number of base units in one sales unit</mat-hint>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Cost Price</mat-label>
            <input matInput type="number" formControlName="costPrice">
            <span matTextPrefix>$&nbsp;</span>
            <mat-error *ngIf="productForm.get('costPrice')?.hasError('required')">
              Cost Price is required
            </mat-error>
            <mat-error *ngIf="productForm.get('costPrice')?.hasError('min')">
              Cost Price must be greater than or equal to 0
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Sales Price</mat-label>
            <input matInput type="number" formControlName="salesPrice">
            <span matTextPrefix>$&nbsp;</span>
            <mat-error *ngIf="productForm.get('salesPrice')?.hasError('required')">
              Sales Price is required
            </mat-error>
            <mat-error *ngIf="productForm.get('salesPrice')?.hasError('min')">
              Sales Price must be greater than or equal to 0
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Tax</mat-label>
            <mat-select formControlName="taxId">
              <mat-option [value]="null">None</mat-option>
              <mat-option *ngFor="let tax of taxRates" [value]="tax.id">
                {{tax.name}} ({{tax.rate}}%)
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Min Stock</mat-label>
            <input matInput type="number" formControlName="minStock">
            <mat-error *ngIf="productForm.get('minStock')?.hasError('min')">
              Min Stock must be greater than or equal to 0
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Max Stock</mat-label>
            <input matInput type="number" formControlName="maxStock">
            <mat-error *ngIf="productForm.get('maxStock')?.hasError('min')">
              Max Stock must be greater than or equal to 0
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Reorder Point</mat-label>
            <input matInput type="number" formControlName="reorderPoint">
            <mat-error *ngIf="productForm.get('reorderPoint')?.hasError('min')">
              Reorder Point must be greater than or equal to 0
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Notes</mat-label>
            <textarea matInput formControlName="notes" rows="3"></textarea>
          </mat-form-field>
        </div>

        <div class="form-row checkbox-row">
          <mat-checkbox formControlName="isStockItem">Stock Item</mat-checkbox>
          <mat-checkbox formControlName="isRecipe">Recipe</mat-checkbox>
          <mat-checkbox formControlName="hasExpiry">Has Expiry</mat-checkbox>
          <mat-checkbox formControlName="isProduction">Production Item</mat-checkbox>
          <mat-checkbox formControlName="isSaleable">Saleable</mat-checkbox>
          <mat-checkbox formControlName="allowDiscount">Allow Discount</mat-checkbox>
          <mat-checkbox formControlName="isActive">Active</mat-checkbox>
        </div>
      </mat-card-content>
    </mat-card>
  </form>
</div>
