-- Improved Schema for Inventory Management System
-- Designed for .NET Core and Angular Web Application

USE [master]
GO

CREATE DATABASE [InventoryManagement]
GO

USE [InventoryManagement]
GO

-- Enable foreign key constraints
EXEC sp_MSforeachtable 'ALTER TABLE ? NOCHECK CONSTRAINT ALL'
GO

-- =============================================
-- CORE TABLES
-- =============================================

-- Unit Group Table (Missing in original schema)
CREATE TABLE [dbo].[UnitGroup](
    [UnitGroupId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](100) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UnitGroup] PRIMARY KEY CLUSTERED ([UnitGroupId] ASC),
    CONSTRAINT [UQ_UnitGroup_Name] UNIQUE ([Name])
)
GO

-- Cost Center Type Table (Missing in original schema)
CREATE TABLE [dbo].[CostCenterType](
    [TypeId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](100) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_CostCenterType] PRIMARY KEY CLUSTERED ([TypeId] ASC),
    CONSTRAINT [UQ_CostCenterType_Name] UNIQUE ([Name])
)
GO

-- Department Table
CREATE TABLE [dbo].[Department](
    [DepartmentId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Department] PRIMARY KEY CLUSTERED ([DepartmentId] ASC),
    CONSTRAINT [UQ_Department_Name] UNIQUE ([Name])
)
GO

-- Product Group Table
CREATE TABLE [dbo].[ProductGroup](
    [GroupId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [DepartmentId] [int] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ProductGroup] PRIMARY KEY CLUSTERED ([GroupId] ASC),
    CONSTRAINT [UQ_ProductGroup_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_ProductGroup_Department] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department] ([DepartmentId])
)
GO

-- Product SubGroup Table
CREATE TABLE [dbo].[ProductSubGroup](
    [SubGroupId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [GroupId] [int] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ProductSubGroup] PRIMARY KEY CLUSTERED ([SubGroupId] ASC),
    CONSTRAINT [UQ_ProductSubGroup_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_ProductSubGroup_ProductGroup] FOREIGN KEY ([GroupId]) REFERENCES [dbo].[ProductGroup] ([GroupId])
)
GO

-- Unit Table
CREATE TABLE [dbo].[Unit](
    [UnitId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Abbreviation] [nvarchar](10) NULL,
    [UnitGroupId] [int] NULL,
    [BaseConversionFactor] [decimal](18, 6) NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Unit] PRIMARY KEY CLUSTERED ([UnitId] ASC),
    CONSTRAINT [UQ_Unit_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_Unit_UnitGroup] FOREIGN KEY ([UnitGroupId]) REFERENCES [dbo].[UnitGroup] ([UnitGroupId])
)
GO

-- Brand Table
CREATE TABLE [dbo].[Brand](
    [BrandId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Brand] PRIMARY KEY CLUSTERED ([BrandId] ASC),
    CONSTRAINT [UQ_Brand_Name] UNIQUE ([Name])
)
GO

-- Tax Table
CREATE TABLE [dbo].[Tax](
    [TaxId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](100) NOT NULL,
    [Rate] [decimal](5, 2) NOT NULL,
    [IsDefault] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Tax] PRIMARY KEY CLUSTERED ([TaxId] ASC),
    CONSTRAINT [UQ_Tax_Name] UNIQUE ([Name])
)
GO

-- Product Table with support for sub-recipes
CREATE TABLE [dbo].[Product](
    [ProductId] [int] IDENTITY(1,1) NOT NULL,
    [Code] [nvarchar](50) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [BrandId] [int] NULL,
    [UnitId] [int] NULL,
    [UnitGroupId] [int] NULL,
    [DepartmentId] [int] NULL,
    [GroupId] [int] NULL,
    [SubGroupId] [int] NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [AverageCost] [decimal](18, 4) NULL,
    [SalesPrice] [decimal](18, 4) NULL,
    [MinStock] [decimal](18, 4) NULL,
    [MaxStock] [decimal](18, 4) NULL,
    [ReorderPoint] [decimal](18, 4) NULL,
    [Notes] [nvarchar](max) NULL,
    [IsStockItem] [bit] NOT NULL DEFAULT 1,
    [IsRecipe] [bit] NOT NULL DEFAULT 0,
    [HasExpiry] [bit] NOT NULL DEFAULT 0,
    [IsProduction] [bit] NOT NULL DEFAULT 0,
    [IsSaleable] [bit] NOT NULL DEFAULT 1,
    [TaxId] [int] NULL,
    [SalesUnitId] [int] NULL,
    [SalesUnitConversionFactor] [decimal](18, 6) NULL,
    [AllowDiscount] [bit] NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Product] PRIMARY KEY CLUSTERED ([ProductId] ASC),
    CONSTRAINT [UQ_Product_Code] UNIQUE ([Code]),
    CONSTRAINT [FK_Product_Brand] FOREIGN KEY ([BrandId]) REFERENCES [dbo].[Brand] ([BrandId]),
    CONSTRAINT [FK_Product_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId]),
    CONSTRAINT [FK_Product_Department] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department] ([DepartmentId]),
    CONSTRAINT [FK_Product_ProductGroup] FOREIGN KEY ([GroupId]) REFERENCES [dbo].[ProductGroup] ([GroupId]),
    CONSTRAINT [FK_Product_ProductSubGroup] FOREIGN KEY ([SubGroupId]) REFERENCES [dbo].[ProductSubGroup] ([SubGroupId]),
    CONSTRAINT [FK_Product_Tax] FOREIGN KEY ([TaxId]) REFERENCES [dbo].[Tax] ([TaxId]),
    CONSTRAINT [FK_Product_SalesUnit] FOREIGN KEY ([SalesUnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Recipe Table - Enhanced to support sub-recipes
CREATE TABLE [dbo].[Recipe](
    [RecipeId] [int] IDENTITY(1,1) NOT NULL,
    [ProductId] [int] NOT NULL,
    [Name] [nvarchar](150) NULL,
    [Description] [nvarchar](max) NULL,
    [YieldQuantity] [decimal](18, 4) NOT NULL DEFAULT 1,
    [YieldUnitId] [int] NULL,
    [TotalCost] [decimal](18, 4) NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    CONSTRAINT [PK_Recipe] PRIMARY KEY CLUSTERED ([RecipeId] ASC),
    CONSTRAINT [FK_Recipe_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_Recipe_Unit] FOREIGN KEY ([YieldUnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Recipe Ingredients Table - Supports sub-recipes
CREATE TABLE [dbo].[RecipeIngredient](
    [RecipeIngredientId] [int] IDENTITY(1,1) NOT NULL,
    [RecipeId] [int] NOT NULL,
    [IngredientProductId] [int] NOT NULL,
    [Quantity] [decimal](18, 4) NOT NULL,
    [UnitId] [int] NOT NULL,
    [CostPerUnit] [decimal](18, 4) NULL,
    [TotalCost] [decimal](18, 4) NULL,
    [IsSubRecipe] [bit] NOT NULL DEFAULT 0,
    [WastagePercentage] [decimal](5, 2) NULL DEFAULT 0,
    [Notes] [nvarchar](max) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_RecipeIngredient] PRIMARY KEY CLUSTERED ([RecipeIngredientId] ASC),
    CONSTRAINT [FK_RecipeIngredient_Recipe] FOREIGN KEY ([RecipeId]) REFERENCES [dbo].[Recipe] ([RecipeId]),
    CONSTRAINT [FK_RecipeIngredient_Product] FOREIGN KEY ([IngredientProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_RecipeIngredient_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Location Table
CREATE TABLE [dbo].[Location](
    [LocationId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [Address] [nvarchar](255) NULL,
    [City] [nvarchar](100) NULL,
    [State] [nvarchar](100) NULL,
    [Country] [nvarchar](100) NULL,
    [PostalCode] [nvarchar](20) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Location] PRIMARY KEY CLUSTERED ([LocationId] ASC),
    CONSTRAINT [UQ_Location_Name] UNIQUE ([Name])
)
GO

-- Store Table
CREATE TABLE [dbo].[Store](
    [StoreId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [LocationId] [int] NULL,
    [IsSalesPoint] [bit] NOT NULL DEFAULT 0,
    [LogoPath] [nvarchar](255) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Store] PRIMARY KEY CLUSTERED ([StoreId] ASC),
    CONSTRAINT [UQ_Store_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_Store_Location] FOREIGN KEY ([LocationId]) REFERENCES [dbo].[Location] ([LocationId])
)
GO

-- Cost Center Table
CREATE TABLE [dbo].[CostCenter](
    [CostCenterId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [StoreId] [int] NULL,
    [TypeId] [int] NULL,
    [TypeName] [nvarchar](100) NULL,
    [AutoTransfer] [bit] NOT NULL DEFAULT 0,
    [IsSalesPoint] [bit] NOT NULL DEFAULT 0,
    [Abbreviation] [nvarchar](50) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_CostCenter] PRIMARY KEY CLUSTERED ([CostCenterId] ASC),
    CONSTRAINT [UQ_CostCenter_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_CostCenter_Store] FOREIGN KEY ([StoreId]) REFERENCES [dbo].[Store] ([StoreId]),
    CONSTRAINT [FK_CostCenter_CostCenterType] FOREIGN KEY ([TypeId]) REFERENCES [dbo].[CostCenterType] ([TypeId])
)
GO

-- =============================================
-- INVENTORY MANAGEMENT TABLES
-- =============================================

-- Barcode Table (New)
CREATE TABLE [dbo].[Barcode](
    [BarcodeId] [int] IDENTITY(1,1) NOT NULL,
    [ProductId] [int] NOT NULL,
    [BarcodeValue] [nvarchar](100) NOT NULL,
    [BarcodeType] [nvarchar](50) NOT NULL DEFAULT 'EAN13',
    [UnitId] [int] NULL,
    [Quantity] [decimal](18, 6) NULL DEFAULT 1,
    [IsPrimary] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Barcode] PRIMARY KEY CLUSTERED ([BarcodeId] ASC),
    CONSTRAINT [UQ_Barcode_Value] UNIQUE ([BarcodeValue]),
    CONSTRAINT [FK_Barcode_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_Barcode_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Stock On Hand Table
CREATE TABLE [dbo].[StockOnHand](
    [StockId] [int] IDENTITY(1,1) NOT NULL,
    [ProductId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [Quantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [BaseQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [UnitId] [int] NULL,
    [AverageCost] [decimal](18, 4) NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [ReturnVariance] [decimal](18, 4) NULL DEFAULT 0,
    [NetCost] [decimal](18, 4) NULL,
    [SalesPrice] [decimal](18, 4) NULL,
    [LastUpdated] [datetime2] NOT NULL DEFAULT GETDATE(),
    CONSTRAINT [PK_StockOnHand] PRIMARY KEY CLUSTERED ([StockId] ASC),
    CONSTRAINT [UQ_StockOnHand_Product_CostCenter] UNIQUE ([ProductId], [CostCenterId]),
    CONSTRAINT [FK_StockOnHand_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_StockOnHand_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_StockOnHand_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Product Cost Center Link Table
CREATE TABLE [dbo].[ProductCostCenterLink](
    [LinkId] [int] IDENTITY(1,1) NOT NULL,
    [ProductId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [MaximumStock] [decimal](18, 4) NULL,
    [MinimumStock] [decimal](18, 4) NULL,
    [ReorderPoint] [decimal](18, 4) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ProductCostCenterLink] PRIMARY KEY CLUSTERED ([LinkId] ASC),
    CONSTRAINT [UQ_ProductCostCenterLink] UNIQUE ([ProductId], [CostCenterId]),
    CONSTRAINT [FK_ProductCostCenterLink_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_ProductCostCenterLink_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId])
)
GO

-- Batch/Lot Tracking Table
CREATE TABLE [dbo].[Batch](
    [BatchId] [int] IDENTITY(1,1) NOT NULL,
    [BatchNumber] [nvarchar](150) NOT NULL,
    [ProductId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [ManufactureDate] [datetime2] NULL,
    [ExpiryDate] [datetime2] NULL,
    [InitialQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [CurrentQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [UnitId] [int] NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [IsOpen] [bit] NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Batch] PRIMARY KEY CLUSTERED ([BatchId] ASC),
    CONSTRAINT [UQ_Batch_Number_Product_CostCenter] UNIQUE ([BatchNumber], [ProductId], [CostCenterId]),
    CONSTRAINT [FK_Batch_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_Batch_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_Batch_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- =============================================
-- TRANSACTION TABLES
-- =============================================

-- Supplier Table
CREATE TABLE [dbo].[Supplier](
    [SupplierId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [ContactPerson] [nvarchar](150) NULL,
    [Email] [nvarchar](150) NULL,
    [Phone] [nvarchar](50) NULL,
    [Address] [nvarchar](255) NULL,
    [City] [nvarchar](100) NULL,
    [State] [nvarchar](100) NULL,
    [Country] [nvarchar](100) NULL,
    [PostalCode] [nvarchar](20) NULL,
    [TaxNumber] [nvarchar](50) NULL,
    [Notes] [nvarchar](max) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Supplier] PRIMARY KEY CLUSTERED ([SupplierId] ASC),
    CONSTRAINT [UQ_Supplier_Name] UNIQUE ([Name])
)
GO

-- Transaction Type Table
CREATE TABLE [dbo].[TransactionType](
    [TransactionTypeId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [AffectsInventory] [bit] NOT NULL DEFAULT 1,
    [IsSale] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionType] PRIMARY KEY CLUSTERED ([TransactionTypeId] ASC),
    CONSTRAINT [UQ_TransactionType_Name] UNIQUE ([Name])
)
GO

-- Purchase Order Table
CREATE TABLE [dbo].[PurchaseOrder](
    [PurchaseOrderId] [int] IDENTITY(1,1) NOT NULL,
    [DocumentNumber] [nvarchar](50) NOT NULL,
    [SupplierId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [OrderDate] [datetime2] NOT NULL,
    [ExpectedDeliveryDate] [datetime2] NULL,
    [Status] [nvarchar](20) NOT NULL DEFAULT 'Draft', -- Draft, Pending, Approved, Rejected, Completed, Cancelled
    [Notes] [nvarchar](500) NULL,
    [TotalAmount] [decimal](18, 4) NOT NULL DEFAULT 0,
    [CreatedBy] [nvarchar](100) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedBy] [nvarchar](100) NULL,
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_PurchaseOrder] PRIMARY KEY CLUSTERED ([PurchaseOrderId] ASC),
    CONSTRAINT [UQ_PurchaseOrder_DocumentNumber] UNIQUE ([DocumentNumber]),
    CONSTRAINT [FK_PurchaseOrder_Supplier] FOREIGN KEY ([SupplierId]) REFERENCES [dbo].[Supplier] ([SupplierId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_PurchaseOrder_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]) ON DELETE NO ACTION
)
GO

-- Purchase Order Detail Table
CREATE TABLE [dbo].[PurchaseOrderDetail](
    [PurchaseOrderDetailId] [int] IDENTITY(1,1) NOT NULL,
    [PurchaseOrderId] [int] NOT NULL,
    [ProductId] [int] NOT NULL,
    [Quantity] [decimal](18, 4) NOT NULL,
    [UnitPrice] [decimal](18, 4) NOT NULL,
    [TotalPrice] [decimal](18, 4) NOT NULL,
    [Notes] [nvarchar](500) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_PurchaseOrderDetail] PRIMARY KEY CLUSTERED ([PurchaseOrderDetailId] ASC),
    CONSTRAINT [FK_PurchaseOrderDetail_PurchaseOrder] FOREIGN KEY ([PurchaseOrderId]) REFERENCES [dbo].[PurchaseOrder] ([PurchaseOrderId]) ON DELETE CASCADE,
    CONSTRAINT [FK_PurchaseOrderDetail_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]) ON DELETE NO ACTION
)
GO

-- Transaction Process Table - To support the three-part transaction process
CREATE TABLE [dbo].[TransactionProcess](
    [ProcessId] [int] IDENTITY(1,1) NOT NULL,
    [ProcessNumber] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [CreatedById] [int] NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionProcess] PRIMARY KEY CLUSTERED ([ProcessId] ASC),
    CONSTRAINT [UQ_TransactionProcess_ProcessNumber] UNIQUE ([ProcessNumber])
)
GO

-- Transaction Stage Type Table - To define the stages in the transaction process
CREATE TABLE [dbo].[TransactionStageType](
    [StageTypeId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [Sequence] [int] NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionStageType] PRIMARY KEY CLUSTERED ([StageTypeId] ASC),
    CONSTRAINT [UQ_TransactionStageType_Name] UNIQUE ([Name])
)
GO

-- Insert default transaction stage types
INSERT INTO [dbo].[TransactionStageType] ([Name], [Description], [Sequence], [CreatedAt], [IsActive])
VALUES
    ('Request', 'Product request stage', 1, GETDATE(), 1),
    ('Order', 'Product order stage', 2, GETDATE(), 1),
    ('Receiving', 'Product receiving stage', 3, GETDATE(), 1),
    ('Transfer', 'Product transfer to another cost center', 4, GETDATE(), 1),
    ('Adjustment', 'Inventory adjustment', 5, GETDATE(), 1),
    ('Production', 'Production of recipes', 6, GETDATE(), 1),
    ('Return', 'Return from customer', 7, GETDATE(), 1),
    ('ReturnToSupplier', 'Return to supplier', 8, GETDATE(), 1);
GO

-- Transaction Header Table - Enhanced to support the three-part transaction process
CREATE TABLE [dbo].[TransactionHeader](
    [TransactionId] [int] IDENTITY(1,1) NOT NULL,
    [TransactionNumber] [nvarchar](50) NOT NULL,
    [ProcessId] [int] NULL, -- Link to the overall transaction process
    [StageTypeId] [int] NULL, -- Identifies which stage this transaction represents
    [TransactionTypeId] [int] NOT NULL,
    [SourceCostCenterId] [int] NULL,
    [DestinationCostCenterId] [int] NULL,
    [SupplierId] [int] NULL,
    [ReferenceNumber] [nvarchar](50) NULL,
    [TransactionDate] [datetime2] NOT NULL,
    [Notes] [nvarchar](max) NULL,
    [SubTotal] [decimal](18, 4) NOT NULL DEFAULT 0,
    [TaxAmount] [decimal](18, 4) NOT NULL DEFAULT 0,
    [TotalAmount] [decimal](18, 4) NOT NULL DEFAULT 0,
    [DiscountAmount] [decimal](18, 4) NOT NULL DEFAULT 0,
    [DiscountPercentage] [decimal](5, 2) NOT NULL DEFAULT 0,
    [Status] [nvarchar](20) NOT NULL DEFAULT 'Draft', -- Draft, Submitted, Approved, Rejected, Cancelled
    [CreatedById] [int] NOT NULL,
    [ApprovedById] [int] NULL,
    [ApprovedDate] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionHeader] PRIMARY KEY CLUSTERED ([TransactionId] ASC),
    CONSTRAINT [UQ_TransactionHeader_TransactionNumber] UNIQUE ([TransactionNumber]),
    CONSTRAINT [FK_TransactionHeader_TransactionProcess] FOREIGN KEY ([ProcessId]) REFERENCES [dbo].[TransactionProcess] ([ProcessId]),
    CONSTRAINT [FK_TransactionHeader_TransactionStageType] FOREIGN KEY ([StageTypeId]) REFERENCES [dbo].[TransactionStageType] ([StageTypeId]),
    CONSTRAINT [FK_TransactionHeader_TransactionType] FOREIGN KEY ([TransactionTypeId]) REFERENCES [dbo].[TransactionType] ([TransactionTypeId]),
    CONSTRAINT [FK_TransactionHeader_SourceCostCenter] FOREIGN KEY ([SourceCostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_TransactionHeader_DestinationCostCenter] FOREIGN KEY ([DestinationCostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_TransactionHeader_Supplier] FOREIGN KEY ([SupplierId]) REFERENCES [dbo].[Supplier] ([SupplierId])
)
GO

-- Transaction Detail Table
CREATE TABLE [dbo].[TransactionDetail](
    [TransactionDetailId] [int] IDENTITY(1,1) NOT NULL,
    [TransactionId] [int] NOT NULL,
    [ProductId] [int] NOT NULL,
    [BatchId] [int] NULL,
    [Quantity] [decimal](18, 4) NOT NULL,
    [UnitId] [int] NOT NULL,
    [UnitPrice] [decimal](18, 4) NOT NULL,
    [TaxId] [int] NULL,
    [TaxRate] [decimal](5, 2) NULL,
    [TaxAmount] [decimal](18, 4) NULL,
    [DiscountAmount] [decimal](18, 4) NULL DEFAULT 0,
    [DiscountPercentage] [decimal](5, 2) NULL DEFAULT 0,
    [LineTotal] [decimal](18, 4) NOT NULL,
    [Notes] [nvarchar](max) NULL,
    [IsRecipe] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionDetail] PRIMARY KEY CLUSTERED ([TransactionDetailId] ASC),
    CONSTRAINT [FK_TransactionDetail_TransactionHeader] FOREIGN KEY ([TransactionId]) REFERENCES [dbo].[TransactionHeader] ([TransactionId]),
    CONSTRAINT [FK_TransactionDetail_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_TransactionDetail_Batch] FOREIGN KEY ([BatchId]) REFERENCES [dbo].[Batch] ([BatchId]),
    CONSTRAINT [FK_TransactionDetail_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId]),
    CONSTRAINT [FK_TransactionDetail_Tax] FOREIGN KEY ([TaxId]) REFERENCES [dbo].[Tax] ([TaxId])
)
GO

-- Stock Take Header Table
CREATE TABLE [dbo].[StockTakeHeader](
    [StockTakeId] [int] IDENTITY(1,1) NOT NULL,
    [StockTakeNumber] [nvarchar](50) NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [StockTakeDate] [datetime2] NOT NULL,
    [Notes] [nvarchar](max) NULL,
    [Status] [nvarchar](20) NOT NULL DEFAULT 'Draft', -- Draft, In Progress, Completed, Cancelled
    [TotalVariance] [decimal](18, 4) NULL,
    [CreatedById] [int] NOT NULL,
    [CompletedById] [int] NULL,
    [CompletedDate] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_StockTakeHeader] PRIMARY KEY CLUSTERED ([StockTakeId] ASC),
    CONSTRAINT [UQ_StockTakeHeader_StockTakeNumber] UNIQUE ([StockTakeNumber]),
    CONSTRAINT [FK_StockTakeHeader_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId])
)
GO

-- Stock Take Detail Table
CREATE TABLE [dbo].[StockTakeDetail](
    [StockTakeDetailId] [int] IDENTITY(1,1) NOT NULL,
    [StockTakeId] [int] NOT NULL,
    [ProductId] [int] NOT NULL,
    [BatchId] [int] NULL,
    [SystemQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [ActualQuantity] [decimal](18, 4) NULL,
    [VarianceQuantity] [decimal](18, 4) NULL,
    [UnitId] [int] NOT NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [VarianceValue] [decimal](18, 4) NULL,
    [Notes] [nvarchar](max) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_StockTakeDetail] PRIMARY KEY CLUSTERED ([StockTakeDetailId] ASC),
    CONSTRAINT [FK_StockTakeDetail_StockTakeHeader] FOREIGN KEY ([StockTakeId]) REFERENCES [dbo].[StockTakeHeader] ([StockTakeId]),
    CONSTRAINT [FK_StockTakeDetail_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_StockTakeDetail_Batch] FOREIGN KEY ([BatchId]) REFERENCES [dbo].[Batch] ([BatchId]),
    CONSTRAINT [FK_StockTakeDetail_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- =============================================
-- USER MANAGEMENT TABLES
-- =============================================

-- Audit Log Table (New)
CREATE TABLE [dbo].[AuditLog](
    [AuditLogId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NULL,
    [Action] [nvarchar](50) NOT NULL,
    [TableName] [nvarchar](100) NOT NULL,
    [RecordId] [nvarchar](50) NOT NULL,
    [OldValues] [nvarchar](max) NULL,
    [NewValues] [nvarchar](max) NULL,
    [ChangedColumns] [nvarchar](max) NULL,
    [Timestamp] [datetime2] NOT NULL DEFAULT GETDATE(),
    [IpAddress] [nvarchar](50) NULL,
    CONSTRAINT [PK_AuditLog] PRIMARY KEY CLUSTERED ([AuditLogId] ASC)
)
GO

-- Role Table
CREATE TABLE [dbo].[Role](
    [RoleId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [Permissions] [nvarchar](max) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Role] PRIMARY KEY CLUSTERED ([RoleId] ASC),
    CONSTRAINT [UQ_Role_Name] UNIQUE ([Name])
)
GO

-- User Table
CREATE TABLE [dbo].[User](
    [UserId] [int] IDENTITY(1,1) NOT NULL,
    [Username] [nvarchar](50) NOT NULL,
    [Email] [nvarchar](150) NULL,
    [PasswordHash] [nvarchar](500) NOT NULL,
    [FirstName] [nvarchar](100) NULL,
    [LastName] [nvarchar](100) NULL,
    [PhoneNumber] [nvarchar](50) NULL,
    [RoleId] [int] NOT NULL,
    [DefaultLanguage] [nvarchar](10) NULL DEFAULT 'en',
    [LastLoginDate] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_User] PRIMARY KEY CLUSTERED ([UserId] ASC),
    CONSTRAINT [UQ_User_Username] UNIQUE ([Username]),
    CONSTRAINT [UQ_User_Email] UNIQUE ([Email]),
    CONSTRAINT [FK_User_Role] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role] ([RoleId])
)
GO

-- User Cost Center Access Table
CREATE TABLE [dbo].[UserCostCenterAccess](
    [AccessId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [CanView] [bit] NOT NULL DEFAULT 1,
    [CanCreate] [bit] NOT NULL DEFAULT 0,
    [CanEdit] [bit] NOT NULL DEFAULT 0,
    [CanDelete] [bit] NOT NULL DEFAULT 0,
    [CanApprove] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UserCostCenterAccess] PRIMARY KEY CLUSTERED ([AccessId] ASC),
    CONSTRAINT [UQ_UserCostCenterAccess] UNIQUE ([UserId], [CostCenterId]),
    CONSTRAINT [FK_UserCostCenterAccess_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_UserCostCenterAccess_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId])
)
GO

-- =============================================
-- PAYMENT AND SALES TABLES
-- =============================================

-- Customer Table (New)
CREATE TABLE [dbo].[Customer](
    [CustomerId] [int] IDENTITY(1,1) NOT NULL,
    [CustomerCode] [nvarchar](50) NULL,
    [FirstName] [nvarchar](100) NOT NULL,
    [LastName] [nvarchar](100) NULL,
    [CompanyName] [nvarchar](150) NULL,
    [Email] [nvarchar](150) NULL,
    [Phone] [nvarchar](50) NULL,
    [Address] [nvarchar](255) NULL,
    [City] [nvarchar](100) NULL,
    [State] [nvarchar](100) NULL,
    [Country] [nvarchar](100) NULL,
    [PostalCode] [nvarchar](20) NULL,
    [TaxNumber] [nvarchar](50) NULL,
    [CreditLimit] [decimal](18, 4) NULL,
    [DiscountPercentage] [decimal](5, 2) NULL DEFAULT 0,
    [Notes] [nvarchar](max) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Customer] PRIMARY KEY CLUSTERED ([CustomerId] ASC),
    CONSTRAINT [UQ_Customer_Email] UNIQUE ([Email])
)
GO

-- Payment Method Table
CREATE TABLE [dbo].[PaymentMethod](
    [PaymentMethodId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [AccountNumber] [nvarchar](50) NULL,
    [IsPointsSystem] [bit] NOT NULL DEFAULT 0,
    [PointsConversionRate] [decimal](18, 4) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_PaymentMethod] PRIMARY KEY CLUSTERED ([PaymentMethodId] ASC),
    CONSTRAINT [UQ_PaymentMethod_Name] UNIQUE ([Name])
)
GO

-- Payment Table
CREATE TABLE [dbo].[Payment](
    [PaymentId] [int] IDENTITY(1,1) NOT NULL,
    [TransactionId] [int] NOT NULL,
    [PaymentMethodId] [int] NOT NULL,
    [Amount] [decimal](18, 4) NOT NULL,
    [ChangeAmount] [decimal](18, 4) NULL DEFAULT 0,
    [PointsUsed] [decimal](18, 4) NULL DEFAULT 0,
    [PointsRate] [decimal](18, 4) NULL,
    [Notes] [nvarchar](max) NULL,
    [PaymentDate] [datetime2] NOT NULL DEFAULT GETDATE(),
    [CreatedById] [int] NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Payment] PRIMARY KEY CLUSTERED ([PaymentId] ASC),
    CONSTRAINT [FK_Payment_TransactionHeader] FOREIGN KEY ([TransactionId]) REFERENCES [dbo].[TransactionHeader] ([TransactionId]),
    CONSTRAINT [FK_Payment_PaymentMethod] FOREIGN KEY ([PaymentMethodId]) REFERENCES [dbo].[PaymentMethod] ([PaymentMethodId])
)
GO

-- Currency Table
CREATE TABLE [dbo].[Currency](
    [CurrencyId] [int] IDENTITY(1,1) NOT NULL,
    [Code] [nvarchar](3) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Symbol] [nvarchar](5) NULL,
    [ExchangeRate] [decimal](18, 6) NOT NULL DEFAULT 1,
    [IsBaseCurrency] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Currency] PRIMARY KEY CLUSTERED ([CurrencyId] ASC),
    CONSTRAINT [UQ_Currency_Code] UNIQUE ([Code])
)
GO

-- Shift Table
CREATE TABLE [dbo].[Shift](
    [ShiftId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [StartTime] [time] NULL,
    [EndTime] [time] NULL,
    [Description] [nvarchar](255) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Shift] PRIMARY KEY CLUSTERED ([ShiftId] ASC),
    CONSTRAINT [UQ_Shift_Name] UNIQUE ([Name])
)
GO

-- Document Storage Table (New)
CREATE TABLE [dbo].[DocumentStorage](
    [DocumentId] [int] IDENTITY(1,1) NOT NULL,
    [FileName] [nvarchar](255) NOT NULL,
    [FileType] [nvarchar](100) NOT NULL,
    [FileSize] [bigint] NOT NULL,
    [FilePath] [nvarchar](500) NOT NULL,
    [EntityType] [nvarchar](100) NOT NULL,
    [EntityId] [int] NOT NULL,
    [Description] [nvarchar](255) NULL,
    [UploadedById] [int] NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_DocumentStorage] PRIMARY KEY CLUSTERED ([DocumentId] ASC)
)
GO

-- =============================================
-- NOTIFICATION SYSTEM
-- =============================================

-- Notification Table (New)
CREATE TABLE [dbo].[Notification](
    [NotificationId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [Title] [nvarchar](150) NOT NULL,
    [Message] [nvarchar](max) NOT NULL,
    [NotificationType] [nvarchar](50) NOT NULL,
    [EntityType] [nvarchar](100) NULL,
    [EntityId] [int] NULL,
    [IsRead] [bit] NOT NULL DEFAULT 0,
    [ReadAt] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [ExpiresAt] [datetime2] NULL,
    CONSTRAINT [PK_Notification] PRIMARY KEY CLUSTERED ([NotificationId] ASC),
    CONSTRAINT [FK_Notification_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId])
)
GO

-- User Preference Table (New)
CREATE TABLE [dbo].[UserPreference](
    [PreferenceId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [PreferenceKey] [nvarchar](100) NOT NULL,
    [PreferenceValue] [nvarchar](max) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    CONSTRAINT [PK_UserPreference] PRIMARY KEY CLUSTERED ([PreferenceId] ASC),
    CONSTRAINT [UQ_UserPreference_User_Key] UNIQUE ([UserId], [PreferenceKey]),
    CONSTRAINT [FK_UserPreference_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId])
)
GO

-- API Key Table (New)
CREATE TABLE [dbo].[ApiKey](
    [ApiKeyId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](100) NOT NULL,
    [KeyValue] [nvarchar](100) NOT NULL,
    [ExpiresAt] [datetime2] NULL,
    [CreatedById] [int] NOT NULL,
    [Permissions] [nvarchar](max) NULL,
    [LastUsed] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ApiKey] PRIMARY KEY CLUSTERED ([ApiKeyId] ASC),
    CONSTRAINT [UQ_ApiKey_KeyValue] UNIQUE ([KeyValue]),
    CONSTRAINT [FK_ApiKey_User] FOREIGN KEY ([CreatedById]) REFERENCES [dbo].[User] ([UserId])
)
GO

-- =============================================
-- NOTE: Initial data is now handled in the migration script
-- =============================================
-- Default data has been moved to migration_script.sql to avoid conflicts
-- during the migration process. The migration script will check for existing
-- data before inserting defaults.
-- =============================================

-- =============================================
-- ENABLE FOREIGN KEY CONSTRAINTS
-- =============================================

EXEC sp_MSforeachtable 'ALTER TABLE ? CHECK CONSTRAINT ALL'
GO

-- =============================================
-- SCHEMA NOTES
-- =============================================

/*
IMPORTANT SCHEMA IMPROVEMENTS:

1. Primary Key Changes:
   - All tables now use ID-based primary keys instead of name-based primary keys
   - Names are still enforced as unique through UNIQUE constraints
   - This improves performance and referential integrity

2. Recipe Support:
   - Enhanced Recipe and RecipeIngredient tables with proper relationships
   - Added IsSubRecipe flag to support sub-recipes
   - Recipes can now be nested to any level

3. Modern Web App Features:
   - Added CreatedAt/UpdatedAt timestamps for all tables
   - Added soft delete functionality with IsActive flag
   - Proper foreign key constraints for referential integrity
   - Consistent naming conventions throughout the schema

4. Authentication & Authorization:
   - Modern user authentication system
   - Role-based authorization
   - Granular permissions at the cost center level
   - API key management for external integrations

5. Additional Improvements:
   - Proper decimal precision for monetary values
   - Batch/lot tracking for expiry date management
   - Multi-currency support
   - Payment processing with multiple payment methods
   - Shift management for sales tracking

6. New Features for .NET 8 and Angular 17:
   - Added UnitGroup and CostCenterType tables for better organization
   - Added Barcode support for products with multiple barcode types
   - Added comprehensive audit logging for all changes
   - Added document storage for file attachments
   - Added notification system for real-time alerts
   - Added customer management for sales tracking
   - Added user preferences for personalization
   - Added API key management for secure external access

This schema is designed to work with .NET 8 and Angular 17 web applications,
providing a solid foundation for a modern inventory management system with
enhanced security, performance, and user experience.
*/
GO
