using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IDepartmentService
{
    Task<IEnumerable<DepartmentDto>> GetAllDepartmentsAsync();
    Task<DepartmentDto?> GetDepartmentByIdAsync(int id);
    Task<DepartmentDto> CreateDepartmentAsync(CreateDepartmentDto createDepartmentDto);
    Task UpdateDepartmentAsync(UpdateDepartmentDto updateDepartmentDto);
    Task DeleteDepartmentAsync(int id);
}
