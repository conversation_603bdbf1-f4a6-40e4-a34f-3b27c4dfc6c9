import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { ProductSubGroup, CreateProductSubGroup, UpdateProductSubGroup } from '../models/product-sub-group.model';

@Injectable({
  providedIn: 'root'
})
export class ProductSubGroupService {
  private readonly path = 'productsubgroups';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<ProductSubGroup[]> {
    return this.apiService.get<ProductSubGroup[]>(this.path);
  }

  getById(id: number): Observable<ProductSubGroup> {
    return this.apiService.get<ProductSubGroup>(`${this.path}/${id}`);
  }

  getByGroupId(groupId: number): Observable<ProductSubGroup[]> {
    return this.apiService.get<ProductSubGroup[]>(`${this.path}/group/${groupId}`);
  }

  create(productSubGroup: CreateProductSubGroup): Observable<ProductSubGroup> {
    return this.apiService.post<ProductSubGroup, CreateProductSubGroup>(this.path, productSubGroup);
  }

  update(id: number, productSubGroup: UpdateProductSubGroup): Observable<void> {
    return this.apiService.put<void, UpdateProductSubGroup>(`${this.path}/${id}`, productSubGroup);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
