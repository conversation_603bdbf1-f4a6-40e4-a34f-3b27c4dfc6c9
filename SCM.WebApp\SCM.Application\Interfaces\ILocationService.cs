using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface ILocationService
{
    Task<IEnumerable<LocationDto>> GetAllLocationsAsync();
    Task<LocationDto?> GetLocationByIdAsync(int id);
    Task<IEnumerable<LocationDto>> GetLocationsByStoreIdAsync(int storeId);
    Task<LocationDto?> GetLocationWithCostCentersAsync(int id);
    Task<LocationDto> CreateLocationAsync(CreateLocationDto createLocationDto);
    Task UpdateLocationAsync(UpdateLocationDto updateLocationDto);
    Task DeleteLocationAsync(int id);
}
