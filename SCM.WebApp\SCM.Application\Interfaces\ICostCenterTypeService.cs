using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface ICostCenterTypeService
{
    Task<IEnumerable<CostCenterTypeDto>> GetAllCostCenterTypesAsync();
    Task<CostCenterTypeDto?> GetCostCenterTypeByIdAsync(int id);
    Task<CostCenterTypeDto?> GetCostCenterTypeWithCostCentersAsync(int id);
    Task<CostCenterTypeDto> CreateCostCenterTypeAsync(CreateCostCenterTypeDto createCostCenterTypeDto);
    Task UpdateCostCenterTypeAsync(UpdateCostCenterTypeDto updateCostCenterTypeDto);
    Task DeleteCostCenterTypeAsync(int id);
}
