<div class="page-container">
  <div class="page-header">
    <h1>Role Management</h1>
  </div>
  
  <div class="content-container">
    <div class="form-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>{{ isEditMode ? 'Edit Role' : 'Add Role' }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="roleForm" class="role-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Role Name</mat-label>
              <input matInput formControlName="name">
              <mat-error *ngIf="roleForm.get('name')?.hasError('required')">
                Role Name is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" rows="3"></textarea>
            </mat-form-field>
            
            <h3>Permissions</h3>
            
            <div class="permissions-container">
              <div *ngFor="let category of permissionCategories" class="permission-category">
                <h4>{{category}}</h4>
                <div class="permission-list">
                  <mat-checkbox 
                    *ngFor="let permission of getPermissionsByCategory(category)"
                    [checked]="permission.selected"
                    (change)="togglePermission(permission)">
                    {{permission.name}}
                  </mat-checkbox>
                </div>
              </div>
            </div>
            
            <div class="form-actions">
              <button mat-button *ngIf="isEditMode" (click)="cancelEdit()">Cancel</button>
              <button mat-raised-button color="primary" (click)="saveRole()" [disabled]="roleForm.invalid">
                {{ isEditMode ? 'Update' : 'Add' }}
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
    
    <div class="table-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Roles</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="table-container mat-elevation-z0">
            <table mat-table [dataSource]="roles" class="role-table">
              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let role">{{role.id}}</td>
              </ng-container>
              
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let role">{{role.name}}</td>
              </ng-container>
              
              <!-- Description Column -->
              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let role">{{role.description}}</td>
              </ng-container>
              
              <!-- User Count Column -->
              <ng-container matColumnDef="userCount">
                <th mat-header-cell *matHeaderCellDef>Users</th>
                <td mat-cell *matCellDef="let role">{{role.userCount}}</td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let role">
                  <button mat-icon-button color="primary" (click)="editRole(role)">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteRole(role)" [disabled]="role.userCount > 0">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
