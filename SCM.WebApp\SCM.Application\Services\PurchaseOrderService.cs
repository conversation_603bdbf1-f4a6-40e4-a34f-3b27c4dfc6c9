using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class PurchaseOrderService : IPurchaseOrderService
{
    private readonly IRepository<PurchaseOrder> _purchaseOrderRepository;
    private readonly IRepository<PurchaseOrderDetail> _purchaseOrderDetailRepository;
    private readonly IRepository<Product> _productRepository;
    private readonly IRepository<Supplier> _supplierRepository;
    private readonly IRepository<CostCenter> _costCenterRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public PurchaseOrderService(
        IRepository<PurchaseOrder> purchaseOrderRepository,
        IRepository<PurchaseOrderDetail> purchaseOrderDetailRepository,
        IRepository<Product> productRepository,
        IRepository<Supplier> supplierRepository,
        IRepository<CostCenter> costCenterRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _purchaseOrderRepository = purchaseOrderRepository;
        _purchaseOrderDetailRepository = purchaseOrderDetailRepository;
        _productRepository = productRepository;
        _supplierRepository = supplierRepository;
        _costCenterRepository = costCenterRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<PurchaseOrderListDto>> GetAllPurchaseOrdersAsync()
    {
        var purchaseOrders = await _dbContext.PurchaseOrders
            .Include(po => po.Supplier)
            .Include(po => po.CostCenter)
            .OrderByDescending(po => po.OrderDate)
            .ToListAsync();

        return _mapper.Map<IEnumerable<PurchaseOrderListDto>>(purchaseOrders);
    }

    public async Task<PurchaseOrderDto?> GetPurchaseOrderByIdAsync(int id)
    {
        var purchaseOrder = await _dbContext.PurchaseOrders
            .Include(po => po.Supplier)
            .Include(po => po.CostCenter)
            .Include(po => po.Details)
                .ThenInclude(d => d.Product)
            .FirstOrDefaultAsync(po => po.Id == id);

        return purchaseOrder != null ? _mapper.Map<PurchaseOrderDto>(purchaseOrder) : null;
    }

    public async Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersBySupplierAsync(int supplierId)
    {
        var purchaseOrders = await _dbContext.PurchaseOrders
            .Include(po => po.Supplier)
            .Include(po => po.CostCenter)
            .Where(po => po.SupplierId == supplierId)
            .OrderByDescending(po => po.OrderDate)
            .ToListAsync();

        return _mapper.Map<IEnumerable<PurchaseOrderListDto>>(purchaseOrders);
    }

    public async Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersByCostCenterAsync(int costCenterId)
    {
        var purchaseOrders = await _dbContext.PurchaseOrders
            .Include(po => po.Supplier)
            .Include(po => po.CostCenter)
            .Where(po => po.CostCenterId == costCenterId)
            .OrderByDescending(po => po.OrderDate)
            .ToListAsync();

        return _mapper.Map<IEnumerable<PurchaseOrderListDto>>(purchaseOrders);
    }

    public async Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersByStatusAsync(string status)
    {
        var purchaseOrders = await _dbContext.PurchaseOrders
            .Include(po => po.Supplier)
            .Include(po => po.CostCenter)
            .Where(po => po.Status == status)
            .OrderByDescending(po => po.OrderDate)
            .ToListAsync();

        return _mapper.Map<IEnumerable<PurchaseOrderListDto>>(purchaseOrders);
    }

    public async Task<PurchaseOrderDto> CreatePurchaseOrderAsync(CreatePurchaseOrderDto createPurchaseOrderDto)
    {
        // Validate supplier
        var supplier = await _supplierRepository.GetByIdAsync(createPurchaseOrderDto.SupplierId);
        if (supplier == null)
            throw new KeyNotFoundException($"Supplier with ID {createPurchaseOrderDto.SupplierId} not found.");

        // Validate cost center
        var costCenter = await _costCenterRepository.GetByIdAsync(createPurchaseOrderDto.CostCenterId);
        if (costCenter == null)
            throw new KeyNotFoundException($"Cost Center with ID {createPurchaseOrderDto.CostCenterId} not found.");

        // Create purchase order
        var purchaseOrder = _mapper.Map<PurchaseOrder>(createPurchaseOrderDto);

        // Generate document number
        purchaseOrder.DocumentNumber = await GenerateDocumentNumberAsync();
        purchaseOrder.Status = "Draft";
        purchaseOrder.CreatedAt = DateTime.UtcNow;
        purchaseOrder.IsActive = true;

        // Add purchase order
        await _purchaseOrderRepository.AddAsync(purchaseOrder);

        // Add details
        if (createPurchaseOrderDto.Details != null && createPurchaseOrderDto.Details.Any())
        {
            foreach (var detailDto in createPurchaseOrderDto.Details)
            {
                // Validate product
                var product = await _productRepository.GetByIdAsync(detailDto.ProductId);
                if (product == null)
                    throw new KeyNotFoundException($"Product with ID {detailDto.ProductId} not found.");

                var detail = _mapper.Map<PurchaseOrderDetail>(detailDto);
                detail.PurchaseOrderId = purchaseOrder.Id;
                detail.TotalPrice = detail.Quantity * detail.UnitPrice;
                detail.CreatedAt = DateTime.UtcNow;
                detail.IsActive = true;

                await _purchaseOrderDetailRepository.AddAsync(detail);
            }
        }

        // Calculate total amount
        await UpdatePurchaseOrderTotalAsync(purchaseOrder.Id);

        // Reload the purchase order with all details
        var createdPurchaseOrder = await _dbContext.PurchaseOrders
            .Include(po => po.Supplier)
            .Include(po => po.CostCenter)
            .Include(po => po.Details)
                .ThenInclude(d => d.Product)
            .FirstOrDefaultAsync(po => po.Id == purchaseOrder.Id);

        return _mapper.Map<PurchaseOrderDto>(createdPurchaseOrder);
    }

    public async Task UpdatePurchaseOrderAsync(UpdatePurchaseOrderDto updatePurchaseOrderDto)
    {
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(updatePurchaseOrderDto.Id);
        if (purchaseOrder == null)
            throw new KeyNotFoundException($"Purchase Order with ID {updatePurchaseOrderDto.Id} not found.");

        // Only allow updates for Draft or Pending status
        if (purchaseOrder.Status != "Draft" && purchaseOrder.Status != "Pending")
            throw new InvalidOperationException($"Cannot update purchase order with status '{purchaseOrder.Status}'.");

        // Validate supplier
        var supplier = await _supplierRepository.GetByIdAsync(updatePurchaseOrderDto.SupplierId);
        if (supplier == null)
            throw new KeyNotFoundException($"Supplier with ID {updatePurchaseOrderDto.SupplierId} not found.");

        // Validate cost center
        var costCenter = await _costCenterRepository.GetByIdAsync(updatePurchaseOrderDto.CostCenterId);
        if (costCenter == null)
            throw new KeyNotFoundException($"Cost Center with ID {updatePurchaseOrderDto.CostCenterId} not found.");

        // Update purchase order
        _mapper.Map(updatePurchaseOrderDto, purchaseOrder);
        purchaseOrder.UpdatedAt = DateTime.UtcNow;

        await _purchaseOrderRepository.UpdateAsync(purchaseOrder);
    }

    public async Task DeletePurchaseOrderAsync(int id)
    {
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(id);
        if (purchaseOrder == null)
            throw new KeyNotFoundException($"Purchase Order with ID {id} not found.");

        // Only allow deletion for Draft status
        if (purchaseOrder.Status != "Draft")
            throw new InvalidOperationException($"Cannot delete purchase order with status '{purchaseOrder.Status}'.");

        // Delete details first
        var details = await _dbContext.PurchaseOrderDetails
            .Where(d => d.PurchaseOrderId == id)
            .ToListAsync();

        foreach (var detail in details)
        {
            await _purchaseOrderDetailRepository.DeleteAsync(detail);
        }

        // Delete purchase order
        await _purchaseOrderRepository.DeleteAsync(purchaseOrder);
    }

    public async Task ApprovePurchaseOrderAsync(int id)
    {
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(id);
        if (purchaseOrder == null)
            throw new KeyNotFoundException($"Purchase Order with ID {id} not found.");

        // Only allow approval for Pending status
        if (purchaseOrder.Status != "Pending")
            throw new InvalidOperationException($"Cannot approve purchase order with status '{purchaseOrder.Status}'.");

        purchaseOrder.Status = "Approved";
        purchaseOrder.UpdatedAt = DateTime.UtcNow;

        await _purchaseOrderRepository.UpdateAsync(purchaseOrder);
    }

    public async Task RejectPurchaseOrderAsync(int id, string reason)
    {
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(id);
        if (purchaseOrder == null)
            throw new KeyNotFoundException($"Purchase Order with ID {id} not found.");

        // Only allow rejection for Pending status
        if (purchaseOrder.Status != "Pending")
            throw new InvalidOperationException($"Cannot reject purchase order with status '{purchaseOrder.Status}'.");

        purchaseOrder.Status = "Rejected";
        purchaseOrder.Notes = string.IsNullOrEmpty(purchaseOrder.Notes)
            ? $"Rejected: {reason}"
            : $"{purchaseOrder.Notes}\nRejected: {reason}";
        purchaseOrder.UpdatedAt = DateTime.UtcNow;

        await _purchaseOrderRepository.UpdateAsync(purchaseOrder);
    }

    public async Task CancelPurchaseOrderAsync(int id, string reason)
    {
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(id);
        if (purchaseOrder == null)
            throw new KeyNotFoundException($"Purchase Order with ID {id} not found.");

        // Only allow cancellation for Draft or Approved status
        if (purchaseOrder.Status != "Draft" && purchaseOrder.Status != "Approved")
            throw new InvalidOperationException($"Cannot cancel purchase order with status '{purchaseOrder.Status}'.");

        purchaseOrder.Status = "Cancelled";
        purchaseOrder.Notes = string.IsNullOrEmpty(purchaseOrder.Notes)
            ? $"Cancelled: {reason}"
            : $"{purchaseOrder.Notes}\nCancelled: {reason}";
        purchaseOrder.UpdatedAt = DateTime.UtcNow;

        await _purchaseOrderRepository.UpdateAsync(purchaseOrder);
    }

    public async Task<PurchaseOrderDetailDto> AddPurchaseOrderDetailAsync(CreatePurchaseOrderDetailDto createDetailDto)
    {
        // Validate purchase order
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(createDetailDto.PurchaseOrderId);
        if (purchaseOrder == null)
            throw new KeyNotFoundException($"Purchase Order with ID {createDetailDto.PurchaseOrderId} not found.");

        // Only allow adding details for Draft status
        if (purchaseOrder.Status != "Draft")
            throw new InvalidOperationException($"Cannot add details to purchase order with status '{purchaseOrder.Status}'.");

        // Validate product
        var product = await _productRepository.GetByIdAsync(createDetailDto.ProductId);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {createDetailDto.ProductId} not found.");

        // Create detail
        var detail = _mapper.Map<PurchaseOrderDetail>(createDetailDto);
        detail.TotalPrice = detail.Quantity * detail.UnitPrice;
        detail.CreatedAt = DateTime.UtcNow;
        detail.IsActive = true;

        await _purchaseOrderDetailRepository.AddAsync(detail);

        // Update purchase order total
        await UpdatePurchaseOrderTotalAsync(purchaseOrder.Id);

        // Reload the detail with product
        var createdDetail = await _dbContext.PurchaseOrderDetails
            .Include(d => d.Product)
            .FirstOrDefaultAsync(d => d.Id == detail.Id);

        return _mapper.Map<PurchaseOrderDetailDto>(createdDetail);
    }

    public async Task UpdatePurchaseOrderDetailAsync(UpdatePurchaseOrderDetailDto updateDetailDto)
    {
        var detail = await _purchaseOrderDetailRepository.GetByIdAsync(updateDetailDto.Id);
        if (detail == null)
            throw new KeyNotFoundException($"Purchase Order Detail with ID {updateDetailDto.Id} not found.");

        // Get purchase order
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(detail.PurchaseOrderId);
        if (purchaseOrder == null)
            throw new KeyNotFoundException($"Purchase Order with ID {detail.PurchaseOrderId} not found.");

        // Only allow updating details for Draft status
        if (purchaseOrder.Status != "Draft")
            throw new InvalidOperationException($"Cannot update details of purchase order with status '{purchaseOrder.Status}'.");

        // Update detail
        _mapper.Map(updateDetailDto, detail);
        detail.TotalPrice = detail.Quantity * detail.UnitPrice;

        await _purchaseOrderDetailRepository.UpdateAsync(detail);

        // Update purchase order total
        await UpdatePurchaseOrderTotalAsync(purchaseOrder.Id);
    }

    public async Task DeletePurchaseOrderDetailAsync(int id)
    {
        var detail = await _purchaseOrderDetailRepository.GetByIdAsync(id);
        if (detail == null)
            throw new KeyNotFoundException($"Purchase Order Detail with ID {id} not found.");

        // Get purchase order
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(detail.PurchaseOrderId);
        if (purchaseOrder == null)
            throw new KeyNotFoundException($"Purchase Order with ID {detail.PurchaseOrderId} not found.");

        // Only allow deleting details for Draft status
        if (purchaseOrder.Status != "Draft")
            throw new InvalidOperationException($"Cannot delete details of purchase order with status '{purchaseOrder.Status}'.");

        // Delete detail
        await _purchaseOrderDetailRepository.DeleteAsync(detail);

        // Update purchase order total
        await UpdatePurchaseOrderTotalAsync(purchaseOrder.Id);
    }

    private async Task<string> GenerateDocumentNumberAsync()
    {
        // Get the current year and month
        var now = DateTime.UtcNow;
        var year = now.Year;
        var month = now.Month;

        // Get the count of purchase orders for the current year and month
        var count = await _dbContext.PurchaseOrders
            .Where(po => po.CreatedAt.Year == year && po.CreatedAt.Month == month)
            .CountAsync();

        // Generate document number in format: PO-YYYYMM-XXXX
        return $"PO-{year}{month:D2}-{count + 1:D4}";
    }

    private async Task UpdatePurchaseOrderTotalAsync(int purchaseOrderId)
    {
        var details = await _dbContext.PurchaseOrderDetails
            .Where(d => d.PurchaseOrderId == purchaseOrderId)
            .ToListAsync();

        var totalAmount = details.Sum(d => d.TotalPrice);

        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(purchaseOrderId);
        if (purchaseOrder != null)
        {
            purchaseOrder.TotalAmount = totalAmount;
            await _purchaseOrderRepository.UpdateAsync(purchaseOrder);
        }
    }
}
