using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class BarcodesController : ApiControllerBase
{
    private readonly IBarcodeService _barcodeService;

    public BarcodesController(IBarcodeService barcodeService)
    {
        _barcodeService = barcodeService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<BarcodeDto>>> GetAll()
    {
        var barcodes = await _barcodeService.GetAllBarcodesAsync();
        return Ok(barcodes);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<BarcodeDto>> GetById(int id)
    {
        var barcode = await _barcodeService.GetBarcodeByIdAsync(id);
        if (barcode == null)
            return NotFound();

        return Ok(barcode);
    }

    [HttpGet("value/{value}")]
    public async Task<ActionResult<BarcodeDto>> GetByValue(string value)
    {
        var barcode = await _barcodeService.GetBarcodeByValueAsync(value);
        if (barcode == null)
            return NotFound();

        return Ok(barcode);
    }

    [HttpGet("product/{productId}")]
    public async Task<ActionResult<IEnumerable<BarcodeDto>>> GetByProductId(int productId)
    {
        var barcodes = await _barcodeService.GetBarcodesByProductIdAsync(productId);
        return Ok(barcodes);
    }

    [HttpGet("product/{productId}/primary")]
    public async Task<ActionResult<BarcodeDto>> GetPrimaryByProductId(int productId)
    {
        var barcode = await _barcodeService.GetPrimaryBarcodeByProductIdAsync(productId);
        if (barcode == null)
            return NotFound();

        return Ok(barcode);
    }

    [HttpPost]
    public async Task<ActionResult<BarcodeDto>> Create(CreateBarcodeDto createBarcodeDto)
    {
        try
        {
            var barcode = await _barcodeService.CreateBarcodeAsync(createBarcodeDto);
            return CreatedAtAction(nameof(GetById), new { id = barcode.Id }, barcode);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateBarcodeDto updateBarcodeDto)
    {
        if (id != updateBarcodeDto.Id)
            return BadRequest();

        try
        {
            await _barcodeService.UpdateBarcodeAsync(updateBarcodeDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _barcodeService.DeleteBarcodeAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }
}
