using Mapster;
using MapsterMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SCM.Application.Services
{
    public class LocationService : ILocationService
    {
        private readonly IRepository<Location> _locationRepository;
        private readonly ApplicationDbContext _dbContext;
        private readonly IMapper _mapper;

        public LocationService(IRepository<Location> locationRepository, ApplicationDbContext dbContext, IMapper mapper)
        {
            _locationRepository = locationRepository ?? throw new ArgumentNullException(nameof(locationRepository));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public async Task<IEnumerable<LocationDto>> GetAllLocationsAsync()
        {
            var locations = await _locationRepository.GetAllAsync();
            return locations.Adapt<IEnumerable<LocationDto>>();
        }

        public async Task<LocationDto?> GetLocationByIdAsync(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            return location?.Adapt<LocationDto>();
        }

        public async Task<IEnumerable<LocationDto>> GetLocationsByStoreIdAsync(int storeId)
        {
            var locations = await _dbContext.Locations
                .Where(l => l.StoreId == storeId)
                .ToListAsync();

            return locations.Adapt<IEnumerable<LocationDto>>();
        }

        public async Task<LocationDto?> GetLocationWithCostCentersAsync(int id)
        {
            var location = await _dbContext.Locations
                .Include(l => l.CostCenters)
                .FirstOrDefaultAsync(l => l.Id == id);

            return location?.Adapt<LocationDto>();
        }

        public async Task<LocationDto> CreateLocationAsync(CreateLocationDto createLocationDto)
        {
            var location = createLocationDto.Adapt<Location>();
            location.CreatedAt = DateTime.UtcNow;
            location.IsActive = true;

            await _locationRepository.AddAsync(location);

            return location.Adapt<LocationDto>();
        }

        public async Task UpdateLocationAsync(UpdateLocationDto updateLocationDto)
        {
            var location = await _locationRepository.GetByIdAsync(updateLocationDto.Id);
            if (location == null)
                throw new KeyNotFoundException($"Location with ID {updateLocationDto.Id} not found.");

            updateLocationDto.Adapt(location);
            location.UpdatedAt = DateTime.UtcNow;

            await _locationRepository.UpdateAsync(location);
        }

        public async Task DeleteLocationAsync(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location == null)
                throw new KeyNotFoundException($"Location with ID {id} not found.");

            await _locationRepository.DeleteAsync(location);
        }
    }
}
