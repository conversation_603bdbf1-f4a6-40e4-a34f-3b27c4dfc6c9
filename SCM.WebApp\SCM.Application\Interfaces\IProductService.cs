using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IProductService
{
    Task<IEnumerable<ProductDto>> GetAllProductsAsync();
    Task<ProductDto?> GetProductByIdAsync(int id);
    Task<ProductDto?> GetProductByCodeAsync(string code);
    Task<IEnumerable<ProductDto>> GetProductsByDepartmentIdAsync(int departmentId);
    Task<IEnumerable<ProductDto>> GetProductsByGroupIdAsync(int groupId);
    Task<IEnumerable<ProductDto>> GetProductsBySubGroupIdAsync(int subGroupId);
    Task<IEnumerable<ProductDto>> SearchProductsAsync(string searchTerm);
    Task<ProductDto> CreateProductAsync(CreateProductDto createProductDto);
    Task UpdateProductAsync(UpdateProductDto updateProductDto);
    Task DeleteProductAsync(int id);
}
