using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class ProductSubGroupsController : ApiControllerBase
{
    private readonly IProductSubGroupService _productSubGroupService;

    public ProductSubGroupsController(IProductSubGroupService productSubGroupService)
    {
        _productSubGroupService = productSubGroupService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ProductSubGroupDto>>> GetAll()
    {
        var productSubGroups = await _productSubGroupService.GetAllProductSubGroupsAsync();
        return Ok(productSubGroups);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ProductSubGroupDto>> GetById(int id)
    {
        var productSubGroup = await _productSubGroupService.GetProductSubGroupByIdAsync(id);
        if (productSubGroup == null)
            return NotFound();

        return Ok(productSubGroup);
    }

    [HttpGet("group/{groupId}")]
    public async Task<ActionResult<IEnumerable<ProductSubGroupDto>>> GetByGroupId(int groupId)
    {
        var productSubGroups = await _productSubGroupService.GetProductSubGroupsByGroupIdAsync(groupId);
        return Ok(productSubGroups);
    }

    [HttpPost]
    public async Task<ActionResult<ProductSubGroupDto>> Create(CreateProductSubGroupDto createProductSubGroupDto)
    {
        var productSubGroup = await _productSubGroupService.CreateProductSubGroupAsync(createProductSubGroupDto);
        return CreatedAtAction(nameof(GetById), new { id = productSubGroup.Id }, productSubGroup);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateProductSubGroupDto updateProductSubGroupDto)
    {
        if (id != updateProductSubGroupDto.Id)
            return BadRequest();

        try
        {
            await _productSubGroupService.UpdateProductSubGroupAsync(updateProductSubGroupDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _productSubGroupService.DeleteProductSubGroupAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
