<div class="page-container">
  <div class="page-header">
    <h1>Users</h1>
    <button mat-raised-button color="primary" (click)="addUser()">
      <mat-icon>add</mat-icon> Add User
    </button>
  </div>
  
  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by username, name or email">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Role</mat-label>
          <mat-select [(ngModel)]="selectedRole" (selectionChange)="applyFilter()">
            <mat-option value="">All Roles</mat-option>
            <mat-option *ngFor="let role of roles" [value]="role">
              {{role}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilter()">
            <mat-option value="">All Statuses</mat-option>
            <mat-option *ngFor="let status of statuses" [value]="status">
              {{status}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>
  
  <div class="table-container mat-elevation-z2">
    <table mat-table [dataSource]="filteredUsers" class="user-table">
      <!-- ID Column -->
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef>ID</th>
        <td mat-cell *matCellDef="let user">{{user.id}}</td>
      </ng-container>
      
      <!-- Username Column -->
      <ng-container matColumnDef="username">
        <th mat-header-cell *matHeaderCellDef>Username</th>
        <td mat-cell *matCellDef="let user">{{user.username}}</td>
      </ng-container>
      
      <!-- Full Name Column -->
      <ng-container matColumnDef="fullName">
        <th mat-header-cell *matHeaderCellDef>Full Name</th>
        <td mat-cell *matCellDef="let user">{{user.fullName}}</td>
      </ng-container>
      
      <!-- Email Column -->
      <ng-container matColumnDef="email">
        <th mat-header-cell *matHeaderCellDef>Email</th>
        <td mat-cell *matCellDef="let user">{{user.email}}</td>
      </ng-container>
      
      <!-- Role Column -->
      <ng-container matColumnDef="role">
        <th mat-header-cell *matHeaderCellDef>Role</th>
        <td mat-cell *matCellDef="let user">
          <mat-chip-option selected disableRipple>{{user.role}}</mat-chip-option>
        </td>
      </ng-container>
      
      <!-- Department Column -->
      <ng-container matColumnDef="department">
        <th mat-header-cell *matHeaderCellDef>Department</th>
        <td mat-cell *matCellDef="let user">{{user.department}}</td>
      </ng-container>
      
      <!-- Last Login Column -->
      <ng-container matColumnDef="lastLogin">
        <th mat-header-cell *matHeaderCellDef>Last Login</th>
        <td mat-cell *matCellDef="let user">{{user.lastLogin | date:'medium' || 'Never'}}</td>
      </ng-container>
      
      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let user" [ngClass]="{'status-active': user.status === 'Active', 'status-inactive': user.status === 'Inactive', 'status-locked': user.status === 'Locked'}">
          {{user.status}}
        </td>
      </ng-container>
      
      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let user">
          <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="viewUserDetails(user)">
              <mat-icon>visibility</mat-icon>
              <span>View Details</span>
            </button>
            <button mat-menu-item (click)="editUser(user)">
              <mat-icon>edit</mat-icon>
              <span>Edit</span>
            </button>
            <button mat-menu-item (click)="resetPassword(user)">
              <mat-icon>lock_reset</mat-icon>
              <span>Reset Password</span>
            </button>
            <button mat-menu-item (click)="toggleUserStatus(user)">
              <mat-icon>{{ user.status === 'Active' ? 'toggle_off' : 'toggle_on' }}</mat-icon>
              <span>{{ user.status === 'Active' ? 'Deactivate' : 'Activate' }}</span>
            </button>
            <button mat-menu-item (click)="deleteUser(user)">
              <mat-icon>delete</mat-icon>
              <span>Delete</span>
            </button>
          </mat-menu>
        </td>
      </ng-container>
      
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
    
    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
