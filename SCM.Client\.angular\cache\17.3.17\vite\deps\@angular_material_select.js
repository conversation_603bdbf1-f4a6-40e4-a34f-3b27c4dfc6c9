import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  matSelectAnimations
} from "./chunk-2I657DLY.js";
import "./chunk-5742JUA3.js";
import "./chunk-BZPXKLBF.js";
import "./chunk-2OG7VG7X.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ield,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>abel,
  MatPrefix,
  MatSuffix
} from "./chunk-KMQJ76RC.js";
import "./chunk-26JACEMX.js";
import "./chunk-7JBOIV3T.js";
import "./chunk-HTZ36MZ2.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-26U3ZGYY.js";
import "./chunk-XTHKPVIX.js";
import "./chunk-APQJ6POP.js";
import "./chunk-IGJZNA3K.js";
import "./chunk-CONQKHOI.js";
import "./chunk-V4GYEGQC.js";
import "./chunk-GC5FLHL6.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
