.page-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.content-container {
  display: flex;
  gap: 20px;
}

.form-section {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
}

.table-section {
  flex: 2;
}

.category-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.table-container {
  overflow-x: auto;
}

.category-table {
  width: 100%;
}

.mat-mdc-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-container {
    flex-direction: column;
  }
  
  .form-section {
    max-width: none;
  }
}
