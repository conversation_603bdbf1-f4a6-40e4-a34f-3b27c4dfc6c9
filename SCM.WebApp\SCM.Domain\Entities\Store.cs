using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Store : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public string? PostalCode { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? ContactPerson { get; set; }
    public int? LocationId { get; set; }

    // Navigation properties
    public virtual ICollection<Location> Locations { get; set; } = new List<Location>();
    public virtual ICollection<CostCenter> CostCenters { get; set; } = new List<CostCenter>();
}
