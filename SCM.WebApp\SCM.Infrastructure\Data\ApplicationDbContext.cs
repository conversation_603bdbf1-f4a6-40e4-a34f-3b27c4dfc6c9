using Microsoft.EntityFrameworkCore;
using SCM.Domain.Entities;

namespace SCM.Infrastructure.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    // Core entities
    public DbSet<Department> Departments { get; set; } = null!;
    public DbSet<ProductGroup> ProductGroups { get; set; } = null!;
    public DbSet<ProductSubGroup> ProductSubGroups { get; set; } = null!;
    public DbSet<UnitGroup> UnitGroups { get; set; } = null!;
    public DbSet<Unit> Units { get; set; } = null!;
    public DbSet<Brand> Brands { get; set; } = null!;
    public DbSet<Tax> Taxes { get; set; } = null!;
    public DbSet<Product> Products { get; set; } = null!;
    public DbSet<Barcode> Barcodes { get; set; } = null!;

    // Location entities
    public DbSet<Store> Stores { get; set; } = null!;
    public DbSet<Location> Locations { get; set; } = null!;
    public DbSet<CostCenterType> CostCenterTypes { get; set; } = null!;
    public DbSet<CostCenter> CostCenters { get; set; } = null!;
    public DbSet<ProductCostCenterLink> ProductCostCenterLinks { get; set; } = null!;

    // Inventory entities
    public DbSet<StockOnHand> StockOnHand { get; set; } = null!;
    public DbSet<Batch> Batches { get; set; } = null!;
    public DbSet<StockTakeHeader> StockTakeHeaders { get; set; } = null!;
    public DbSet<StockTakeDetail> StockTakeDetails { get; set; } = null!;

    // Transaction entities
    public DbSet<TransactionType> TransactionTypes { get; set; } = null!;
    public DbSet<TransactionProcess> TransactionProcesses { get; set; } = null!;
    public DbSet<TransactionStageType> TransactionStageTypes { get; set; } = null!;
    public DbSet<TransactionHeader> TransactionHeaders { get; set; } = null!;
    public DbSet<TransactionDetail> TransactionDetails { get; set; } = null!;
    public DbSet<TransactionStage> TransactionStages { get; set; } = null!;
    public DbSet<PurchaseOrder> PurchaseOrders { get; set; } = null!;
    public DbSet<PurchaseOrderDetail> PurchaseOrderDetails { get; set; } = null!;

    // Recipe entities
    public DbSet<Recipe> Recipes { get; set; } = null!;
    public DbSet<RecipeIngredient> RecipeIngredients { get; set; } = null!;

    // User entities
    public DbSet<Role> Roles { get; set; } = null!;
    public DbSet<User> Users { get; set; } = null!;
    public DbSet<UserCostCenterAccess> UserCostCenterAccesses { get; set; } = null!;
    public DbSet<UserPreference> UserPreferences { get; set; } = null!;
    public DbSet<ApiKey> ApiKeys { get; set; } = null!;

    // Sales entities
    public DbSet<PaymentMethod> PaymentMethods { get; set; } = null!;
    public DbSet<PaymentDetail> PaymentDetails { get; set; } = null!;
    public DbSet<Currency> Currencies { get; set; } = null!;
    public DbSet<Shift> Shifts { get; set; } = null!;
    public DbSet<Customer> Customers { get; set; } = null!;

    // Other entities
    public DbSet<Supplier> Suppliers { get; set; } = null!;
    public DbSet<DocumentStorage> DocumentStorages { get; set; } = null!;
    public DbSet<AuditLog> AuditLogs { get; set; } = null!;
    public DbSet<Notification> Notifications { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure entity relationships and constraints
        ConfigureCore(modelBuilder);
        ConfigureInventory(modelBuilder);
        ConfigureTransactions(modelBuilder);
        ConfigurePurchaseOrders(modelBuilder);
        ConfigureUsers(modelBuilder);
        ConfigureSales(modelBuilder);
    }

    private void ConfigureCore(ModelBuilder modelBuilder)
    {
        // Department
        modelBuilder.Entity<Department>(entity =>
        {
            entity.ToTable("Department");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("DepartmentId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(d => d.Name).IsUnique();
        });

        // ProductGroup
        modelBuilder.Entity<ProductGroup>(entity =>
        {
            entity.ToTable("ProductGroup");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("GroupId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.DepartmentId).HasColumnName("DepartmentId");
            entity.HasIndex(g => g.Name).IsUnique();
        });

        // ProductSubGroup
        modelBuilder.Entity<ProductSubGroup>(entity =>
        {
            entity.ToTable("ProductSubGroup");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("SubGroupId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.GroupId).HasColumnName("GroupId");
            entity.HasIndex(sg => sg.Name).IsUnique();
        });

        // UnitGroup
        modelBuilder.Entity<UnitGroup>(entity =>
        {
            entity.ToTable("UnitGroup");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("UnitGroupId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.HasIndex(ug => ug.Name).IsUnique();
        });

        // Unit
        modelBuilder.Entity<Unit>(entity =>
        {
            entity.ToTable("Unit");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("UnitId");
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Abbreviation).HasMaxLength(10);
            entity.Property(e => e.UnitGroupId).HasColumnName("UnitGroupId");
            entity.Property(e => e.BaseConversionFactor).HasColumnType("decimal(18, 6)");
            entity.Property(e => e.ConversionFactor).HasColumnType("decimal(18, 6)").HasDefaultValue(1);
            entity.HasIndex(u => u.Name).IsUnique();
        });

        // Brand
        modelBuilder.Entity<Brand>(entity =>
        {
            entity.ToTable("Brand");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("BrandId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(b => b.Name).IsUnique();
        });

        // Tax
        modelBuilder.Entity<Tax>(entity =>
        {
            entity.ToTable("Tax");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TaxId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Rate).HasColumnType("decimal(5, 2)");
            entity.HasIndex(t => t.Name).IsUnique();
        });

        // Product
        modelBuilder.Entity<Product>(entity =>
        {
            entity.ToTable("Product");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("ProductId");
            entity.Property(e => e.Code).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.BrandId).HasColumnName("BrandId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.Property(e => e.UnitGroupId).HasColumnName("UnitGroupId");
            entity.Property(e => e.DepartmentId).HasColumnName("DepartmentId");
            entity.Property(e => e.GroupId).HasColumnName("GroupId");
            entity.Property(e => e.SubGroupId).HasColumnName("SubGroupId");
            entity.Property(e => e.TaxId).HasColumnName("TaxId");
            entity.Property(e => e.SalesUnitId).HasColumnName("SalesUnitId");
            entity.HasIndex(p => p.Code).IsUnique();
        });

        // Barcode
        modelBuilder.Entity<Barcode>(entity =>
        {
            entity.ToTable("Barcode");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("BarcodeId");
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.BarcodeValue).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.HasIndex(b => b.BarcodeValue).IsUnique();
        });
    }

    private void ConfigureInventory(ModelBuilder modelBuilder)
    {
        // Store
        modelBuilder.Entity<Store>(entity =>
        {
            entity.ToTable("Store");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StoreId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.LocationId).HasColumnName("LocationId");
            entity.HasIndex(s => s.Name).IsUnique();
        });

        // Location
        modelBuilder.Entity<Location>(entity =>
        {
            entity.ToTable("Location");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("LocationId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(l => l.Name).IsUnique();
        });

        // CostCenterType
        modelBuilder.Entity<CostCenterType>(entity =>
        {
            entity.ToTable("CostCenterType");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TypeId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.HasIndex(ct => ct.Name).IsUnique();
        });

        // CostCenter
        modelBuilder.Entity<CostCenter>(entity =>
        {
            entity.ToTable("CostCenter");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("CostCenterId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.StoreId).HasColumnName("StoreId");
            entity.Property(e => e.TypeId).HasColumnName("TypeId");
            entity.Property(e => e.TypeName).HasMaxLength(100);
            entity.Property(e => e.Abbreviation).HasMaxLength(50);
            entity.HasIndex(cc => cc.Name).IsUnique();
        });

        // ProductCostCenterLink
        modelBuilder.Entity<ProductCostCenterLink>(entity =>
        {
            entity.ToTable("ProductCostCenterLink");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("LinkId");
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.MinStock).HasColumnName("MinimumStock").HasColumnType("decimal(18, 4)");
            entity.Property(e => e.MaxStock).HasColumnName("MaximumStock").HasColumnType("decimal(18, 4)");
            entity.HasIndex(pcl => new { pcl.ProductId, pcl.CostCenterId }).IsUnique();
        });

        // Batch
        modelBuilder.Entity<Batch>(entity =>
        {
            entity.ToTable("Batch");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("BatchId");
            entity.Property(e => e.BatchNumber).HasMaxLength(150).IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.HasIndex(b => b.BatchNumber);
        });

        // StockOnHand
        modelBuilder.Entity<StockOnHand>(entity =>
        {
            entity.ToTable("StockOnHand");
            entity.HasKey(s => new { s.ProductId, s.CostCenterId, s.BatchId });
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.BatchId).HasColumnName("BatchId").IsRequired();
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.CostPrice).HasColumnType("decimal(18, 4)");
        });

        // StockTakeHeader
        modelBuilder.Entity<StockTakeHeader>(entity =>
        {
            entity.ToTable("StockTakeHeader");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockTakeId");
            entity.Property(e => e.ReferenceNumber).HasColumnName("StockTakeNumber").HasMaxLength(50).IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.StockTakeDate).HasColumnType("datetime2");
            entity.Property(e => e.Status).HasMaxLength(20);
            entity.Property(e => e.CreatedById).HasColumnName("CreatedById");
            entity.Property(e => e.CompletedById).HasColumnName("CompletedById");
            entity.Property(e => e.CompletedAt).HasColumnName("CompletedDate");
            entity.HasIndex(sth => sth.ReferenceNumber).IsUnique();

            // Configure relationships with User
            entity.HasOne(sth => sth.CreatedBy)
                .WithMany()
                .HasForeignKey(sth => sth.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(sth => sth.CompletedBy)
                .WithMany()
                .HasForeignKey(sth => sth.CompletedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // StockTakeDetail
        modelBuilder.Entity<StockTakeDetail>(entity =>
        {
            entity.ToTable("StockTakeDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockTakeDetailId");
            entity.Property(e => e.StockTakeHeaderId).HasColumnName("StockTakeId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.BatchId).HasColumnName("BatchId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId").IsRequired();
            entity.Property(e => e.SystemQuantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.CountedQuantity).HasColumnName("ActualQuantity").HasColumnType("decimal(18, 4)");
            entity.Property(e => e.Variance).HasColumnName("VarianceQuantity").HasColumnType("decimal(18, 4)");
        });
    }

    private void ConfigureTransactions(ModelBuilder modelBuilder)
    {
        // TransactionType
        modelBuilder.Entity<TransactionType>(entity =>
        {
            entity.ToTable("TransactionType");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TransactionTypeId");
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.Property(e => e.AffectsStock).HasColumnName("AffectsInventory");
            entity.HasIndex(tt => tt.Name).IsUnique();
        });

        // TransactionProcess
        modelBuilder.Entity<TransactionProcess>(entity =>
        {
            entity.ToTable("TransactionProcess");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("ProcessId");
            entity.Property(e => e.Name).HasColumnName("ProcessNumber").HasMaxLength(50).IsRequired();
            entity.HasIndex(tp => tp.Name).IsUnique();
        });

        // TransactionStageType
        modelBuilder.Entity<TransactionStageType>(entity =>
        {
            entity.ToTable("TransactionStageType");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StageTypeId");
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.HasIndex(tst => tst.Name).IsUnique();
        });

        // TransactionHeader
        modelBuilder.Entity<TransactionHeader>(entity =>
        {
            entity.ToTable("TransactionHeader");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TransactionId");
            entity.Property(e => e.ReferenceNumber).HasColumnName("TransactionNumber").HasMaxLength(50).IsRequired();
            entity.Property(e => e.TransactionTypeId).HasColumnName("TransactionTypeId").IsRequired();
            entity.Property(e => e.TransactionProcessId).HasColumnName("ProcessId");
            entity.Property(e => e.CostCenterId).HasColumnName("SourceCostCenterId");
            entity.Property(e => e.ToCostCenterId).HasColumnName("DestinationCostCenterId");
            entity.Property(e => e.Status).HasMaxLength(20);
            entity.HasIndex(th => th.ReferenceNumber).IsUnique();

            // Configure relationships with CostCenter
            entity.HasOne(th => th.CostCenter)
                .WithMany(cc => cc.TransactionHeaders)
                .HasForeignKey(th => th.CostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(th => th.ToCostCenter)
                .WithMany(cc => cc.TransactionHeadersTo)
                .HasForeignKey(th => th.ToCostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure relationships with User
            entity.HasOne(th => th.CreatedBy)
                .WithMany()
                .HasForeignKey(th => th.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(th => th.ApprovedBy)
                .WithMany()
                .HasForeignKey(th => th.ApprovedById)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(th => th.CompletedBy)
                .WithMany()
                .HasForeignKey(th => th.CompletedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // TransactionDetail
        modelBuilder.Entity<TransactionDetail>(entity =>
        {
            entity.ToTable("TransactionDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TransactionDetailId");
            entity.Property(e => e.TransactionHeaderId).HasColumnName("TransactionId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.BatchId).HasColumnName("BatchId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId").IsRequired();
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.UnitPrice).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.TaxId).HasColumnName("TaxId");
            entity.Property(e => e.TaxRate).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.TaxAmount).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.DiscountPercent).HasColumnName("DiscountPercentage").HasColumnType("decimal(5, 2)");
            entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.TotalAmount).HasColumnName("LineTotal").HasColumnType("decimal(18, 4)");
        });

        // Supplier
        modelBuilder.Entity<Supplier>(entity =>
        {
            entity.ToTable("Supplier");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("SupplierId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(s => s.Name).IsUnique();
        });
    }

    private void ConfigureUsers(ModelBuilder modelBuilder)
    {
        // Role
        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("Role");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("RoleId");
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.HasIndex(r => r.Name).IsUnique();
        });

        // User
        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("User");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("UserId");
            entity.Property(e => e.Username).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Email).HasMaxLength(150);
            entity.Property(e => e.PasswordHash).HasMaxLength(500).IsRequired();
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.Phone).HasColumnName("PhoneNumber").HasMaxLength(50);
            entity.Property(e => e.RoleId).HasColumnName("RoleId");
            entity.Property(e => e.LastLogin).HasColumnName("LastLoginDate");

            // Ignore properties that don't exist in the database
            entity.Ignore(e => e.IsAdmin);
            entity.Ignore(e => e.IsLocked);
            entity.Ignore(e => e.MustChangePassword);
            entity.Ignore(e => e.FailedLoginAttempts);
            entity.Ignore(e => e.Role);
            entity.HasIndex(u => u.Username).IsUnique();
            entity.HasIndex(u => u.Email).IsUnique();
        });

        // UserCostCenterAccess
        modelBuilder.Entity<UserCostCenterAccess>(entity =>
        {
            entity.ToTable("UserCostCenterAccess");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("AccessId");
            entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.HasIndex(ucca => new { ucca.UserId, ucca.CostCenterId }).IsUnique();
        });

        // UserPreference
        modelBuilder.Entity<UserPreference>(entity =>
        {
            entity.ToTable("UserPreference");
            entity.HasKey(e => e.PreferenceId);
            entity.Property(e => e.PreferenceId).HasColumnName("PreferenceId");
            entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired();
            entity.Property(e => e.PreferenceKey).HasMaxLength(100).IsRequired();
            entity.Property(e => e.PreferenceValue);
            entity.HasIndex(up => new { up.UserId, up.PreferenceKey }).IsUnique();
        });

        // ApiKey
        modelBuilder.Entity<ApiKey>(entity =>
        {
            entity.ToTable("ApiKey");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("ApiKeyId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.KeyValue).HasMaxLength(100).IsRequired();
            entity.Property(e => e.CreatedById).HasColumnName("CreatedById").IsRequired();
            entity.HasIndex(ak => ak.KeyValue).IsUnique();

            // Configure relationship with User
            entity.HasOne(ak => ak.CreatedBy)
                .WithMany()
                .HasForeignKey(ak => ak.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Notification
        modelBuilder.Entity<Notification>(entity =>
        {
            entity.ToTable("Notification");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("NotificationId");
            entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired();
            entity.Property(e => e.Title).HasMaxLength(150).IsRequired();
            entity.Property(e => e.NotificationType).HasMaxLength(50);

            // Configure relationship with User
            entity.HasOne(n => n.User)
                .WithMany()
                .HasForeignKey(n => n.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // AuditLog
        modelBuilder.Entity<AuditLog>(entity =>
        {
            entity.ToTable("AuditLog");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("AuditLogId");
            entity.Property(e => e.UserId).HasColumnName("UserId");
            entity.Property(e => e.Action).HasMaxLength(50).IsRequired();
            entity.Property(e => e.TableName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.RecordId).HasMaxLength(50).IsRequired();
            entity.Property(e => e.IpAddress).HasMaxLength(50);

            // Configure relationship with User
            entity.HasOne(al => al.User)
                .WithMany()
                .HasForeignKey(al => al.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // DocumentStorage
        modelBuilder.Entity<DocumentStorage>(entity =>
        {
            entity.ToTable("DocumentStorage");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("DocumentId");
            entity.Property(e => e.FileName).HasMaxLength(255).IsRequired();
            entity.Property(e => e.FileType).HasMaxLength(100).IsRequired();
            entity.Property(e => e.FileSize).HasColumnType("bigint").IsRequired();
            entity.Property(e => e.FilePath).HasMaxLength(500).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.UploadedById).HasColumnName("UploadedById").IsRequired();

            // Configure relationship with User
            entity.HasOne(ds => ds.UploadedBy)
                .WithMany()
                .HasForeignKey(ds => ds.UploadedById)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigurePurchaseOrders(ModelBuilder modelBuilder)
    {
        // PurchaseOrder
        modelBuilder.Entity<PurchaseOrder>(entity =>
        {
            entity.ToTable("PurchaseOrder");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PurchaseOrderId");
            entity.Property(e => e.DocumentNumber).HasMaxLength(50).IsRequired();
            entity.Property(e => e.SupplierId).HasColumnName("SupplierId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.OrderDate).HasColumnType("datetime2").IsRequired();
            entity.Property(e => e.ExpectedDeliveryDate).HasColumnType("datetime2");
            entity.Property(e => e.Status).HasMaxLength(20).IsRequired();
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime2").IsRequired();
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);
            entity.Property(e => e.UpdatedAt).HasColumnType("datetime2");
            entity.HasIndex(po => po.DocumentNumber).IsUnique();

            // Relationships
            entity.HasOne(po => po.Supplier)
                .WithMany()
                .HasForeignKey(po => po.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(po => po.CostCenter)
                .WithMany()
                .HasForeignKey(po => po.CostCenterId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // PurchaseOrderDetail
        modelBuilder.Entity<PurchaseOrderDetail>(entity =>
        {
            entity.ToTable("PurchaseOrderDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PurchaseOrderDetailId");
            entity.Property(e => e.PurchaseOrderId).HasColumnName("PurchaseOrderId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.UnitPrice).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.TotalPrice).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.Notes).HasMaxLength(500);

            // Relationships
            entity.HasOne(pod => pod.PurchaseOrder)
                .WithMany(po => po.Details)
                .HasForeignKey(pod => pod.PurchaseOrderId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(pod => pod.Product)
                .WithMany()
                .HasForeignKey(pod => pod.ProductId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureSales(ModelBuilder modelBuilder)
    {
        // PaymentMethod
        modelBuilder.Entity<PaymentMethod>(entity =>
        {
            entity.ToTable("PaymentMethod");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PaymentMethodId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(pm => pm.Name).IsUnique();
        });

        // PaymentDetail
        modelBuilder.Entity<PaymentDetail>(entity =>
        {
            entity.ToTable("Payment");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PaymentId");
            entity.Property(e => e.TransactionHeaderId).HasColumnName("TransactionId").IsRequired();
            entity.Property(e => e.PaymentMethodId).HasColumnName("PaymentMethodId").IsRequired();
            entity.Property(e => e.Amount).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.PaymentDate).HasDefaultValueSql("GETDATE()");
        });

        // Currency
        modelBuilder.Entity<Currency>(entity =>
        {
            entity.ToTable("Currency");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("CurrencyId");
            entity.Property(e => e.Code).HasMaxLength(3).IsRequired();
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Symbol).HasMaxLength(5);
            entity.Property(e => e.ExchangeRate).HasColumnType("decimal(18, 6)");
            entity.Property(e => e.IsDefault).HasColumnName("IsBaseCurrency");
            entity.HasIndex(c => c.Code).IsUnique();
        });

        // Shift
        modelBuilder.Entity<Shift>(entity =>
        {
            entity.ToTable("Shift");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("ShiftId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.StartTime).HasColumnType("time");
            entity.Property(e => e.EndTime).HasColumnType("time");
            entity.HasIndex(s => s.Name).IsUnique();
        });

        // Customer
        modelBuilder.Entity<Customer>(entity =>
        {
            entity.ToTable("Customer");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("CustomerId");
            entity.Property(e => e.CustomerCode).HasMaxLength(50);
            entity.Property(e => e.FirstName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.CompanyName).HasMaxLength(150);
            entity.Property(e => e.Email).HasMaxLength(150);
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.CreditLimit).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.DiscountPercentage).HasColumnType("decimal(5, 2)");
            entity.HasIndex(c => c.Email).IsUnique().HasFilter("[Email] IS NOT NULL");
        });
    }
}
