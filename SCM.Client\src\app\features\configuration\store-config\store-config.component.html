<div class="page-container">
  <div class="page-header">
    <h1>1 - Casa Mare Resort</h1>
    <div class="location-label">All Locations</div>
  </div>
  
  <div class="branch-details-header">
    <h2>BRANCH DETAILS</h2>
  </div>
  
  <form [formGroup]="storeForm" class="store-form">
    <div class="form-container">
      <div class="form-section">
        <h3>General</h3>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Branch No</mat-label>
            <input matInput formControlName="branchNo">
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Store ID</mat-label>
            <input matInput formControlName="storeId">
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Branch Name</mat-label>
            <input matInput formControlName="branchName">
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Region</mat-label>
            <mat-select formControlName="region">
              <mat-option *ngFor="let region of regions" [value]="region">
                {{region}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Store Type</mat-label>
            <mat-select formControlName="storeType">
              <mat-option *ngFor="let type of storeTypes" [value]="type">
                {{type}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Inhouse Barcode Prefix</mat-label>
            <input matInput formControlName="inHouseBarcodePrefix">
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Document Prefix</mat-label>
            <input matInput formControlName="documentPrefix">
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Tax Rate</mat-label>
            <mat-select formControlName="taxRate">
              <mat-option *ngFor="let rate of taxRates" [value]="rate">
                {{rate}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Selling Price Target</mat-label>
            <mat-select formControlName="sellingPriceTarget">
              <mat-option *ngFor="let target of sellingPriceTargets" [value]="target">
                {{target}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      
      <div class="form-section">
        <div class="image-upload-container">
          <div class="image-placeholder">
            <span>Click to add Picture</span>
            <button mat-icon-button color="primary" (click)="uploadImage()">
              <mat-icon>add_a_photo</mat-icon>
            </button>
          </div>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Address</mat-label>
            <textarea matInput formControlName="address" rows="5"></textarea>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Branch Export Path</mat-label>
            <input matInput formControlName="branchExportPath">
            <button mat-icon-button matSuffix>
              <mat-icon>folder</mat-icon>
            </button>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Scale Export Path</mat-label>
            <input matInput formControlName="scaleExportPath">
            <button mat-icon-button matSuffix>
              <mat-icon>folder</mat-icon>
            </button>
          </mat-form-field>
        </div>
      </div>
    </div>
    
    <div class="form-actions">
      <button mat-raised-button color="primary" (click)="saveStore()" [disabled]="storeForm.invalid">
        Settings
      </button>
      <button mat-raised-button color="warn" (click)="deleteStore()">
        Delete
      </button>
    </div>
  </form>
</div>
