export interface ProductSubGroup {
  id: number;
  name: string;
  description?: string;
  groupId: number;
  groupName?: string;
  departmentId?: number;
  departmentName?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateProductSubGroup {
  name: string;
  description?: string;
  groupId: number;
}

export interface UpdateProductSubGroup {
  id: number;
  name: string;
  description?: string;
  groupId: number;
  isActive: boolean;
}
