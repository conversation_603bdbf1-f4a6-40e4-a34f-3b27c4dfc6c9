using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using SCM.API.Models;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace SCM.API.Controllers;

public class AuthController : ApiControllerBase
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthController> _logger;

    public AuthController(
        ApplicationDbContext dbContext,
        IConfiguration configuration,
        ILogger<AuthController> logger)
    {
        _dbContext = dbContext;
        _configuration = configuration;
        _logger = logger;
    }

    [HttpPost("login")]
    public async Task<ActionResult<AuthResponse>> Login(LoginRequest request)
    {
        try
        {
            // Validate request
            if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest("Username and password are required");
            }

            // Find user by username
            var user = await _dbContext.Users
                .FirstOrDefaultAsync(u => u.Username == request.Username && u.IsActive);

            // Get user role if needed
            string roleName = "User";
            if (user != null && user.RoleId.HasValue)
            {
                var role = await _dbContext.Roles.FirstOrDefaultAsync(r => r.Id == user.RoleId);
                roleName = role?.Name ?? "User";
            }

            if (user == null)
            {
                _logger.LogWarning("Login attempt with invalid username: {Username}", request.Username);
                return Unauthorized("Invalid username or password");
            }

            // Verify password
            if (!VerifyPasswordHash(request.Password, user.PasswordHash))
            {
                _logger.LogWarning("Login attempt with invalid password for user: {Username}", request.Username);
                return Unauthorized("Invalid username or password");
            }

            // Update last login date
            user.LastLogin = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            // Generate JWT token
            var token = GenerateJwtToken(user, roleName);

            // Return response
            return Ok(new AuthResponse
            {
                Token = token,
                UserId = user.Id,
                Username = user.Username,
                FullName = $"{user.FirstName} {user.LastName}".Trim(),
                Role = roleName,
                IsAdmin = false
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login attempt");
            return StatusCode(500, "An error occurred during login");
        }
    }

    [HttpPost("register")]
    public async Task<ActionResult<AuthResponse>> Register(RegisterRequest request)
    {
        try
        {
            // Validate request
            if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest("Username and password are required");
            }

            // Check if username already exists
            if (await _dbContext.Users.AnyAsync(u => u.Username == request.Username))
            {
                return BadRequest("Username already exists");
            }

            // Check if email already exists (if provided)
            if (!string.IsNullOrEmpty(request.Email) && await _dbContext.Users.AnyAsync(u => u.Email == request.Email))
            {
                return BadRequest("Email already exists");
            }

            // Get default role (or user role)
            var userRole = await _dbContext.Roles.FirstOrDefaultAsync(r => r.Name == "User")
                ?? await _dbContext.Roles.FirstOrDefaultAsync();

            // Create new user
            var user = new User
            {
                Username = request.Username,
                Email = request.Email ?? string.Empty,
                PasswordHash = HashPassword(request.Password),
                FirstName = request.FirstName ?? string.Empty,
                LastName = request.LastName ?? string.Empty,
                RoleId = userRole?.Id,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.Users.Add(user);
            await _dbContext.SaveChangesAsync();

            // Get role name
            string roleName = userRole?.Name ?? "User";

            // Generate JWT token
            var token = GenerateJwtToken(user, roleName);

            // Return response
            return Ok(new AuthResponse
            {
                Token = token,
                UserId = user.Id,
                Username = user.Username,
                FullName = $"{user.FirstName} {user.LastName}".Trim(),
                Role = roleName,
                IsAdmin = false
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during registration");
            return StatusCode(500, "An error occurred during registration");
        }
    }

    private string GenerateJwtToken(User user, string roleName)
    {
        var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"] ?? "DefaultSecretKeyForDevelopment12345678901234");
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email ?? string.Empty),
                new Claim(ClaimTypes.Role, roleName)
            }),
            Expires = DateTime.UtcNow.AddDays(7),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
            Issuer = _configuration["Jwt:Issuer"],
            Audience = _configuration["Jwt:Audience"]
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    private string HashPassword(string password)
    {
        using var hmac = new HMACSHA512();
        var salt = hmac.Key;
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(password));

        // Combine salt and hash
        var hashBytes = new byte[salt.Length + hash.Length];
        Array.Copy(salt, 0, hashBytes, 0, salt.Length);
        Array.Copy(hash, 0, hashBytes, salt.Length, hash.Length);

        return Convert.ToBase64String(hashBytes);
    }

    private bool VerifyPasswordHash(string password, string storedHash)
    {
        try
        {
            // Check if storedHash is null or empty
            if (string.IsNullOrEmpty(storedHash))
            {
                _logger.LogWarning("Stored password hash is null or empty");
                return false;
            }

            // Try to convert from Base64
            byte[] hashBytes;
            try
            {
                hashBytes = Convert.FromBase64String(storedHash);
            }
            catch (FormatException ex)
            {
                _logger.LogWarning("Invalid Base64 format in stored password hash: {Error}", ex.Message);
                return false;
            }

            // Validate hash length (should be at least 64 bytes for salt + 64 bytes for hash)
            if (hashBytes.Length < 128)
            {
                _logger.LogWarning("Stored password hash has invalid length: {Length}", hashBytes.Length);
                return false;
            }

            // Extract salt (first 64 bytes)
            var salt = new byte[64];
            Array.Copy(hashBytes, 0, salt, 0, salt.Length);

            // Compute hash with the same salt
            using var hmac = new HMACSHA512(salt);
            var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(password));

            // Compare computed hash with stored hash
            for (int i = 0; i < computedHash.Length; i++)
            {
                if (hashBytes[salt.Length + i] != computedHash[i])
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during password verification");
            return false;
        }
    }

    /// <summary>
    /// Checks if a stored password hash is valid Base64 format
    /// </summary>
    private static bool IsValidPasswordHash(string storedHash)
    {
        if (string.IsNullOrEmpty(storedHash))
            return false;

        try
        {
            var hashBytes = Convert.FromBase64String(storedHash);
            return hashBytes.Length >= 128; // Should be at least 64 bytes salt + 64 bytes hash
        }
        catch (FormatException)
        {
            return false;
        }
    }

    /// <summary>
    /// Diagnostic endpoint to check for users with invalid password hashes
    /// </summary>
    [HttpGet("check-password-hashes")]
    public async Task<ActionResult> CheckPasswordHashes()
    {
        try
        {
            var users = await _dbContext.Users.ToListAsync();
            var invalidUsers = users.Where(u => !IsValidPasswordHash(u.PasswordHash)).ToList();

            var result = new
            {
                TotalUsers = users.Count,
                InvalidPasswordHashes = invalidUsers.Count,
                InvalidUsernames = invalidUsers.Select(u => u.Username).ToList()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking password hashes");
            return StatusCode(500, "An error occurred while checking password hashes");
        }
    }

    /// <summary>
    /// Updates a user's password hash to the correct format
    /// This can be used for migrating users with invalid password hashes
    /// </summary>
    [HttpPost("reset-password-hash/{userId}")]
    public async Task<ActionResult> ResetPasswordHash(int userId, [FromBody] string newPassword)
    {
        try
        {
            // This endpoint should be protected and only accessible by admins
            // For now, we'll add basic validation
            if (string.IsNullOrEmpty(newPassword))
            {
                return BadRequest("New password is required");
            }

            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return NotFound("User not found");
            }

            // Update the password hash
            user.PasswordHash = HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("Password hash reset for user: {Username}", user.Username);
            return Ok("Password hash updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password hash for user: {UserId}", userId);
            return StatusCode(500, "An error occurred while resetting password hash");
        }
    }
}
