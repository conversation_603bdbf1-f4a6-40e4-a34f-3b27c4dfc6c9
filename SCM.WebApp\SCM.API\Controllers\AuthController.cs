using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using SCM.API.Models;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace SCM.API.Controllers;

public class AuthController : ApiControllerBase
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthController> _logger;

    public AuthController(
        ApplicationDbContext dbContext,
        IConfiguration configuration,
        ILogger<AuthController> logger)
    {
        _dbContext = dbContext;
        _configuration = configuration;
        _logger = logger;
    }

    [HttpPost("login")]
    public async Task<ActionResult<AuthResponse>> Login(LoginRequest request)
    {
        try
        {
            // Validate request
            if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest("Username and password are required");
            }

            // Find user by username
            var user = await _dbContext.Users
                .FirstOrDefaultAsync(u => u.Username == request.Username && u.IsActive);

            // Get user role if needed
            string roleName = "User";
            if (user != null && user.RoleId.HasValue)
            {
                var role = await _dbContext.Roles.FirstOrDefaultAsync(r => r.Id == user.RoleId);
                roleName = role?.Name ?? "User";
            }

            if (user == null)
            {
                _logger.LogWarning("Login attempt with invalid username: {Username}", request.Username);
                return Unauthorized("Invalid username or password");
            }

            // Verify password
            if (!VerifyPasswordHash(request.Password, user.PasswordHash))
            {
                _logger.LogWarning("Login attempt with invalid password for user: {Username}", request.Username);
                return Unauthorized("Invalid username or password");
            }

            // Update last login date
            user.LastLogin = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            // Generate JWT token
            var token = GenerateJwtToken(user, roleName);

            // Return response
            return Ok(new AuthResponse
            {
                Token = token,
                UserId = user.Id,
                Username = user.Username,
                FullName = $"{user.FirstName} {user.LastName}".Trim(),
                Role = roleName,
                IsAdmin = false
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login attempt");
            return StatusCode(500, "An error occurred during login");
        }
    }

    [HttpPost("register")]
    public async Task<ActionResult<AuthResponse>> Register(RegisterRequest request)
    {
        try
        {
            // Validate request
            if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest("Username and password are required");
            }

            // Check if username already exists
            if (await _dbContext.Users.AnyAsync(u => u.Username == request.Username))
            {
                return BadRequest("Username already exists");
            }

            // Check if email already exists (if provided)
            if (!string.IsNullOrEmpty(request.Email) && await _dbContext.Users.AnyAsync(u => u.Email == request.Email))
            {
                return BadRequest("Email already exists");
            }

            // Get default role (or user role)
            var userRole = await _dbContext.Roles.FirstOrDefaultAsync(r => r.Name == "User")
                ?? await _dbContext.Roles.FirstOrDefaultAsync();

            // Create new user
            var user = new User
            {
                Username = request.Username,
                Email = request.Email ?? string.Empty,
                PasswordHash = HashPassword(request.Password),
                FirstName = request.FirstName ?? string.Empty,
                LastName = request.LastName ?? string.Empty,
                RoleId = userRole?.Id,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.Users.Add(user);
            await _dbContext.SaveChangesAsync();

            // Get role name
            string roleName = userRole?.Name ?? "User";

            // Generate JWT token
            var token = GenerateJwtToken(user, roleName);

            // Return response
            return Ok(new AuthResponse
            {
                Token = token,
                UserId = user.Id,
                Username = user.Username,
                FullName = $"{user.FirstName} {user.LastName}".Trim(),
                Role = roleName,
                IsAdmin = false
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during registration");
            return StatusCode(500, "An error occurred during registration");
        }
    }

    private string GenerateJwtToken(User user, string roleName)
    {
        var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"] ?? "DefaultSecretKeyForDevelopment12345678901234");
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email ?? string.Empty),
                new Claim(ClaimTypes.Role, roleName)
            }),
            Expires = DateTime.UtcNow.AddDays(7),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
            Issuer = _configuration["Jwt:Issuer"],
            Audience = _configuration["Jwt:Audience"]
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    private string HashPassword(string password)
    {
        using var hmac = new HMACSHA512();
        var salt = hmac.Key;
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(password));

        // Combine salt and hash
        var hashBytes = new byte[salt.Length + hash.Length];
        Array.Copy(salt, 0, hashBytes, 0, salt.Length);
        Array.Copy(hash, 0, hashBytes, salt.Length, hash.Length);

        return Convert.ToBase64String(hashBytes);
    }

    private bool VerifyPasswordHash(string password, string storedHash)
    {
        var hashBytes = Convert.FromBase64String(storedHash);

        // Extract salt (first 64 bytes)
        var salt = new byte[64];
        Array.Copy(hashBytes, 0, salt, 0, salt.Length);

        // Compute hash with the same salt
        using var hmac = new HMACSHA512(salt);
        var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(password));

        // Compare computed hash with stored hash
        for (int i = 0; i < computedHash.Length; i++)
        {
            if (hashBytes[salt.Length + i] != computedHash[i])
                return false;
        }

        return true;
    }
}
