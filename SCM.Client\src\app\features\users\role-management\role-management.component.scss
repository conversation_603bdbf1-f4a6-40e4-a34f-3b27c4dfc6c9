.page-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.content-container {
  display: flex;
  gap: 20px;
}

.form-section {
  flex: 1;
  min-width: 300px;
  max-width: 600px;
}

.table-section {
  flex: 1;
}

.role-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.table-container {
  overflow-x: auto;
}

.role-table {
  width: 100%;
}

.mat-mdc-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.permissions-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.permission-category {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
}

.permission-category h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #1976d2;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 6px;
  font-size: 14px;
}

.permission-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-container {
    flex-direction: column;
  }
  
  .form-section {
    max-width: none;
  }
}
