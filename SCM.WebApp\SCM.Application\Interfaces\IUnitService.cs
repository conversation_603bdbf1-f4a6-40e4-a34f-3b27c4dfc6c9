using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IUnitService
{
    Task<IEnumerable<UnitDto>> GetAllUnitsAsync();
    Task<UnitDto?> GetUnitByIdAsync(int id);
    Task<IEnumerable<UnitDto>> GetUnitsByUnitGroupIdAsync(int unitGroupId);
    Task<UnitDto> CreateUnitAsync(CreateUnitDto createUnitDto);
    Task UpdateUnitAsync(UpdateUnitDto updateUnitDto);
    Task DeleteUnitAsync(int id);
}
