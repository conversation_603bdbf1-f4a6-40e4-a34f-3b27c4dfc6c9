using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class UnitGroupsController : ApiControllerBase
{
    private readonly IUnitGroupService _unitGroupService;

    public UnitGroupsController(IUnitGroupService unitGroupService)
    {
        _unitGroupService = unitGroupService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<UnitGroupDto>>> GetAll()
    {
        var unitGroups = await _unitGroupService.GetAllUnitGroupsAsync();
        return Ok(unitGroups);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<UnitGroupDto>> GetById(int id)
    {
        var unitGroup = await _unitGroupService.GetUnitGroupByIdAsync(id);
        if (unitGroup == null)
            return NotFound();

        return Ok(unitGroup);
    }

    [HttpGet("{id}/with-units")]
    public async Task<ActionResult<UnitGroupDto>> GetWithUnits(int id)
    {
        var unitGroup = await _unitGroupService.GetUnitGroupWithUnitsAsync(id);
        if (unitGroup == null)
            return NotFound();

        return Ok(unitGroup);
    }

    [HttpPost]
    public async Task<ActionResult<UnitGroupDto>> Create(CreateUnitGroupDto createUnitGroupDto)
    {
        var unitGroup = await _unitGroupService.CreateUnitGroupAsync(createUnitGroupDto);
        return CreatedAtAction(nameof(GetById), new { id = unitGroup.Id }, unitGroup);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateUnitGroupDto updateUnitGroupDto)
    {
        if (id != updateUnitGroupDto.Id)
            return BadRequest();

        try
        {
            await _unitGroupService.UpdateUnitGroupAsync(updateUnitGroupDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _unitGroupService.DeleteUnitGroupAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
