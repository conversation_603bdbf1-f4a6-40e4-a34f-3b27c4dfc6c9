import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { PurchaseOrderService } from '../../../../core/services/purchase-order.service';
import { SupplierService, Supplier } from '../../../../core/services/supplier.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { ProductService } from '../../../../core/services/product.service';
import { ErrorService } from '../../../../core/services/error.service';
import { 
  PurchaseOrder, 
  PurchaseOrderDetail, 
  CreatePurchaseOrder, 
  CreatePurchaseOrderDetail 
} from '../../../../core/models/purchase-order.model';
import { Product } from '../../../../core/models/product.model';
import { forkJoin, finalize, catchError, of } from 'rxjs';

@Component({
  selector: 'app-purchase-order-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    MatDialogModule
  ],
  templateUrl: './purchase-order-detail.component.html',
  styleUrls: ['./purchase-order-detail.component.scss']
})
export class PurchaseOrderDetailComponent implements OnInit {
  purchaseOrderForm!: FormGroup;
  purchaseOrder: PurchaseOrder | null = null;
  suppliers: Supplier[] = [];
  costCenters: any[] = [];
  products: Product[] = [];
  
  isLoading = false;
  isEditMode = false;
  isViewMode = false;
  purchaseOrderId: number | null = null;
  
  displayedColumns: string[] = [
    'productName',
    'quantity',
    'unitPrice',
    'totalPrice',
    'actions'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private purchaseOrderService: PurchaseOrderService,
    private supplierService: SupplierService,
    private costCenterService: CostCenterService,
    private productService: ProductService,
    private errorService: ErrorService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadReferenceData();
    
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      const mode = params.get('mode');
      
      if (id) {
        this.purchaseOrderId = +id;
        this.isViewMode = !mode || mode !== 'edit';
        this.isEditMode = mode === 'edit';
        this.loadPurchaseOrder(this.purchaseOrderId);
      }
    });
  }

  initForm(): void {
    this.purchaseOrderForm = this.fb.group({
      supplierId: ['', Validators.required],
      costCenterId: ['', Validators.required],
      orderDate: [new Date(), Validators.required],
      expectedDeliveryDate: [null],
      notes: [''],
      details: this.fb.array([])
    });
    
    // Disable form if in view mode
    if (this.isViewMode) {
      this.purchaseOrderForm.disable();
    }
  }

  loadReferenceData(): void {
    this.isLoading = true;
    
    forkJoin({
      suppliers: this.supplierService.getAll().pipe(
        catchError(error => {
          console.error('Error loading suppliers', error);
          return of([]);
        })
      ),
      costCenters: this.costCenterService.getAllCostCenters().pipe(
        catchError(error => {
          console.error('Error loading cost centers', error);
          return of([]);
        })
      ),
      products: this.productService.getAll().pipe(
        catchError(error => {
          console.error('Error loading products', error);
          return of([]);
        })
      )
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe(result => {
      this.suppliers = result.suppliers;
      this.costCenters = result.costCenters;
      this.products = result.products;
    });
  }

  loadPurchaseOrder(id: number): void {
    this.isLoading = true;
    
    this.purchaseOrderService.getById(id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (order) => {
          this.purchaseOrder = order;
          this.patchFormWithPurchaseOrder(order);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.router.navigate(['/transactions/purchase-orders']);
        }
      });
  }

  patchFormWithPurchaseOrder(order: PurchaseOrder): void {
    this.purchaseOrderForm.patchValue({
      supplierId: order.supplierId,
      costCenterId: order.costCenterId,
      orderDate: new Date(order.orderDate),
      expectedDeliveryDate: order.expectedDeliveryDate ? new Date(order.expectedDeliveryDate) : null,
      notes: order.notes || ''
    });
    
    // Clear existing details
    while (this.details.length) {
      this.details.removeAt(0);
    }
    
    // Add details
    if (order.details && order.details.length > 0) {
      order.details.forEach(detail => {
        this.addDetail(detail);
      });
    }
    
    // Disable form if in view mode
    if (this.isViewMode) {
      this.purchaseOrderForm.disable();
    }
  }

  get details(): FormArray {
    return this.purchaseOrderForm.get('details') as FormArray;
  }

  addDetail(detail?: PurchaseOrderDetail): void {
    const detailForm = this.fb.group({
      productId: [detail ? detail.productId : '', Validators.required],
      quantity: [detail ? detail.quantity : 1, [Validators.required, Validators.min(1)]],
      unitPrice: [detail ? detail.unitPrice : 0, [Validators.required, Validators.min(0)]],
      notes: [detail ? detail.notes : '']
    });
    
    this.details.push(detailForm);
    
    // Disable the detail form if in view mode
    if (this.isViewMode) {
      detailForm.disable();
    }
  }

  removeDetail(index: number): void {
    this.details.removeAt(index);
  }

  calculateTotalPrice(index: number): number {
    const detail = this.details.at(index).value;
    return detail.quantity * detail.unitPrice;
  }

  calculateOrderTotal(): number {
    return this.details.controls.reduce((total, control) => {
      const detail = control.value;
      return total + (detail.quantity * detail.unitPrice);
    }, 0);
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.id === productId);
    return product ? product.name : '';
  }

  getSupplierName(supplierId: number): string {
    const supplier = this.suppliers.find(s => s.id === supplierId);
    return supplier ? supplier.name : '';
  }

  getCostCenterName(costCenterId: number): string {
    const costCenter = this.costCenters.find(c => c.id === costCenterId);
    return costCenter ? costCenter.name : '';
  }

  savePurchaseOrder(): void {
    if (this.purchaseOrderForm.invalid) {
      this.markFormGroupTouched(this.purchaseOrderForm);
      this.errorService.showError('Please fix the errors in the form before saving.');
      return;
    }
    
    if (this.details.length === 0) {
      this.errorService.showError('Please add at least one product to the purchase order.');
      return;
    }
    
    this.isLoading = true;
    
    const formValue = this.purchaseOrderForm.value;
    
    if (this.isEditMode && this.purchaseOrderId) {
      // Update existing purchase order
      const updatePurchaseOrder = {
        id: this.purchaseOrderId,
        ...formValue
      };
      
      this.purchaseOrderService.update(this.purchaseOrderId, updatePurchaseOrder)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.errorService.showSuccess('Purchase order updated successfully');
            this.router.navigate(['/transactions/purchase-orders']);
          },
          error: (error) => this.errorService.handleError(error)
        });
    } else {
      // Create new purchase order
      const createPurchaseOrder: CreatePurchaseOrder = {
        supplierId: formValue.supplierId,
        costCenterId: formValue.costCenterId,
        orderDate: formValue.orderDate,
        expectedDeliveryDate: formValue.expectedDeliveryDate,
        notes: formValue.notes,
        details: formValue.details.map((detail: any) => ({
          productId: detail.productId,
          quantity: detail.quantity,
          unitPrice: detail.unitPrice,
          notes: detail.notes
        }))
      };
      
      this.purchaseOrderService.create(createPurchaseOrder)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.errorService.showSuccess('Purchase order created successfully');
            this.router.navigate(['/transactions/purchase-orders']);
          },
          error: (error) => this.errorService.handleError(error)
        });
    }
  }

  approvePurchaseOrder(): void {
    if (!this.purchaseOrderId) return;
    
    this.isLoading = true;
    
    this.purchaseOrderService.approve(this.purchaseOrderId)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order approved successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  rejectPurchaseOrder(): void {
    if (!this.purchaseOrderId) return;
    
    const reason = prompt('Please enter a reason for rejection:');
    if (reason === null) return; // User cancelled
    
    this.isLoading = true;
    
    this.purchaseOrderService.reject(this.purchaseOrderId, reason)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order rejected successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  cancelPurchaseOrder(): void {
    if (!this.purchaseOrderId) return;
    
    const reason = prompt('Please enter a reason for cancellation:');
    if (reason === null) return; // User cancelled
    
    this.isLoading = true;
    
    this.purchaseOrderService.cancel(this.purchaseOrderId, reason)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order cancelled successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  cancel(): void {
    this.router.navigate(['/transactions/purchase-orders']);
  }

  // Helper method to mark all controls in a form group as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
