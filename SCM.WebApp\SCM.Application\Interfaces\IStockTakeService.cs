using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IStockTakeService
{
    Task<IEnumerable<StockTakeHeaderDto>> GetAllStockTakesAsync();
    Task<StockTakeHeaderDto?> GetStockTakeByIdAsync(int id);
    Task<IEnumerable<StockTakeHeaderDto>> GetStockTakesByCostCenterIdAsync(int costCenterId);
    Task<IEnumerable<StockTakeHeaderDto>> GetStockTakesByStatusAsync(string status);
    Task<StockTakeHeaderDto> CreateStockTakeAsync(CreateStockTakeHeaderDto createStockTakeHeaderDto);
    Task UpdateStockTakeAsync(UpdateStockTakeHeaderDto updateStockTakeHeaderDto);
    Task DeleteStockTakeAsync(int id);
    Task CompleteStockTakeAsync(CompleteStockTakeDto completeStockTakeDto);
    Task CancelStockTakeAsync(int id);
    
    Task<IEnumerable<StockTakeDetailDto>> GetStockTakeDetailsAsync(int stockTakeHeaderId);
    Task<StockTakeDetailDto?> GetStockTakeDetailByIdAsync(int id);
    Task<StockTakeDetailDto> CreateStockTakeDetailAsync(CreateStockTakeDetailDto createStockTakeDetailDto);
    Task UpdateStockTakeDetailAsync(UpdateStockTakeDetailDto updateStockTakeDetailDto);
    Task DeleteStockTakeDetailAsync(int id);
    Task GenerateStockTakeDetailsAsync(int stockTakeHeaderId);
}
