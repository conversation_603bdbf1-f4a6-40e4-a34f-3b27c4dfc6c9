<div class="page-container">
  <div class="page-header">
    <h1>{{ isEditMode ? 'Edit User' : 'Add User' }}</h1>
    <div class="header-actions">
      <button mat-button (click)="cancel()">Cancel</button>
      <button mat-raised-button color="primary" (click)="saveUser()">Save</button>
    </div>
  </div>
  
  <form [formGroup]="userForm" class="user-form">
    <mat-tab-group>
      <mat-tab label="User Information">
        <div class="tab-content">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Username</mat-label>
              <input matInput formControlName="username" [readonly]="isEditMode">
              <mat-error *ngIf="userForm.get('username')?.hasError('required')">
                Username is required
              </mat-error>
              <mat-error *ngIf="userForm.get('username')?.hasError('minlength')">
                Username must be at least 3 characters
              </mat-error>
              <mat-error *ngIf="userForm.get('username')?.hasError('maxlength')">
                Username cannot exceed 20 characters
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Full Name</mat-label>
              <input matInput formControlName="fullName">
              <mat-error *ngIf="userForm.get('fullName')?.hasError('required')">
                Full Name is required
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" type="email">
              <mat-error *ngIf="userForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="userForm.get('email')?.hasError('email')">
                Please enter a valid email address
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Role</mat-label>
              <mat-select formControlName="role">
                <mat-option *ngFor="let role of roles" [value]="role">
                  {{role}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="userForm.get('role')?.hasError('required')">
                Role is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Department</mat-label>
              <mat-select formControlName="department">
                <mat-option *ngFor="let department of departments" [value]="department">
                  {{department}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="userForm.get('department')?.hasError('required')">
                Department is required
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row" *ngIf="!isEditMode">
            <mat-form-field appearance="outline">
              <mat-label>Password</mat-label>
              <input matInput formControlName="password" type="password">
              <mat-error *ngIf="userForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
              <mat-error *ngIf="userForm.get('password')?.hasError('minlength')">
                Password must be at least 8 characters
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Confirm Password</mat-label>
              <input matInput formControlName="confirmPassword" type="password">
              <mat-error *ngIf="userForm.get('confirmPassword')?.hasError('required')">
                Please confirm your password
              </mat-error>
              <mat-error *ngIf="userForm.get('confirmPassword')?.hasError('mismatch')">
                Passwords do not match
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-checkbox formControlName="isActive">Active</mat-checkbox>
          </div>
        </div>
      </mat-tab>
      
      <mat-tab label="Permissions">
        <div class="tab-content">
          <div class="permissions-container">
            <div *ngFor="let category of permissionCategories" class="permission-category">
              <h3>{{category}}</h3>
              <div class="permission-list">
                <mat-checkbox 
                  *ngFor="let permission of getPermissionsByCategory(category)"
                  [checked]="permission.selected"
                  (change)="togglePermission(permission)">
                  {{permission.name}}
                </mat-checkbox>
              </div>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </form>
</div>
