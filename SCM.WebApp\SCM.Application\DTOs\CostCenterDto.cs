namespace SCM.Application.DTOs;

public class CostCenterDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int? StoreId { get; set; }
    public string? StoreName { get; set; }
    public int? LocationId { get; set; }
    public string? LocationName { get; set; }
    public int? TypeId { get; set; }
    public string? TypeName { get; set; }
    public bool AutoTransfer { get; set; }
    public bool IsSalesPoint { get; set; }
    public string? Abbreviation { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateCostCenterDto
{
    public string Name { get; set; } = string.Empty;
    public int? StoreId { get; set; }
    public int? LocationId { get; set; }
    public int? TypeId { get; set; }
    public bool AutoTransfer { get; set; } = false;
    public bool IsSalesPoint { get; set; } = false;
    public string? Abbreviation { get; set; }
}

public class UpdateCostCenterDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int? StoreId { get; set; }
    public int? LocationId { get; set; }
    public int? TypeId { get; set; }
    public bool AutoTransfer { get; set; }
    public bool IsSalesPoint { get; set; }
    public string? Abbreviation { get; set; }
    public bool IsActive { get; set; }
}
