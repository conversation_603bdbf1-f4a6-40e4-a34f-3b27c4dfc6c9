<div class="page-container">
  <div class="page-header">
    <h1>Stock Taking</h1>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <mat-tab-group *ngIf="!isLoading">
    <mat-tab label="New Stock Count">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>New Stock Count</h2>
          <div class="header-actions">
            <button mat-raised-button color="primary" (click)="saveStockCount()">Save Count</button>
          </div>
        </div>

        <form [formGroup]="stockCountForm">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="countDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Cost Center</mat-label>
              <mat-select formControlName="costCenter">
                <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                  {{costCenter.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="stockCountForm.get('costCenter')?.hasError('required')">
                Cost Center is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Counted By</mat-label>
              <input matInput formControlName="countedBy">
              <mat-error *ngIf="stockCountForm.get('countedBy')?.hasError('required')">
                Counted By is required
              </mat-error>
            </mat-form-field>

            <div class="reference-number">
              <span>Count No.</span>
              <span class="reference-value">{{countNo}}</span>
            </div>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <textarea matInput formControlName="notes" rows="2"></textarea>
            </mat-form-field>
          </div>

          <div class="filter-row">
            <mat-form-field appearance="outline">
              <mat-label>Filter by Category</mat-label>
              <mat-select [(ngModel)]="selectedCategory" [ngModelOptions]="{standalone: true}" (selectionChange)="filterByCategory()">
                <mat-option *ngFor="let category of categories" [value]="category">
                  {{category}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="table-container mat-elevation-z2">
            <div *ngIf="filteredProducts.length === 0" class="no-data-message">
              No products found. Please select a cost center and category to view products.
            </div>
            <table mat-table [dataSource]="items.controls" *ngIf="filteredProducts.length > 0">
              <!-- Select Column -->
              <ng-container matColumnDef="select">
                <th mat-header-cell *matHeaderCellDef>
                  <mat-checkbox [checked]="true" disabled>
                  </mat-checkbox>
                </th>
                <td mat-cell *matCellDef="let item">
                  <mat-checkbox [formControl]="item.get('selected')">
                  </mat-checkbox>
                </td>
              </ng-container>

              <!-- Product ID Column -->
              <ng-container matColumnDef="productId">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('productId').value}}
                </td>
              </ng-container>

              <!-- Product Name Column -->
              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('productName').value}}
                </td>
              </ng-container>

              <!-- Category Column -->
              <ng-container matColumnDef="category">
                <th mat-header-cell *matHeaderCellDef>Category</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('category').value}}
                </td>
              </ng-container>

              <!-- Unit Size Column -->
              <ng-container matColumnDef="unitSize">
                <th mat-header-cell *matHeaderCellDef>Unit Size</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('unitSize').value}}
                </td>
              </ng-container>

              <!-- System Stock Column -->
              <ng-container matColumnDef="systemStock">
                <th mat-header-cell *matHeaderCellDef>System Stock</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('systemStock').value}}
                </td>
              </ng-container>

              <!-- Counted Stock Column -->
              <ng-container matColumnDef="countedStock">
                <th mat-header-cell *matHeaderCellDef>Counted Stock</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('countedStock')">
                    <mat-error *ngIf="item.get('countedStock')?.hasError('required')">
                      Required
                    </mat-error>
                    <mat-error *ngIf="item.get('countedStock')?.hasError('min')">
                      Min 0
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Difference Column -->
              <ng-container matColumnDef="difference">
                <th mat-header-cell *matHeaderCellDef>Difference</th>
                <td mat-cell *matCellDef="let item" [ngClass]="{'negative-diff': item.get('difference').value < 0, 'positive-diff': item.get('difference').value > 0}">
                  {{item.get('difference').value}}
                </td>
              </ng-container>

              <!-- Notes Column -->
              <ng-container matColumnDef="notes">
                <th mat-header-cell *matHeaderCellDef>Notes</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('notes')">
                  </mat-form-field>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </form>
      </div>
    </mat-tab>

    <mat-tab label="Stock Count History">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>Stock Count History</h2>
        </div>

        <div class="table-container mat-elevation-z2">
          <div *ngIf="stockCounts.length === 0" class="no-data-message">
            No stock count history found.
          </div>
          <table mat-table [dataSource]="stockCounts" class="history-table" *ngIf="stockCounts.length > 0">
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>Count ID</th>
              <td mat-cell *matCellDef="let count">{{count.id}}</td>
            </ng-container>

            <!-- Date Column -->
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef>Date</th>
              <td mat-cell *matCellDef="let count">{{count.startDate | date:'shortDate'}}</td>
            </ng-container>

            <!-- Cost Center Column -->
            <ng-container matColumnDef="costCenter">
              <th mat-header-cell *matHeaderCellDef>Cost Center</th>
              <td mat-cell *matCellDef="let count">{{count.costCenterName}}</td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let count">{{count.status}}</td>
            </ng-container>

            <!-- Counted By Column -->
            <ng-container matColumnDef="countedBy">
              <th mat-header-cell *matHeaderCellDef>Counted By</th>
              <td mat-cell *matCellDef="let count">{{count.createdBy}}</td>
            </ng-container>

            <!-- Approved By Column -->
            <ng-container matColumnDef="approvedBy">
              <th mat-header-cell *matHeaderCellDef>Approved By</th>
              <td mat-cell *matCellDef="let count">-</td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let count">
                <button mat-icon-button color="primary" (click)="viewStockCount(count)">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="historyColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: historyColumns;"></tr>
          </table>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
