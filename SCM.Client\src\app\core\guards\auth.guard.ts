import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if (this.authService.isAuthenticated()) {
      // Check if route has data.roles and user has required role
      if (route.data['roles'] && !this.checkRoles(route.data['roles'])) {
        // User doesn't have required role, redirect to home page
        return this.router.createUrlTree(['/']);
      }
      
      // User is authenticated and has required role (if specified)
      return true;
    }
    
    // User is not authenticated, redirect to login page with return url
    return this.router.createUrlTree(['/login'], { 
      queryParams: { returnUrl: state.url }
    });
  }
  
  private checkRoles(roles: string[]): boolean {
    // If no roles are required, allow access
    if (!roles || roles.length === 0) {
      return true;
    }
    
    // Check if user has any of the required roles
    return roles.some(role => this.authService.hasRole(role));
  }
}
