import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import {
  StockOnHand,
  StockAdjustment,
  StockTransfer,
  StockTakeHeader,
  CreateStockTakeHeader,
  StockTakeDetail,
  UpdateStockTakeDetail,
  StockOnHandSummary,
  StockRequest
} from '../models/stock.model';

@Injectable({
  providedIn: 'root'
})
export class StockService {
  private readonly path = 'stock';
  private readonly stockTakePath = 'stocktakes';
  private readonly stockRequestPath = 'stockrequests';

  constructor(private apiService: ApiService) { }

  // Stock On Hand
  getStockOnHand(costCenterId: number): Observable<StockOnHand[]> {
    return this.apiService.get<StockOnHand[]>(`${this.path}/costcenter/${costCenterId}`);
  }

  getProductStock(productId: number): Observable<StockOnHand[]> {
    return this.apiService.get<StockOnHand[]>(`${this.path}/product/${productId}`);
  }

  getStockSummary(costCenterId: number): Observable<StockOnHandSummary[]> {
    return this.apiService.get<StockOnHandSummary[]>(`${this.path}/summary/${costCenterId}`);
  }

  getLowStockItems(costCenterId?: number): Observable<StockOnHandSummary[]> {
    const params = costCenterId ? `?costCenterId=${costCenterId}` : '';
    return this.apiService.get<StockOnHandSummary[]>(`${this.path}/lowstock${params}`);
  }

  getOverStockItems(costCenterId?: number): Observable<StockOnHandSummary[]> {
    const params = costCenterId ? `?costCenterId=${costCenterId}` : '';
    return this.apiService.get<StockOnHandSummary[]>(`${this.path}/overstock${params}`);
  }

  getReorderItems(costCenterId?: number): Observable<StockOnHandSummary[]> {
    const params = costCenterId ? `?costCenterId=${costCenterId}` : '';
    return this.apiService.get<StockOnHandSummary[]>(`${this.path}/reorder${params}`);
  }

  // Stock Adjustments
  adjustStock(adjustment: StockAdjustment): Observable<void> {
    return this.apiService.post<void, StockAdjustment>(`${this.path}/adjust`, adjustment);
  }

  // Stock Transfers
  transferStock(transfer: StockTransfer): Observable<void> {
    return this.apiService.post<void, StockTransfer>(`${this.path}/transfer`, transfer);
  }

  // Stock Takes
  getAllStockTakes(): Observable<StockTakeHeader[]> {
    return this.apiService.get<StockTakeHeader[]>(this.stockTakePath);
  }

  getStockTakeById(id: number): Observable<StockTakeHeader> {
    return this.apiService.get<StockTakeHeader>(`${this.stockTakePath}/${id}`);
  }

  getStockTakesByCostCenter(costCenterId: number): Observable<StockTakeHeader[]> {
    return this.apiService.get<StockTakeHeader[]>(`${this.stockTakePath}/costcenter/${costCenterId}`);
  }

  createStockTake(stockTake: CreateStockTakeHeader): Observable<StockTakeHeader> {
    return this.apiService.post<StockTakeHeader, CreateStockTakeHeader>(this.stockTakePath, stockTake);
  }

  updateStockTakeDetail(id: number, detail: UpdateStockTakeDetail): Observable<void> {
    return this.apiService.put<void, UpdateStockTakeDetail>(`${this.stockTakePath}/detail/${id}`, detail);
  }

  completeStockTake(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.stockTakePath}/${id}/complete`, {});
  }

  cancelStockTake(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.stockTakePath}/${id}/cancel`, {});
  }

  // Stock Requests
  getAllStockRequests(): Observable<StockRequest[]> {
    return this.apiService.get<StockRequest[]>(this.stockRequestPath);
  }

  getStockRequestById(id: number): Observable<StockRequest> {
    return this.apiService.get<StockRequest>(`${this.stockRequestPath}/${id}`);
  }

  createStockRequest(stockRequest: StockRequest): Observable<StockRequest> {
    return this.apiService.post<StockRequest, StockRequest>(this.stockRequestPath, stockRequest);
  }

  submitStockRequest(stockRequest: StockRequest): Observable<StockRequest> {
    // If the request already has an ID, update it
    if (stockRequest.id) {
      stockRequest.isSubmitted = true;
      return this.apiService.put<StockRequest, StockRequest>(`${this.stockRequestPath}/${stockRequest.id}`, stockRequest);
    }

    // Otherwise create a new submitted request
    stockRequest.isSubmitted = true;
    return this.apiService.post<StockRequest, StockRequest>(this.stockRequestPath, stockRequest);
  }

  approveStockRequest(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.stockRequestPath}/${id}/approve`, {});
  }

  rejectStockRequest(id: number, reason: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.stockRequestPath}/${id}/reject`, { reason });
  }

  cancelStockRequest(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.stockRequestPath}/${id}/cancel`, {});
  }
}
