<div class="page-container">
  <div class="page-header">
    <h1>Stock Transfer</h1>
  </div>
  
  <mat-tab-group>
    <mat-tab label="New Transfer">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>New Stock Transfer</h2>
          <div class="header-actions">
            <button mat-raised-button color="primary" (click)="saveTransfer()">Save Transfer</button>
          </div>
        </div>
        
        <form [formGroup]="transferForm">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="transferDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
            
            <div class="spacer"></div>
            
            <div class="reference-number">
              <span>Transfer No.</span>
              <span class="reference-value">{{transferNo}}</span>
            </div>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>From Cost Center</mat-label>
              <mat-select formControlName="fromCostCenter">
                <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                  {{costCenter.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="transferForm.get('fromCostCenter')?.hasError('required')">
                From Cost Center is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>To Cost Center</mat-label>
              <mat-select formControlName="toCostCenter">
                <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                  {{costCenter.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="transferForm.get('toCostCenter')?.hasError('required')">
                To Cost Center is required
              </mat-error>
              <mat-error *ngIf="transferForm.get('toCostCenter')?.hasError('sameCostCenter')">
                From and To Cost Centers cannot be the same
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <textarea matInput formControlName="notes" rows="2"></textarea>
            </mat-form-field>
          </div>
          
          <div class="table-container mat-elevation-z2">
            <table mat-table [dataSource]="items.controls">
              <!-- Product ID Column -->
              <ng-container matColumnDef="productId">
                <th mat-header-cell *matHeaderCellDef>Product</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('productId')" [matAutocomplete]="auto">
                    <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayProductFn">
                      <mat-option *ngFor="let product of filteredProducts[i] | async" [value]="product.id">
                        {{product.id}} - {{product.name}} ({{product.unitSize}})
                      </mat-option>
                    </mat-autocomplete>
                    <mat-error *ngIf="item.get('productId')?.hasError('required')">
                      Product is required
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Product Name Column -->
              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('productName')" readonly>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Unit Size Column -->
              <ng-container matColumnDef="unitSize">
                <th mat-header-cell *matHeaderCellDef>Unit Size</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('unitSize')" readonly>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Available Stock Column -->
              <ng-container matColumnDef="availableStock">
                <th mat-header-cell *matHeaderCellDef>Available Stock</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('availableStock')" readonly>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Transfer Qty Column -->
              <ng-container matColumnDef="transferQty">
                <th mat-header-cell *matHeaderCellDef>Transfer Qty</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('transferQty')">
                    <mat-error *ngIf="item.get('transferQty')?.hasError('required')">
                      Transfer quantity is required
                    </mat-error>
                    <mat-error *ngIf="item.get('transferQty')?.hasError('min')">
                      Must be greater than 0
                    </mat-error>
                    <mat-error *ngIf="item.get('transferQty')?.hasError('exceedsStock')">
                      Exceeds available stock
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <button mat-icon-button color="warn" (click)="removeItem(i)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
            
            <div class="table-actions">
              <button mat-button color="primary" (click)="addItem()">
                <mat-icon>add</mat-icon> Add Item
              </button>
            </div>
          </div>
        </form>
      </div>
    </mat-tab>
    
    <mat-tab label="Transfer History">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>Transfer History</h2>
        </div>
        
        <div class="table-container mat-elevation-z2">
          <table mat-table [dataSource]="transfers" class="history-table">
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>Transfer ID</th>
              <td mat-cell *matCellDef="let transfer">{{transfer.id}}</td>
            </ng-container>
            
            <!-- Date Column -->
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef>Date</th>
              <td mat-cell *matCellDef="let transfer">{{transfer.date | date:'shortDate'}}</td>
            </ng-container>
            
            <!-- From Cost Center Column -->
            <ng-container matColumnDef="fromCostCenter">
              <th mat-header-cell *matHeaderCellDef>From</th>
              <td mat-cell *matCellDef="let transfer">{{transfer.fromCostCenter}}</td>
            </ng-container>
            
            <!-- To Cost Center Column -->
            <ng-container matColumnDef="toCostCenter">
              <th mat-header-cell *matHeaderCellDef>To</th>
              <td mat-cell *matCellDef="let transfer">{{transfer.toCostCenter}}</td>
            </ng-container>
            
            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let transfer" [ngClass]="{'status-completed': transfer.status === 'Completed', 'status-pending': transfer.status === 'Pending'}">
                {{transfer.status}}
              </td>
            </ng-container>
            
            <!-- Created By Column -->
            <ng-container matColumnDef="createdBy">
              <th mat-header-cell *matHeaderCellDef>Created By</th>
              <td mat-cell *matCellDef="let transfer">{{transfer.createdBy}}</td>
            </ng-container>
            
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let transfer">
                <button mat-icon-button color="primary" (click)="viewTransfer(transfer)">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>
            
            <tr mat-header-row *matHeaderRowDef="historyColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: historyColumns;"></tr>
          </table>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
