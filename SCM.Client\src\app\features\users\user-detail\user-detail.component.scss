.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.user-form {
  max-width: 1200px;
}

.tab-content {
  padding: 20px 0;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-row mat-form-field {
  flex: 1;
  min-width: 200px;
}

.permissions-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.permission-category {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
}

.permission-category h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1976d2;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.permission-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    margin-top: 10px;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-row mat-form-field {
    width: 100%;
  }
  
  .permissions-container {
    grid-template-columns: 1fr;
  }
}
