using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class TaxService : ITaxService
{
    private readonly IRepository<Tax> _taxRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public TaxService(
        IRepository<Tax> taxRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _taxRepository = taxRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<TaxDto>> GetAllTaxesAsync()
    {
        var taxes = await _taxRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<TaxDto>>(taxes);
    }

    public async Task<TaxDto?> GetTaxByIdAsync(int id)
    {
        var tax = await _taxRepository.GetByIdAsync(id);
        return tax != null ? _mapper.Map<TaxDto>(tax) : null;
    }

    public async Task<TaxDto?> GetDefaultTaxAsync()
    {
        var defaultTax = await _dbContext.Taxes.FirstOrDefaultAsync(t => t.IsDefault);
        return defaultTax != null ? _mapper.Map<TaxDto>(defaultTax) : null;
    }

    public async Task<TaxDto> CreateTaxAsync(CreateTaxDto createTaxDto)
    {
        var tax = _mapper.Map<Tax>(createTaxDto);
        
        // If this tax is set as default, unset any existing default tax
        if (tax.IsDefault)
        {
            var existingDefaultTaxes = await _dbContext.Taxes.Where(t => t.IsDefault).ToListAsync();
            foreach (var existingDefaultTax in existingDefaultTaxes)
            {
                existingDefaultTax.IsDefault = false;
                _dbContext.Taxes.Update(existingDefaultTax);
            }
        }
        
        await _taxRepository.AddAsync(tax);
        return _mapper.Map<TaxDto>(tax);
    }

    public async Task UpdateTaxAsync(UpdateTaxDto updateTaxDto)
    {
        var tax = await _taxRepository.GetByIdAsync(updateTaxDto.Id);
        if (tax == null)
            throw new KeyNotFoundException($"Tax with ID {updateTaxDto.Id} not found.");

        // If this tax is being set as default, unset any existing default tax
        if (updateTaxDto.IsDefault && !tax.IsDefault)
        {
            var existingDefaultTaxes = await _dbContext.Taxes.Where(t => t.IsDefault && t.Id != updateTaxDto.Id).ToListAsync();
            foreach (var existingDefaultTax in existingDefaultTaxes)
            {
                existingDefaultTax.IsDefault = false;
                _dbContext.Taxes.Update(existingDefaultTax);
            }
        }

        _mapper.Map(updateTaxDto, tax);
        await _taxRepository.UpdateAsync(tax);
    }

    public async Task DeleteTaxAsync(int id)
    {
        var tax = await _taxRepository.GetByIdAsync(id);
        if (tax == null)
            throw new KeyNotFoundException($"Tax with ID {id} not found.");

        // Check if there are any products using this tax
        var hasProducts = await _dbContext.Products.AnyAsync(p => p.TaxId == id);
        if (hasProducts)
            throw new InvalidOperationException($"Cannot delete Tax with ID {id} because it is being used by one or more products.");

        // Check if there are any transaction details using this tax
        var hasTransactionDetails = await _dbContext.TransactionDetails.AnyAsync(td => td.TaxId == id);
        if (hasTransactionDetails)
            throw new InvalidOperationException($"Cannot delete Tax with ID {id} because it is being used by one or more transaction details.");

        await _taxRepository.DeleteAsync(tax);
    }
}
