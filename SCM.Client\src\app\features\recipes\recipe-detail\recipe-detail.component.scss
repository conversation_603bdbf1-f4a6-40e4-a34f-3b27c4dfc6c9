.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.recipe-form {
  max-width: 1200px;
}

.tab-content {
  padding: 20px 0;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-row mat-form-field {
  flex: 1;
  min-width: 200px;
}

.yield-container {
  display: flex;
  gap: 16px;
  flex: 1;
}

.yield-container mat-form-field:first-child {
  flex: 1;
}

.yield-container mat-form-field:last-child {
  flex: 2;
}

.full-width {
  width: 100%;
}

.table-container {
  margin: 20px 0;
  overflow-x: auto;
  border-radius: 4px;
}

table {
  width: 100%;
}

.table-form-field {
  width: 100%;
  margin: 0;
  font-size: 14px;
}

.table-form-field ::ng-deep .mat-mdc-form-field-infix {
  padding: 8px 0;
  width: auto;
}

.table-form-field ::ng-deep .mat-mdc-text-field-wrapper {
  padding: 0 8px;
}

.table-actions {
  padding: 8px;
  display: flex;
  justify-content: flex-start;
}

.cost-summary {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.cost-row {
  display: flex;
  justify-content: space-between;
  width: 300px;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.cost-value {
  font-weight: bold;
  color: #1976d2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    margin-top: 10px;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-row mat-form-field {
    width: 100%;
  }
  
  .yield-container {
    flex-direction: column;
    width: 100%;
  }
  
  .cost-summary {
    align-items: stretch;
  }
  
  .cost-row {
    width: 100%;
  }
}
