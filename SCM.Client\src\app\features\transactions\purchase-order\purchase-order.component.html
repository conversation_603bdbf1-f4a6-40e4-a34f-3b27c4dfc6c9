<div class="page-container">
  <div class="page-header">
    <h1>Purchase Orders</h1>
  </div>
  
  <mat-tab-group>
    <mat-tab label="New Purchase Order">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>New Purchase Order</h2>
          <div class="header-actions">
            <button mat-raised-button color="primary" (click)="savePurchaseOrder()">Save Order</button>
          </div>
        </div>
        
        <form [formGroup]="poForm">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="poDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
            
            <div class="spacer"></div>
            
            <div class="reference-number">
              <span>PO No.</span>
              <span class="reference-value">{{poNo}}</span>
            </div>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Supplier</mat-label>
              <mat-select formControlName="supplier">
                <mat-option *ngFor="let supplier of suppliers" [value]="supplier.id">
                  {{supplier.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="poForm.get('supplier')?.hasError('required')">
                Supplier is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Cost Center</mat-label>
              <mat-select formControlName="costCenter">
                <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                  {{costCenter.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="poForm.get('costCenter')?.hasError('required')">
                Cost Center is required
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Payment Terms</mat-label>
              <mat-select formControlName="paymentTerms">
                <mat-option *ngFor="let term of paymentTerms" [value]="term.id">
                  {{term.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="poForm.get('paymentTerms')?.hasError('required')">
                Payment Terms is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Expected Delivery Date</mat-label>
              <input matInput [matDatepicker]="deliveryPicker" formControlName="expectedDeliveryDate">
              <mat-datepicker-toggle matSuffix [for]="deliveryPicker"></mat-datepicker-toggle>
              <mat-datepicker #deliveryPicker></mat-datepicker>
              <mat-error *ngIf="poForm.get('expectedDeliveryDate')?.hasError('required')">
                Expected Delivery Date is required
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <textarea matInput formControlName="notes" rows="2"></textarea>
            </mat-form-field>
          </div>
          
          <div class="table-container mat-elevation-z2">
            <table mat-table [dataSource]="items.controls">
              <!-- Product ID Column -->
              <ng-container matColumnDef="productId">
                <th mat-header-cell *matHeaderCellDef>Product</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('productId')" [matAutocomplete]="auto">
                    <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayProductFn">
                      <mat-option *ngFor="let product of filteredProducts[i] | async" [value]="product.id">
                        {{product.id}} - {{product.name}} ({{product.unitSize}})
                      </mat-option>
                    </mat-autocomplete>
                    <mat-error *ngIf="item.get('productId')?.hasError('required')">
                      Product is required
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Product Name Column -->
              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('productName')" readonly>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Unit Size Column -->
              <ng-container matColumnDef="unitSize">
                <th mat-header-cell *matHeaderCellDef>Unit Size</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('unitSize')" readonly>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('quantity')">
                    <mat-error *ngIf="item.get('quantity')?.hasError('required')">
                      Required
                    </mat-error>
                    <mat-error *ngIf="item.get('quantity')?.hasError('min')">
                      Min 1
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Unit Price Column -->
              <ng-container matColumnDef="unitPrice">
                <th mat-header-cell *matHeaderCellDef>Unit Price</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('unitPrice')">
                    <span matTextPrefix>$&nbsp;</span>
                    <mat-error *ngIf="item.get('unitPrice')?.hasError('required')">
                      Required
                    </mat-error>
                    <mat-error *ngIf="item.get('unitPrice')?.hasError('min')">
                      Min 0
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Total Column -->
              <ng-container matColumnDef="total">
                <th mat-header-cell *matHeaderCellDef>Total</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('total')" readonly>
                    <span matTextPrefix>$&nbsp;</span>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <button mat-icon-button color="warn" (click)="removeItem(i)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
            
            <div class="table-actions">
              <button mat-button color="primary" (click)="addItem()">
                <mat-icon>add</mat-icon> Add Item
              </button>
            </div>
          </div>
          
          <div class="totals-section">
            <div class="totals-row">
              <span>Total:</span>
              <span class="total-value">{{calculateOrderTotal() | currency}}</span>
            </div>
          </div>
        </form>
      </div>
    </mat-tab>
    
    <mat-tab label="Purchase Order History">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>Purchase Order History</h2>
        </div>
        
        <div class="table-container mat-elevation-z2">
          <table mat-table [dataSource]="purchaseOrders" class="history-table">
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>PO ID</th>
              <td mat-cell *matCellDef="let po">{{po.id}}</td>
            </ng-container>
            
            <!-- Date Column -->
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef>Date</th>
              <td mat-cell *matCellDef="let po">{{po.date | date:'shortDate'}}</td>
            </ng-container>
            
            <!-- Supplier Column -->
            <ng-container matColumnDef="supplier">
              <th mat-header-cell *matHeaderCellDef>Supplier</th>
              <td mat-cell *matCellDef="let po">{{po.supplier}}</td>
            </ng-container>
            
            <!-- Cost Center Column -->
            <ng-container matColumnDef="costCenter">
              <th mat-header-cell *matHeaderCellDef>Cost Center</th>
              <td mat-cell *matCellDef="let po">{{po.costCenter}}</td>
            </ng-container>
            
            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let po" 
                  [ngClass]="{'status-pending': po.status === 'Pending', 
                             'status-approved': po.status === 'Approved', 
                             'status-completed': po.status === 'Completed'}">
                {{po.status}}
              </td>
            </ng-container>
            
            <!-- Total Column -->
            <ng-container matColumnDef="total">
              <th mat-header-cell *matHeaderCellDef>Total</th>
              <td mat-cell *matCellDef="let po">{{po.total | currency}}</td>
            </ng-container>
            
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let po">
                <button mat-icon-button color="primary" (click)="viewPurchaseOrder(po)">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>
            
            <tr mat-header-row *matHeaderRowDef="historyColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: historyColumns;"></tr>
          </table>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
