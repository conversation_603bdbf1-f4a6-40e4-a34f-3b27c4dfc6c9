namespace SCM.Application.DTOs;

public class ProductCostCenterLinkDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public bool AutoReorder { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateProductCostCenterLinkDto
{
    public int ProductId { get; set; }
    public int CostCenterId { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public bool AutoReorder { get; set; } = false;
}

public class UpdateProductCostCenterLinkDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public int CostCenterId { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public bool AutoReorder { get; set; }
    public bool IsActive { get; set; }
}
