namespace SCM.Domain.Entities;

public class StockOnHand
{
    public int ProductId { get; set; }
    public int CostCenterId { get; set; }
    public int BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? CostPrice { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Product Product { get; set; } = null!;
    public virtual CostCenter CostCenter { get; set; } = null!;
    public virtual Batch Batch { get; set; } = null!;
    public virtual Unit? Unit { get; set; }
}
