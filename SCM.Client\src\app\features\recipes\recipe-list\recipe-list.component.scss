.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-container mat-form-field {
  flex: 1;
  min-width: 200px;
}

.table-container {
  overflow-x: auto;
  border-radius: 4px;
}

.recipe-table {
  width: 100%;
}

.mat-mdc-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.status-active {
  color: #4caf50;
  font-weight: 500;
}

.status-inactive {
  color: #f44336;
  font-weight: 500;
}

mat-chip-option {
  pointer-events: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-container mat-form-field {
    width: 100%;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .page-header button {
    margin-top: 10px;
  }
}
