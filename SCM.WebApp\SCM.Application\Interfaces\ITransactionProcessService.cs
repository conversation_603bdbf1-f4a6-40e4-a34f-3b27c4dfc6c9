using SCM.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SCM.Application.Interfaces
{
    public interface ITransactionProcessService
    {
        Task<IEnumerable<TransactionProcessDto>> GetAllAsync();
        Task<TransactionProcessDto> GetByIdAsync(int id);
        Task<TransactionProcessDto> CreateAsync(TransactionProcessDto transactionProcessDto);
        Task UpdateAsync(int id, TransactionProcessDto transactionProcessDto);
        Task DeleteAsync(int id);
    }
}
