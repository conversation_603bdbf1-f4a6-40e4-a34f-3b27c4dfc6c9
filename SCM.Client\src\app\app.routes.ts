import { Routes } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';

export const routes: Routes = [
  // Auth routes
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'register',
    loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent)
  },

  // Main application routes (protected)
  {
    path: '',
    loadComponent: () => import('./layout/main-layout/main-layout.component').then(m => m.MainLayoutComponent),
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
      },
      // Inventory Management
      {
        path: 'inventory/stock-request',
        loadComponent: () => import('./features/inventory/stock-request/stock-request.component').then(m => m.StockRequestComponent)
      },
      {
        path: 'inventory/stock-adjustment',
        loadComponent: () => import('./features/inventory/stock-adjustment/stock-adjustment.component').then(m => m.StockAdjustmentComponent)
      },
      {
        path: 'inventory/stock-taking',
        loadComponent: () => import('./features/inventory/stock-taking/stock-taking.component').then(m => m.StockTakingComponent)
      },
      {
        path: 'inventory/stock-transfer',
        loadComponent: () => import('./features/inventory/stock-transfer/stock-transfer.component').then(m => m.StockTransferComponent)
      },

      // Product Management
      {
        path: 'products',
        loadComponent: () => import('./features/products/product-list/product-list.component').then(m => m.ProductListComponent)
      },
      {
        path: 'products/new',
        loadComponent: () => import('./features/products/product-detail/product-detail.component').then(m => m.ProductDetailComponent)
      },
      {
        path: 'products/:id',
        loadComponent: () => import('./features/products/product-detail/product-detail.component').then(m => m.ProductDetailComponent)
      },
      {
        path: 'products/categories',
        loadComponent: () => import('./features/products/product-category/product-category.component').then(m => m.ProductCategoryComponent)
      },

      // Transaction Management
      {
        path: 'transactions/purchase-orders',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order-list/purchase-order-list.component').then(m => m.PurchaseOrderListComponent)
      },
      {
        path: 'transactions/purchase-orders/new',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order-detail/purchase-order-detail.component').then(m => m.PurchaseOrderDetailComponent)
      },
      {
        path: 'transactions/purchase-orders/:id',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order-detail/purchase-order-detail.component').then(m => m.PurchaseOrderDetailComponent)
      },
      {
        path: 'transactions/purchase-orders/:id/:mode',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order-detail/purchase-order-detail.component').then(m => m.PurchaseOrderDetailComponent)
      },
      {
        path: 'transactions/goods-receiving',
        loadComponent: () => import('./features/transactions/goods-receiving/goods-receiving.component').then(m => m.GoodsReceivingComponent)
      },
      {
        path: 'transactions/sales',
        loadComponent: () => import('./features/transactions/sales/sales.component').then(m => m.SalesComponent)
      },

      // Recipe Management
      {
        path: 'recipes',
        loadComponent: () => import('./features/recipes/recipe-list/recipe-list.component').then(m => m.RecipeListComponent)
      },
      {
        path: 'recipes/new',
        loadComponent: () => import('./features/recipes/recipe-detail/recipe-detail.component').then(m => m.RecipeDetailComponent)
      },
      {
        path: 'recipes/:id',
        loadComponent: () => import('./features/recipes/recipe-detail/recipe-detail.component').then(m => m.RecipeDetailComponent)
      },

      // User Management
      {
        path: 'users',
        loadComponent: () => import('./features/users/user-list/user-list.component').then(m => m.UserListComponent),
        data: { roles: ['Admin'] }
      },
      {
        path: 'users/new',
        loadComponent: () => import('./features/users/user-detail/user-detail.component').then(m => m.UserDetailComponent),
        data: { roles: ['Admin'] }
      },
      {
        path: 'users/:id',
        loadComponent: () => import('./features/users/user-detail/user-detail.component').then(m => m.UserDetailComponent),
        data: { roles: ['Admin'] }
      },
      {
        path: 'users/roles',
        loadComponent: () => import('./features/users/role-management/role-management.component').then(m => m.RoleManagementComponent),
        data: { roles: ['Admin'] }
      },

      // Configuration
      {
        path: 'configuration/store-config',
        loadComponent: () => import('./features/configuration/store-config/store-config.component').then(m => m.StoreConfigComponent)
      },
      {
        path: 'configuration/departments',
        loadComponent: () => import('./features/configuration/departments/departments.component').then(m => m.DepartmentsComponent),
        data: { roles: ['Admin'] }
      }
    ]
  },
  {
    path: '**',
    redirectTo: ''
  }
];
