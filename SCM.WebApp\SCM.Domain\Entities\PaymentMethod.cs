using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class PaymentMethod : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool RequiresReference { get; set; } = false;
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<PaymentDetail> PaymentDetails { get; set; } = new List<PaymentDetail>();
}
