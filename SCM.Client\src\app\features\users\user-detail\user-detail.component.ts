import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-user-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatSnackBarModule,
    MatCheckboxModule,
    MatTabsModule,
    MatChipsModule,
    RouterModule
  ],
  templateUrl: './user-detail.component.html',
  styleUrls: ['./user-detail.component.scss']
})
export class UserDetailComponent implements OnInit {
  userForm!: FormGroup;
  isEditMode: boolean = false;
  userId: number | null = null;
  
  roles: string[] = [
    'Administrator',
    'Manager',
    'Supervisor',
    'Inventory Clerk',
    'Purchasing Officer',
    'Sales Representative',
    'Kitchen Staff',
    'Viewer'
  ];
  
  departments: string[] = [
    'Management',
    'Finance',
    'Purchasing',
    'Inventory',
    'Sales',
    'Kitchen',
    'IT'
  ];
  
  permissions = [
    { id: 1, name: 'View Products', category: 'Products', selected: false },
    { id: 2, name: 'Create Products', category: 'Products', selected: false },
    { id: 3, name: 'Edit Products', category: 'Products', selected: false },
    { id: 4, name: 'Delete Products', category: 'Products', selected: false },
    { id: 5, name: 'View Inventory', category: 'Inventory', selected: false },
    { id: 6, name: 'Adjust Inventory', category: 'Inventory', selected: false },
    { id: 7, name: 'Transfer Inventory', category: 'Inventory', selected: false },
    { id: 8, name: 'View Purchases', category: 'Purchases', selected: false },
    { id: 9, name: 'Create Purchase Orders', category: 'Purchases', selected: false },
    { id: 10, name: 'Approve Purchase Orders', category: 'Purchases', selected: false },
    { id: 11, name: 'Receive Goods', category: 'Purchases', selected: false },
    { id: 12, name: 'View Sales', category: 'Sales', selected: false },
    { id: 13, name: 'Create Sales', category: 'Sales', selected: false },
    { id: 14, name: 'View Reports', category: 'Reports', selected: false },
    { id: 15, name: 'Export Reports', category: 'Reports', selected: false },
    { id: 16, name: 'View Users', category: 'Users', selected: false },
    { id: 17, name: 'Create Users', category: 'Users', selected: false },
    { id: 18, name: 'Edit Users', category: 'Users', selected: false },
    { id: 19, name: 'Delete Users', category: 'Users', selected: false },
    { id: 20, name: 'View Recipes', category: 'Recipes', selected: false },
    { id: 21, name: 'Create Recipes', category: 'Recipes', selected: false },
    { id: 22, name: 'Edit Recipes', category: 'Recipes', selected: false },
    { id: 23, name: 'Delete Recipes', category: 'Recipes', selected: false }
  ];
  
  permissionCategories: string[] = [...new Set(this.permissions.map(p => p.category))];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
    
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.userId = +id;
        this.isEditMode = true;
        this.loadUserData(this.userId);
      }
    });
  }

  initForm(): void {
    this.userForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(20)]],
      fullName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      role: ['', Validators.required],
      department: ['', Validators.required],
      password: ['', this.isEditMode ? [] : [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', this.isEditMode ? [] : [Validators.required]],
      isActive: [true]
    }, { validators: this.passwordMatchValidator });
    
    // If editing, remove password validators
    if (this.isEditMode) {
      this.userForm.get('password')?.clearValidators();
      this.userForm.get('confirmPassword')?.clearValidators();
      this.userForm.get('password')?.updateValueAndValidity();
      this.userForm.get('confirmPassword')?.updateValueAndValidity();
    }
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;
    
    if (password === confirmPassword) {
      form.get('confirmPassword')?.setErrors(null);
      return null;
    } else {
      form.get('confirmPassword')?.setErrors({ mismatch: true });
      return { mismatch: true };
    }
  }

  loadUserData(id: number): void {
    // In a real application, this would be an API call
    // For now, we'll just simulate loading data
    if (id === 1) {
      this.userForm.patchValue({
        username: 'admin',
        fullName: 'System Administrator',
        email: '<EMAIL>',
        role: 'Administrator',
        department: 'IT',
        isActive: true
      });
      
      // Set all permissions to true for admin
      this.permissions.forEach(p => p.selected = true);
    } else if (id === 3) {
      this.userForm.patchValue({
        username: 'mjohnson',
        fullName: 'Mary Johnson',
        email: '<EMAIL>',
        role: 'Inventory Clerk',
        department: 'Inventory',
        isActive: true
      });
      
      // Set specific permissions for inventory clerk
      this.permissions.forEach(p => {
        if (p.category === 'Inventory' || p.name === 'View Products' || p.name === 'View Reports') {
          p.selected = true;
        }
      });
    }
  }

  togglePermission(permission: any): void {
    permission.selected = !permission.selected;
  }

  getPermissionsByCategory(category: string): any[] {
    return this.permissions.filter(p => p.category === category);
  }

  saveUser(): void {
    if (this.userForm.valid) {
      // Get selected permissions
      const selectedPermissions = this.permissions
        .filter(p => p.selected)
        .map(p => p.id);
      
      const userData = {
        ...this.userForm.value,
        permissions: selectedPermissions
      };
      
      console.log('User data:', userData);
      
      this.snackBar.open('User saved successfully', 'Close', {
        duration: 3000
      });
      
      if (!this.isEditMode) {
        this.router.navigate(['/users']);
      }
    } else {
      this.markFormGroupTouched(this.userForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/users']);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
