import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Observable, of, forkJoin } from 'rxjs';
import { map, startWith, catchError, finalize } from 'rxjs/operators';
import { ProductService } from '../../../core/services/product.service';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { StockService } from '../../../core/services/stock.service';
import { PermissionsService } from '../../../core/services/permissions.service';
import { Product } from '../../../core/models/product.model';
import { StockRequest, StockRequestItem } from '../../../core/models/stock.model';

// Using StockRequestItem from stock.model.ts

interface CostCenter {
  id: number;
  name: string;
}

@Component({
  selector: 'app-stock-request',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './stock-request.component.html',
  styleUrls: ['./stock-request.component.scss']
})
export class StockRequestComponent implements OnInit {
  stockRequestForm!: FormGroup;
  stockRequestNo: string = 'SR-' + Math.floor(Math.random() * 10000).toString().padStart(5, '0');
  currentDate: Date = new Date();
  isLoading = false;

  displayedColumns: string[] = [
    'productCode',
    'productName',
    'unitName',
    'quantity',
    'price',
    'total',
    'deliveryDate',
    'actions'
  ];

  costCenters: CostCenter[] = [];
  products: Product[] = [];
  filteredProducts: Observable<Product[]> = of([]);

  constructor(
    private fb: FormBuilder,
    private productService: ProductService,
    private costCenterService: CostCenterService,
    private stockService: StockService,
    private permissionsService: PermissionsService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadReferenceData();
  }

  initForm(): void {
    this.stockRequestForm = this.fb.group({
      requestDate: [this.currentDate, Validators.required],
      fromCostCenterId: ['', Validators.required],
      toCostCenterId: ['', Validators.required],
      remarks: [''],
      refNo: [''],
      items: this.fb.array([])
    });

    // Add an empty row by default
    this.addItem();

    // Set up validation to prevent selecting the same cost center
    this.stockRequestForm.get('fromCostCenterId')?.valueChanges.subscribe(() => {
      this.validateCostCenters();
    });

    this.stockRequestForm.get('toCostCenterId')?.valueChanges.subscribe(() => {
      this.validateCostCenters();
    });
  }

  loadReferenceData(): void {
    this.isLoading = true;

    forkJoin({
      costCenters: this.costCenterService.getAllCostCenters().pipe(
        catchError(error => {
          console.error('Error loading cost centers', error);
          return of([]);
        })
      ),
      products: this.productService.getAll().pipe(
        catchError(error => {
          console.error('Error loading products', error);
          return of([]);
        })
      )
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe({
      next: (data) => {
        this.costCenters = data.costCenters;
        this.products = data.products;

        // If no data is returned, use mock data for development
        if (this.costCenters.length === 0) {
          this.costCenters = [
            { id: 1, name: '1 - Food Store' },
            { id: 2, name: '2 - Beverage Store' },
            { id: 3, name: '3 - General Store' },
            { id: 4, name: '4 - Engineering Store' },
            { id: 5, name: '5 - S.O.E. Store' },
            { id: 6, name: '6 - Tobacco' },
            { id: 7, name: '7 - Food Main Kitchen' },
            { id: 8, name: '8 - Main Restaurant' }
          ];
        }

        // Set up product filtering for the first item
        this.setupProductFiltering(0);
      },
      error: (error) => {
        console.error('Error loading reference data', error);
        this.snackBar.open('Error loading reference data. Please try again.', 'Close', {
          duration: 3000
        });
      }
    });
  }

  validateCostCenters(): void {
    const fromCostCenterId = this.stockRequestForm.get('fromCostCenterId')?.value;
    const toCostCenterId = this.stockRequestForm.get('toCostCenterId')?.value;

    if (fromCostCenterId && toCostCenterId && fromCostCenterId === toCostCenterId) {
      this.stockRequestForm.get('toCostCenterId')?.setErrors({ sameCostCenter: true });
    }
  }

  get items(): FormArray {
    return this.stockRequestForm.get('items') as FormArray;
  }

  addItem(): void {
    const itemForm = this.fb.group({
      productId: ['', Validators.required],
      productCode: ['', Validators.required],
      productName: [{ value: '', disabled: true }],
      unitId: [''],
      unitName: [{ value: '', disabled: true }],
      quantity: [0, [Validators.required, Validators.min(0.01)]],
      price: [0],
      total: [{ value: 0, disabled: true }],
      notes: [''],
      deliveryDate: [this.currentDate]
    });

    // Auto-calculate total when quantity or price changes
    itemForm.get('quantity')?.valueChanges.subscribe(() => this.calculateItemTotal(itemForm));
    itemForm.get('price')?.valueChanges.subscribe(() => this.calculateItemTotal(itemForm));

    this.items.push(itemForm);

    // Set up product filtering for the new item
    const index = this.items.length - 1;
    this.setupProductFiltering(index);
  }

  removeItem(index: number): void {
    this.items.removeAt(index);

    // If all items are removed, add an empty one
    if (this.items.length === 0) {
      this.addItem();
    }

    this.calculateTotals();
  }

  setupProductFiltering(index: number): void {
    const productCodeControl = this.items.at(index).get('productCode');

    if (productCodeControl) {
      // Set up filtering for product code input
      productCodeControl.valueChanges.pipe(
        startWith(''),
        map(value => this._filterProducts(value || ''))
      ).subscribe(filteredProducts => {
        this.filteredProducts = of(filteredProducts);
      });
    }
  }

  private _filterProducts(value: string): Product[] {
    const filterValue = value.toLowerCase();
    return this.products.filter(product => {
      const codeMatch = product.code?.toLowerCase().includes(filterValue) || false;
      const nameMatch = product.name?.toLowerCase().includes(filterValue) || false;
      return codeMatch || nameMatch;
    });
  }

  selectProduct(index: number, product: Product): void {
    const itemForm = this.items.at(index);

    // Create a patch object with required fields
    const patchObject: any = {
      productId: product.id,
      productCode: product.code,
      productName: product.name
    };

    // Add optional fields if they exist
    if (product.unitId !== undefined) {
      patchObject.unitId = product.unitId;
    }

    if (product.unitName !== undefined) {
      patchObject.unitName = product.unitName;
    }

    itemForm.patchValue(patchObject);
  }

  calculateItemTotal(itemForm: FormGroup): void {
    const quantity = itemForm.get('quantity')?.value || 0;
    const price = itemForm.get('price')?.value || 0;
    const total = quantity * price;
    itemForm.get('total')?.setValue(total, { emitEvent: false });
    this.calculateTotals();
  }

  calculateSubTotal(): number {
    return this.items.controls
      .map(item => {
        const quantity = item.get('quantity')?.value || 0;
        const price = item.get('price')?.value || 0;
        return quantity * price;
      })
      .reduce((sum, current) => sum + current, 0);
  }

  calculateTax(): number {
    // Get tax rate from configuration (assuming 15% for now)
    const taxRate = 0.15;
    return this.calculateSubTotal() * taxRate;
  }

  calculateTotal(): number {
    return this.calculateSubTotal() + this.calculateTax();
  }

  calculateTotals(): void {
    // This method is called whenever an item's total changes
    // Update any UI elements that display totals
  }

  open(): void {
    // Implement open functionality
    this.snackBar.open('Open functionality not implemented yet', 'Close', {
      duration: 3000
    });
  }

  save(): void {
    if (this.stockRequestForm.invalid) {
      this.markFormGroupTouched(this.stockRequestForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
      return;
    }

    this.isLoading = true;

    // Get form values
    const formValue = this.stockRequestForm.getRawValue();

    // Filter out empty items
    formValue.items = formValue.items.filter((item: any) =>
      item.productId && item.quantity > 0
    );

    // Call API to save stock request
    this.stockService.createStockRequest(formValue).pipe(
      finalize(() => this.isLoading = false)
    ).subscribe({
      next: () => {
        this.snackBar.open('Stock request saved successfully', 'Close', {
          duration: 3000
        });
      },
      error: (error) => {
        console.error('Error saving stock request', error);
        this.snackBar.open('Error saving stock request. Please try again.', 'Close', {
          duration: 3000
        });
      }
    });
  }

  submit(): void {
    if (this.stockRequestForm.invalid) {
      this.markFormGroupTouched(this.stockRequestForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
      return;
    }

    this.isLoading = true;

    // Get form values
    const formValue = this.stockRequestForm.getRawValue();

    // Filter out empty items
    formValue.items = formValue.items.filter((item: any) =>
      item.productId && item.quantity > 0
    );

    // Add submit flag
    formValue.isSubmitted = true;

    // Call API to submit stock request
    this.stockService.submitStockRequest(formValue).pipe(
      finalize(() => this.isLoading = false)
    ).subscribe({
      next: () => {
        this.snackBar.open('Stock request submitted successfully', 'Close', {
          duration: 3000
        });
        this.resetForm();
      },
      error: (error) => {
        console.error('Error submitting stock request', error);
        this.snackBar.open('Error submitting stock request. Please try again.', 'Close', {
          duration: 3000
        });
      }
    });
  }

  discard(): void {
    this.resetForm();
    this.snackBar.open('Form has been reset', 'Close', {
      duration: 3000
    });
  }

  resetForm(): void {
    this.stockRequestForm.reset({
      requestDate: new Date()
    });

    // Clear items
    while (this.items.length > 0) {
      this.items.removeAt(0);
    }

    // Add empty row
    this.addItem();

    // Generate new request number
    this.stockRequestNo = 'SR-' + Math.floor(Math.random() * 10000).toString().padStart(5, '0');
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  canSubmit(): boolean {
    return this.permissionsService.hasPermission('inventory.stockrequest.submit');
  }
}
