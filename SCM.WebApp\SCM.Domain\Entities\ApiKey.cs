using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class ApiKey : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string KeyValue { get; set; } = string.Empty;
    public DateTime? ExpiresAt { get; set; }
    public int CreatedById { get; set; }
    public string? Permissions { get; set; }
    public DateTime? LastUsed { get; set; }
    
    // Navigation properties
    public virtual User CreatedBy { get; set; } = null!;
}
