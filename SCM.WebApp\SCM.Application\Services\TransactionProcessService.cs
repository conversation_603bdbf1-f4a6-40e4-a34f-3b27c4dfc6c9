using Mapster;
using MapsterMapper;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SCM.Application.Services
{
    public class TransactionProcessService : ITransactionProcessService
    {
        private readonly IRepository<TransactionProcess> _transactionProcessRepository;
        private readonly IMapper _mapper;

        public TransactionProcessService(IRepository<TransactionProcess> transactionProcessRepository, IMapper mapper)
        {
            _transactionProcessRepository = transactionProcessRepository ?? throw new ArgumentNullException(nameof(transactionProcessRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public async Task<IEnumerable<TransactionProcessDto>> GetAllAsync()
        {
            var transactionProcesses = await _transactionProcessRepository.GetAllAsync();
            return transactionProcesses.Adapt<IEnumerable<TransactionProcessDto>>();
        }

        public async Task<TransactionProcessDto> GetByIdAsync(int id)
        {
            var transactionProcess = await _transactionProcessRepository.GetByIdAsync(id);
            return transactionProcess?.Adapt<TransactionProcessDto>();
        }

        public async Task<TransactionProcessDto> CreateAsync(TransactionProcessDto transactionProcessDto)
        {
            var transactionProcess = transactionProcessDto.Adapt<TransactionProcess>();
            transactionProcess.CreatedAt = DateTime.UtcNow;
            transactionProcess.IsActive = true;

            await _transactionProcessRepository.AddAsync(transactionProcess);

            return transactionProcess.Adapt<TransactionProcessDto>();
        }

        public async Task UpdateAsync(int id, TransactionProcessDto transactionProcessDto)
        {
            var transactionProcess = await _transactionProcessRepository.GetByIdAsync(id);
            if (transactionProcess == null)
                throw new KeyNotFoundException($"TransactionProcess with ID {id} not found.");

            transactionProcessDto.Adapt(transactionProcess);
            transactionProcess.UpdatedAt = DateTime.UtcNow;

            await _transactionProcessRepository.UpdateAsync(transactionProcess);
        }

        public async Task DeleteAsync(int id)
        {
            var transactionProcess = await _transactionProcessRepository.GetByIdAsync(id);
            if (transactionProcess == null)
                throw new KeyNotFoundException($"TransactionProcess with ID {id} not found.");

            await _transactionProcessRepository.DeleteAsync(transactionProcess);
        }
    }
}
