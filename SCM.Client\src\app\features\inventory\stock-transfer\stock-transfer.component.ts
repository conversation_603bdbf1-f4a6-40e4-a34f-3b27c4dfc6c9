import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTabsModule } from '@angular/material/tabs';
import { Observable, map, startWith } from 'rxjs';

interface Product {
  id: string;
  name: string;
  unitSize: string;
  currentStock: number;
}

interface Transfer {
  id: string;
  date: Date;
  fromCostCenter: string;
  toCostCenter: string;
  status: string;
  createdBy: string;
}

@Component({
  selector: 'app-stock-transfer',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatTabsModule
  ],
  templateUrl: './stock-transfer.component.html',
  styleUrls: ['./stock-transfer.component.scss']
})
export class StockTransferComponent implements OnInit {
  transferForm!: FormGroup;
  transferNo: string = 'TRF-00001';
  currentDate: Date = new Date();
  
  displayedColumns: string[] = [
    'productId', 
    'productName', 
    'unitSize',
    'availableStock', 
    'transferQty', 
    'actions'
  ];
  
  historyColumns: string[] = [
    'id',
    'date',
    'fromCostCenter',
    'toCostCenter',
    'status',
    'createdBy',
    'actions'
  ];
  
  products: Product[] = [
    { id: 'P001', name: 'Rice', unitSize: '25kg', currentStock: 120 },
    { id: 'P002', name: 'Flour', unitSize: '10kg', currentStock: 85 },
    { id: 'P003', name: 'Soft Drinks', unitSize: '24x330ml', currentStock: 45 },
    { id: 'P004', name: 'Cleaning Liquid', unitSize: '5L', currentStock: 32 },
    { id: 'P005', name: 'Light Bulbs', unitSize: '10pcs', currentStock: 15 },
    { id: 'P006', name: 'Cigarettes', unitSize: '200pcs', currentStock: 25 },
    { id: 'P007', name: 'Paper Towels', unitSize: '12 rolls', currentStock: 40 },
    { id: 'P008', name: 'Coffee', unitSize: '1kg', currentStock: 18 }
  ];
  
  transfers: Transfer[] = [
    { id: 'TRF-00001', date: new Date(2025, 4, 5), fromCostCenter: '1 - Food Store', toCostCenter: '7 - Food Main Kitchen', status: 'Completed', createdBy: 'John Doe' },
    { id: 'TRF-00002', date: new Date(2025, 4, 3), fromCostCenter: '2 - Beverage Store', toCostCenter: '8 - Main Restaurant', status: 'Completed', createdBy: 'John Doe' },
    { id: 'TRF-00003', date: new Date(2025, 4, 1), fromCostCenter: '3 - General Store', toCostCenter: '4 - Engineering Store', status: 'Pending', createdBy: 'Jane Smith' },
    { id: 'TRF-00004', date: new Date(2025, 3, 28), fromCostCenter: '1 - Food Store', toCostCenter: '8 - Main Restaurant', status: 'Completed', createdBy: 'John Doe' }
  ];
  
  filteredProducts: Observable<Product[]>[] = [];
  
  costCenters = [
    { id: 1, name: '1 - Food Store' },
    { id: 2, name: '2 - Beverage Store' },
    { id: 3, name: '3 - General Store' },
    { id: 4, name: '4 - Engineering Store' },
    { id: 5, name: '5 - S.O.E. Store' },
    { id: 6, name: '6 - Tobacco' },
    { id: 7, name: '7 - Food Main Kitchen' },
    { id: 8, name: '8 - Main Restaurant' }
  ];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.transferForm = this.fb.group({
      transferDate: [this.currentDate, Validators.required],
      fromCostCenter: ['', Validators.required],
      toCostCenter: ['', Validators.required],
      notes: [''],
      items: this.fb.array([])
    });
    
    // Add an empty row by default
    this.addItem();
    
    // Add validation to prevent selecting the same cost center for from and to
    this.transferForm.get('fromCostCenter')?.valueChanges.subscribe(() => {
      this.validateCostCenters();
    });
    
    this.transferForm.get('toCostCenter')?.valueChanges.subscribe(() => {
      this.validateCostCenters();
    });
  }

  validateCostCenters(): void {
    const fromCostCenter = this.transferForm.get('fromCostCenter')?.value;
    const toCostCenter = this.transferForm.get('toCostCenter')?.value;
    
    if (fromCostCenter && toCostCenter && fromCostCenter === toCostCenter) {
      this.transferForm.get('toCostCenter')?.setErrors({ sameCostCenter: true });
    } else {
      const errors = this.transferForm.get('toCostCenter')?.errors;
      if (errors) {
        delete errors['sameCostCenter'];
        if (Object.keys(errors).length === 0) {
          this.transferForm.get('toCostCenter')?.setErrors(null);
        } else {
          this.transferForm.get('toCostCenter')?.setErrors(errors);
        }
      }
    }
  }

  get items(): FormArray {
    return this.transferForm.get('items') as FormArray;
  }

  addItem(): void {
    const itemForm = this.fb.group({
      productId: ['', Validators.required],
      productName: [''],
      unitSize: [''],
      availableStock: [{ value: 0, disabled: true }],
      transferQty: [0, [Validators.required, Validators.min(1)]]
    });
    
    // Set up product autocomplete filtering
    const index = this.items.length;
    this.setupProductAutocomplete(itemForm, index);
    
    // Add validation to prevent transferring more than available stock
    itemForm.get('transferQty')?.valueChanges.subscribe(() => {
      this.validateTransferQty(itemForm);
    });
    
    this.items.push(itemForm);
  }

  setupProductAutocomplete(itemForm: FormGroup, index: number): void {
    const productIdControl = itemForm.get('productId');
    
    this.filteredProducts[index] = productIdControl!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterProducts(value || ''))
    );
    
    productIdControl?.valueChanges.subscribe(productId => {
      const product = this.products.find(p => p.id === productId);
      if (product) {
        itemForm.patchValue({
          productName: product.name,
          unitSize: product.unitSize,
          availableStock: product.currentStock
        });
        this.validateTransferQty(itemForm);
      } else {
        itemForm.patchValue({
          productName: '',
          unitSize: '',
          availableStock: 0
        });
      }
    });
  }

  private _filterProducts(value: string): Product[] {
    const filterValue = value.toLowerCase();
    return this.products.filter(product => 
      product.id.toLowerCase().includes(filterValue) || 
      product.name.toLowerCase().includes(filterValue)
    );
  }

  validateTransferQty(itemForm: FormGroup): void {
    const availableStock = itemForm.get('availableStock')?.value || 0;
    const transferQty = itemForm.get('transferQty')?.value || 0;
    
    if (transferQty > availableStock) {
      itemForm.get('transferQty')?.setErrors({ exceedsStock: true });
    } else {
      const errors = itemForm.get('transferQty')?.errors;
      if (errors) {
        delete errors['exceedsStock'];
        if (Object.keys(errors).length === 0) {
          itemForm.get('transferQty')?.setErrors(null);
        } else {
          itemForm.get('transferQty')?.setErrors(errors);
        }
      }
    }
  }

  removeItem(index: number): void {
    this.items.removeAt(index);
    this.filteredProducts.splice(index, 1);
  }

  displayProductFn(productId: string): string {
    if (!productId) return '';
    const product = this.products.find(p => p.id === productId);
    return product ? `${product.id} - ${product.name}` : '';
  }

  saveTransfer(): void {
    if (this.transferForm.valid) {
      console.log('Transfer data:', this.transferForm.getRawValue());
      
      this.snackBar.open('Stock transfer saved successfully', 'Close', {
        duration: 3000
      });
      
      // Reset form after successful save
      this.initForm();
    } else {
      this.markFormGroupTouched(this.transferForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  viewTransfer(transfer: Transfer): void {
    console.log('View transfer:', transfer);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
