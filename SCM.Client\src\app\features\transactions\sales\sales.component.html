<div class="page-container">
  <div class="page-header">
    <h1>Sales</h1>
  </div>
  
  <mat-tab-group>
    <mat-tab label="New Sale">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>New Sale</h2>
          <div class="header-actions">
            <button mat-raised-button color="primary" (click)="saveSale()">Save Sale</button>
          </div>
        </div>
        
        <form [formGroup]="salesForm">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="saleDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
            
            <div class="spacer"></div>
            
            <div class="reference-number">
              <span>Invoice No.</span>
              <span class="reference-value">{{invoiceNo}}</span>
            </div>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Customer</mat-label>
              <mat-select formControlName="customer">
                <mat-option *ngFor="let customer of customers" [value]="customer.id">
                  {{customer.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="salesForm.get('customer')?.hasError('required')">
                Customer is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Cost Center</mat-label>
              <mat-select formControlName="costCenter">
                <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                  {{costCenter.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="salesForm.get('costCenter')?.hasError('required')">
                Cost Center is required
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Payment Method</mat-label>
              <mat-select formControlName="paymentMethod">
                <mat-option *ngFor="let method of paymentMethods" [value]="method.id">
                  {{method.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="salesForm.get('paymentMethod')?.hasError('required')">
                Payment Method is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Reference</mat-label>
              <input matInput formControlName="reference">
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <textarea matInput formControlName="notes" rows="2"></textarea>
            </mat-form-field>
          </div>
          
          <div class="table-container mat-elevation-z2">
            <table mat-table [dataSource]="items.controls">
              <!-- Product ID Column -->
              <ng-container matColumnDef="productId">
                <th mat-header-cell *matHeaderCellDef>Product</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('productId')" [matAutocomplete]="auto">
                    <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayProductFn">
                      <mat-option *ngFor="let product of filteredProducts[i] | async" [value]="product.id">
                        {{product.id}} - {{product.name}} ({{product.unitSize}})
                      </mat-option>
                    </mat-autocomplete>
                    <mat-error *ngIf="item.get('productId')?.hasError('required')">
                      Product is required
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Product Name Column -->
              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('productName')" readonly>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Unit Size Column -->
              <ng-container matColumnDef="unitSize">
                <th mat-header-cell *matHeaderCellDef>Unit Size</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('unitSize')" readonly>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Available Stock Column -->
              <ng-container matColumnDef="availableStock">
                <th mat-header-cell *matHeaderCellDef>Available</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('availableStock')" readonly>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('quantity')">
                    <mat-error *ngIf="item.get('quantity')?.hasError('required')">
                      Required
                    </mat-error>
                    <mat-error *ngIf="item.get('quantity')?.hasError('min')">
                      Min 1
                    </mat-error>
                    <mat-error *ngIf="item.get('quantity')?.hasError('exceedsStock')">
                      Exceeds stock
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Unit Price Column -->
              <ng-container matColumnDef="unitPrice">
                <th mat-header-cell *matHeaderCellDef>Unit Price</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('unitPrice')">
                    <span matTextPrefix>$&nbsp;</span>
                    <mat-error *ngIf="item.get('unitPrice')?.hasError('required')">
                      Required
                    </mat-error>
                    <mat-error *ngIf="item.get('unitPrice')?.hasError('min')">
                      Min 0
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Discount Column -->
              <ng-container matColumnDef="discount">
                <th mat-header-cell *matHeaderCellDef>Discount %</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('discount')">
                    <mat-error *ngIf="item.get('discount')?.hasError('min')">
                      Min 0
                    </mat-error>
                    <mat-error *ngIf="item.get('discount')?.hasError('max')">
                      Max 100
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Total Column -->
              <ng-container matColumnDef="total">
                <th mat-header-cell *matHeaderCellDef>Total</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('total')" readonly>
                    <span matTextPrefix>$&nbsp;</span>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <button mat-icon-button color="warn" (click)="removeItem(i)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
            
            <div class="table-actions">
              <button mat-button color="primary" (click)="addItem()">
                <mat-icon>add</mat-icon> Add Item
              </button>
            </div>
          </div>
          
          <div class="totals-section">
            <div class="totals-row">
              <span>Total:</span>
              <span class="total-value">{{calculateOrderTotal() | currency}}</span>
            </div>
          </div>
        </form>
      </div>
    </mat-tab>
    
    <mat-tab label="Sales History">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>Sales History</h2>
        </div>
        
        <div class="table-container mat-elevation-z2">
          <table mat-table [dataSource]="sales" class="history-table">
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>Invoice ID</th>
              <td mat-cell *matCellDef="let sale">{{sale.id}}</td>
            </ng-container>
            
            <!-- Date Column -->
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef>Date</th>
              <td mat-cell *matCellDef="let sale">{{sale.date | date:'shortDate'}}</td>
            </ng-container>
            
            <!-- Customer Column -->
            <ng-container matColumnDef="customer">
              <th mat-header-cell *matHeaderCellDef>Customer</th>
              <td mat-cell *matCellDef="let sale">{{sale.customer}}</td>
            </ng-container>
            
            <!-- Cost Center Column -->
            <ng-container matColumnDef="costCenter">
              <th mat-header-cell *matHeaderCellDef>Cost Center</th>
              <td mat-cell *matCellDef="let sale">{{sale.costCenter}}</td>
            </ng-container>
            
            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let sale" 
                  [ngClass]="{'status-pending': sale.status === 'Pending', 
                             'status-completed': sale.status === 'Completed'}">
                {{sale.status}}
              </td>
            </ng-container>
            
            <!-- Total Column -->
            <ng-container matColumnDef="total">
              <th mat-header-cell *matHeaderCellDef>Total</th>
              <td mat-cell *matCellDef="let sale">{{sale.total | currency}}</td>
            </ng-container>
            
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let sale">
                <button mat-icon-button color="primary" (click)="viewSale(sale)">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>
            
            <tr mat-header-row *matHeaderRowDef="historyColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: historyColumns;"></tr>
          </table>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
