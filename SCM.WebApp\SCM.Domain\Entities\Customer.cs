using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Customer : BaseEntity
{
    public string? CustomerCode { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string? LastName { get; set; }
    public string? CompanyName { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public string? PostalCode { get; set; }
    public string? TaxNumber { get; set; }
    public decimal? CreditLimit { get; set; }
    public decimal? DiscountPercentage { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual ICollection<TransactionHeader> Transactions { get; set; } = new List<TransactionHeader>();
}
