using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class PurchaseOrder : BaseEntity
{
    public string DocumentNumber { get; set; } = string.Empty;
    public int SupplierId { get; set; }
    public int CostCenterId { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime? ExpectedDeliveryDate { get; set; }
    public string Status { get; set; } = "Draft"; // Draft, Pending, Approved, Rejected, Completed, Cancelled
    public string? Notes { get; set; }
    public decimal TotalAmount { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public Supplier? Supplier { get; set; }
    public CostCenter? CostCenter { get; set; }
    public ICollection<PurchaseOrderDetail> Details { get; set; } = new List<PurchaseOrderDetail>();
}

public class PurchaseOrderDetail : BaseEntity
{
    public int PurchaseOrderId { get; set; }
    public int ProductId { get; set; }
    public decimal Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public string? Notes { get; set; }

    // Navigation properties
    public PurchaseOrder? PurchaseOrder { get; set; }
    public Product? Product { get; set; }
}
