import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { ProductService } from '../../core/services/product.service';
import { StockService } from '../../core/services/stock.service';
import { forkJoin, catchError, of, finalize } from 'rxjs';

interface InventorySummary {
  totalProducts: number;
  lowStockItems: number;
  pendingRequests: number;
  pendingTransfers: number;
  totalValue: number;
}

interface Transaction {
  id: string;
  date: Date;
  type: string;
  amount: number;
  status: string;
  costCenter: string;
}

interface LowStockItem {
  id: string;
  name: string;
  currentStock: number;
  minLevel: number;
  costCenter: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  today: Date = new Date();
  isLoading: boolean = false;

  inventorySummary: InventorySummary = {
    totalProducts: 0,
    lowStockItems: 0,
    pendingRequests: 0,
    pendingTransfers: 0,
    totalValue: 0
  };

  recentTransactions: Transaction[] = [];
  lowStockItems: LowStockItem[] = [];

  transactionColumns: string[] = ['id', 'date', 'type', 'amount', 'status', 'costCenter', 'actions'];
  lowStockColumns: string[] = ['id', 'name', 'currentStock', 'minLevel', 'costCenter', 'actions'];

  constructor(
    private productService: ProductService,
    private stockService: StockService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.isLoading = true;

    // Load data from multiple sources in parallel
    forkJoin({
      products: this.productService.getAll().pipe(catchError(error => {
        console.error('Error loading products', error);
        return of([]);
      })),
      lowStock: this.stockService.getLowStockItems().pipe(catchError(error => {
        console.error('Error loading low stock items', error);
        return of([]);
      }))
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe({
      next: (data) => {
        // Update inventory summary
        this.inventorySummary = {
          totalProducts: data.products.length,
          lowStockItems: data.lowStock.length,
          pendingRequests: 0, // This would come from a transaction service
          pendingTransfers: 0, // This would come from a transaction service
          totalValue: data.lowStock.reduce((sum, item) => sum + (item.averageCostPrice || 0) * item.totalQuantity, 0)
        };

        // Map low stock items
        this.lowStockItems = data.lowStock.map(item => ({
          id: item.productCode,
          name: item.productName,
          currentStock: item.totalQuantity,
          minLevel: item.minStock || 0,
          costCenter: item.costCenterName
        })).slice(0, 5); // Show only the first 5 items

        // For transactions, we would need a transaction service
        // For now, we'll use placeholder data
        this.recentTransactions = [
          {
            id: 'TR-001',
            date: new Date(),
            type: 'Purchase',
            amount: 12500,
            status: 'Completed',
            costCenter: '1 - Food Store'
          },
          {
            id: 'TR-002',
            date: new Date(Date.now() - 86400000), // Yesterday
            type: 'Transfer',
            amount: 5600,
            status: 'Pending',
            costCenter: '2 - Beverage Store'
          }
        ];
      },
      error: (error) => {
        console.error('Error loading dashboard data', error);
        this.snackBar.open('Error loading dashboard data. Please try again later.', 'Close', {
          duration: 5000
        });
      }
    });
  }

  createStockRequest(): void {
    this.router.navigate(['/inventory/stock-request']);
  }

  viewAllTransactions(): void {
    this.router.navigate(['/transactions/purchase-orders']);
  }

  viewAllLowStockItems(): void {
    this.router.navigate(['/inventory/stock-taking']);
  }
}
