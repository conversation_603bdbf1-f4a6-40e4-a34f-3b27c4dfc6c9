import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

export interface Tax {
  id: number;
  name: string;
  rate: number;
  isDefault: boolean;
  isActive: boolean;
}

export interface CreateTax {
  name: string;
  rate: number;
  isDefault: boolean;
}

export interface UpdateTax {
  id: number;
  name: string;
  rate: number;
  isDefault: boolean;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class TaxService {
  private readonly path = 'taxes';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<Tax[]> {
    return this.apiService.get<Tax[]>(this.path);
  }

  getById(id: number): Observable<Tax> {
    return this.apiService.get<Tax>(`${this.path}/${id}`);
  }

  getDefault(): Observable<Tax> {
    return this.apiService.get<Tax>(`${this.path}/default`);
  }

  create(tax: CreateTax): Observable<Tax> {
    return this.apiService.post<Tax, CreateTax>(this.path, tax);
  }

  update(id: number, tax: UpdateTax): Observable<void> {
    return this.apiService.put<void, UpdateTax>(`${this.path}/${id}`, tax);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
