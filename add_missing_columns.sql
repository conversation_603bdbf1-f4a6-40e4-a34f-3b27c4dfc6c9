-- Add missing columns to database tables
USE [InventoryManagement]
GO

-- Brand table: Missing Description and LogoUrl columns
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'Brand' AND COLUMN_NAME = 'Description'
)
BEGIN
    ALTER TABLE [dbo].[Brand]
    ADD [Description] [nvarchar](255) NULL

    PRINT 'Description column added to Brand table.'
END
ELSE
BEGIN
    PRINT 'Description column already exists in Brand table.'
END

IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'Brand' AND COLUMN_NAME = 'LogoUrl'
)
BEGIN
    ALTER TABLE [dbo].[Brand]
    ADD [LogoUrl] [nvarchar](255) NULL

    PRINT 'LogoUrl column added to Brand table.'
END
ELSE
BEGIN
    PRINT 'LogoUrl column already exists in Brand table.'
END

-- Unit table: Missing ConversionFactor and IsBaseUnit columns
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'Unit' AND COLUMN_NAME = 'ConversionFactor'
)
BEGIN
    ALTER TABLE [dbo].[Unit]
    ADD [ConversionFactor] [decimal](18, 4) NULL

    PRINT 'ConversionFactor column added to Unit table.'
END
ELSE
BEGIN
    PRINT 'ConversionFactor column already exists in Unit table.'
END

IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'Unit' AND COLUMN_NAME = 'IsBaseUnit'
)
BEGIN
    ALTER TABLE [dbo].[Unit]
    ADD [IsBaseUnit] [bit] NULL

    PRINT 'IsBaseUnit column added to Unit table.'
END
ELSE
BEGIN
    PRINT 'IsBaseUnit column already exists in Unit table.'
END

-- Department table: Missing Description column
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'Department' AND COLUMN_NAME = 'Description'
)
BEGIN
    ALTER TABLE [dbo].[Department]
    ADD [Description] [nvarchar](255) NULL

    PRINT 'Description column added to Department table.'
END
ELSE
BEGIN
    PRINT 'Description column already exists in Department table.'
END

-- ProductGroup table: Missing Description column
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'ProductGroup' AND COLUMN_NAME = 'Description'
)
BEGIN
    ALTER TABLE [dbo].[ProductGroup]
    ADD [Description] [nvarchar](255) NULL

    PRINT 'Description column added to ProductGroup table.'
END
ELSE
BEGIN
    PRINT 'Description column already exists in ProductGroup table.'
END

-- ProductSubGroup table: Missing Description column
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'ProductSubGroup' AND COLUMN_NAME = 'Description'
)
BEGIN
    ALTER TABLE [dbo].[ProductSubGroup]
    ADD [Description] [nvarchar](255) NULL

    PRINT 'Description column added to ProductSubGroup table.'
END
ELSE
BEGIN
    PRINT 'Description column already exists in ProductSubGroup table.'
END

-- UnitGroup table: Missing Description column
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'UnitGroup' AND COLUMN_NAME = 'Description'
)
BEGIN
    ALTER TABLE [dbo].[UnitGroup]
    ADD [Description] [nvarchar](255) NULL

    PRINT 'Description column added to UnitGroup table.'
END
ELSE
BEGIN
    PRINT 'Description column already exists in UnitGroup table.'
END

-- Supplier table: Missing BankAccount and BankName columns
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'Supplier' AND COLUMN_NAME = 'BankAccount'
)
BEGIN
    ALTER TABLE [dbo].[Supplier]
    ADD [BankAccount] [nvarchar](50) NULL

    PRINT 'BankAccount column added to Supplier table.'
END
ELSE
BEGIN
    PRINT 'BankAccount column already exists in Supplier table.'
END

IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'Supplier' AND COLUMN_NAME = 'BankName'
)
BEGIN
    ALTER TABLE [dbo].[Supplier]
    ADD [BankName] [nvarchar](100) NULL

    PRINT 'BankName column added to Supplier table.'
END
ELSE
BEGIN
    PRINT 'BankName column already exists in Supplier table.'
END

-- CostCenter table: Missing LocationId column
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'CostCenter' AND COLUMN_NAME = 'LocationId'
)
BEGIN
    ALTER TABLE [dbo].[CostCenter]
    ADD [LocationId] [int] NULL

    PRINT 'LocationId column added to CostCenter table.'
END
ELSE
BEGIN
    PRINT 'LocationId column already exists in CostCenter table.'
END
GO
