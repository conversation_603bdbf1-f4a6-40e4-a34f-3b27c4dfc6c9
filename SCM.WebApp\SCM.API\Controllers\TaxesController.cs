using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class TaxesController : ApiControllerBase
{
    private readonly ITaxService _taxService;

    public TaxesController(ITaxService taxService)
    {
        _taxService = taxService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<TaxDto>>> GetAll()
    {
        var taxes = await _taxService.GetAllTaxesAsync();
        return Ok(taxes);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<TaxDto>> GetById(int id)
    {
        var tax = await _taxService.GetTaxByIdAsync(id);
        if (tax == null)
            return NotFound();

        return Ok(tax);
    }

    [HttpGet("default")]
    public async Task<ActionResult<TaxDto>> GetDefault()
    {
        var tax = await _taxService.GetDefaultTaxAsync();
        if (tax == null)
            return NotFound();

        return Ok(tax);
    }

    [HttpPost]
    public async Task<ActionResult<TaxDto>> Create(CreateTaxDto createTaxDto)
    {
        var tax = await _taxService.CreateTaxAsync(createTaxDto);
        return CreatedAtAction(nameof(GetById), new { id = tax.Id }, tax);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateTaxDto updateTaxDto)
    {
        if (id != updateTaxDto.Id)
            return BadRequest();

        try
        {
            await _taxService.UpdateTaxAsync(updateTaxDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _taxService.DeleteTaxAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
