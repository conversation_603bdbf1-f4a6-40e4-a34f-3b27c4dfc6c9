using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IUnitGroupService
{
    Task<IEnumerable<UnitGroupDto>> GetAllUnitGroupsAsync();
    Task<UnitGroupDto?> GetUnitGroupByIdAsync(int id);
    Task<UnitGroupDto?> GetUnitGroupWithUnitsAsync(int id);
    Task<UnitGroupDto> CreateUnitGroupAsync(CreateUnitGroupDto createUnitGroupDto);
    Task UpdateUnitGroupAsync(UpdateUnitGroupDto updateUnitGroupDto);
    Task DeleteUnitGroupAsync(int id);
}
