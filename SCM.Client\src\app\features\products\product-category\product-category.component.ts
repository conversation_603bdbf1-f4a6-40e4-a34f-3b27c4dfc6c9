import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';

interface ProductCategory {
  id: number;
  name: string;
  description: string;
  productCount: number;
}

@Component({
  selector: 'app-product-category',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatTableModule,
    MatSnackBarModule
  ],
  templateUrl: './product-category.component.html',
  styleUrls: ['./product-category.component.scss']
})
export class ProductCategoryComponent implements OnInit {
  categoryForm!: FormGroup;
  displayedColumns: string[] = ['id', 'name', 'description', 'productCount', 'actions'];
  categories: ProductCategory[] = [];
  selectedCategory: ProductCategory | null = null;
  isEditMode: boolean = false;

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    // Mock data for demonstration
    this.categories = [
      {
        id: 1,
        name: 'Food',
        description: 'Food items for the kitchen',
        productCount: 25
      },
      {
        id: 2,
        name: 'Beverage',
        description: 'Drinks and beverages',
        productCount: 18
      },
      {
        id: 3,
        name: 'Cleaning Supplies',
        description: 'Cleaning products and supplies',
        productCount: 12
      },
      {
        id: 4,
        name: 'Office Supplies',
        description: 'Office stationery and supplies',
        productCount: 8
      },
      {
        id: 5,
        name: 'Engineering',
        description: 'Engineering tools and parts',
        productCount: 15
      },
      {
        id: 6,
        name: 'Tobacco',
        description: 'Tobacco products',
        productCount: 5
      }
    ];
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.categoryForm = this.fb.group({
      name: ['', Validators.required],
      description: ['']
    });
  }

  saveCategory(): void {
    if (this.categoryForm.valid) {
      if (this.isEditMode && this.selectedCategory) {
        // Update existing category
        const index = this.categories.findIndex(c => c.id === this.selectedCategory!.id);
        if (index !== -1) {
          this.categories[index] = {
            ...this.selectedCategory,
            name: this.categoryForm.value.name,
            description: this.categoryForm.value.description
          };
          
          this.snackBar.open('Category updated successfully', 'Close', {
            duration: 3000
          });
        }
      } else {
        // Add new category
        const newId = Math.max(...this.categories.map(c => c.id)) + 1;
        this.categories.push({
          id: newId,
          name: this.categoryForm.value.name,
          description: this.categoryForm.value.description,
          productCount: 0
        });
        
        this.snackBar.open('Category added successfully', 'Close', {
          duration: 3000
        });
      }
      
      this.resetForm();
    }
  }

  editCategory(category: ProductCategory): void {
    this.selectedCategory = category;
    this.isEditMode = true;
    this.categoryForm.patchValue({
      name: category.name,
      description: category.description
    });
  }

  deleteCategory(category: ProductCategory): void {
    // In a real application, you would show a confirmation dialog
    const index = this.categories.findIndex(c => c.id === category.id);
    if (index !== -1) {
      this.categories.splice(index, 1);
      
      this.snackBar.open('Category deleted successfully', 'Close', {
        duration: 3000
      });
      
      if (this.selectedCategory && this.selectedCategory.id === category.id) {
        this.resetForm();
      }
    }
  }

  resetForm(): void {
    this.categoryForm.reset();
    this.selectedCategory = null;
    this.isEditMode = false;
  }

  cancelEdit(): void {
    this.resetForm();
  }
}
