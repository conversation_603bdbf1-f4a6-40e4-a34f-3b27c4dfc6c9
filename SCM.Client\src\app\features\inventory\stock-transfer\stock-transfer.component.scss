.page-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.tab-content {
  padding: 20px 0;
}

.page-subheader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}

.full-width {
  width: 100%;
}

.spacer {
  flex: 1;
}

.reference-number {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.reference-value {
  font-weight: bold;
  font-size: 1.2em;
  color: #1976d2;
}

.table-container {
  margin: 20px 0;
  overflow-x: auto;
  border-radius: 4px;
}

table {
  width: 100%;
}

.table-form-field {
  width: 100%;
  margin: 0;
  font-size: 14px;
}

.table-form-field ::ng-deep .mat-mdc-form-field-infix {
  padding: 8px 0;
  width: auto;
}

.table-form-field ::ng-deep .mat-mdc-text-field-wrapper {
  padding: 0 8px;
}

.table-actions {
  padding: 8px;
  display: flex;
  justify-content: flex-start;
}

.history-table {
  width: 100%;
}

.status-completed {
  color: #4caf50;
  font-weight: 500;
}

.status-pending {
  color: #ff9800;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .page-subheader {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    margin-top: 10px;
  }
  
  .form-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .reference-number {
    align-items: flex-start;
    margin-top: 10px;
  }
}
