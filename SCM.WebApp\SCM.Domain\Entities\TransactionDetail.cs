using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionDetail : BaseEntity
{
    public int TransactionHeaderId { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? TaxRate { get; set; }
    public int? TaxId { get; set; }
    public decimal? TaxAmount { get; set; }
    public decimal? DiscountPercent { get; set; }
    public decimal? DiscountAmount { get; set; }
    public decimal? TotalAmount { get; set; }
    public string? Notes { get; set; }
    public int LineNumber { get; set; }
    
    // Navigation properties
    public virtual TransactionHeader TransactionHeader { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
    public virtual Batch? Batch { get; set; }
    public virtual Unit? Unit { get; set; }
    public virtual Tax? Tax { get; set; }
}
