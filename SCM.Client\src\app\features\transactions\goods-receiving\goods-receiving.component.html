<div class="page-container">
  <div class="page-header">
    <h1>Goods Receiving</h1>
  </div>
  
  <mat-tab-group>
    <mat-tab label="New Goods Receipt">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>New Goods Receipt</h2>
          <div class="header-actions">
            <button mat-raised-button color="primary" (click)="saveGoodsReceipt()">Save Receipt</button>
          </div>
        </div>
        
        <form [formGroup]="grForm">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="grDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Purchase Order</mat-label>
              <mat-select formControlName="poNumber">
                <mat-option *ngFor="let po of purchaseOrders" [value]="po.id">
                  {{po.id}} - {{po.supplierName}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="grForm.get('poNumber')?.hasError('required')">
                Purchase Order is required
              </mat-error>
            </mat-form-field>
            
            <div class="reference-number">
              <span>GR No.</span>
              <span class="reference-value">{{grNo}}</span>
            </div>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Supplier</mat-label>
              <input matInput formControlName="supplier" readonly>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Cost Center</mat-label>
              <input matInput formControlName="costCenter" readonly>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Received By</mat-label>
              <input matInput formControlName="receivedBy">
              <mat-error *ngIf="grForm.get('receivedBy')?.hasError('required')">
                Received By is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Invoice Number</mat-label>
              <input matInput formControlName="invoiceNumber">
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <textarea matInput formControlName="notes" rows="2"></textarea>
            </mat-form-field>
          </div>
          
          <div class="table-container mat-elevation-z2">
            <table mat-table [dataSource]="items.controls">
              <!-- Select Column -->
              <ng-container matColumnDef="select">
                <th mat-header-cell *matHeaderCellDef>
                  <mat-checkbox [checked]="true" disabled>
                  </mat-checkbox>
                </th>
                <td mat-cell *matCellDef="let item">
                  <mat-checkbox [formControl]="item.get('selected')">
                  </mat-checkbox>
                </td>
              </ng-container>
              
              <!-- Product ID Column -->
              <ng-container matColumnDef="productId">
                <th mat-header-cell *matHeaderCellDef>Product ID</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('productId').value}}
                </td>
              </ng-container>
              
              <!-- Product Name Column -->
              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('productName').value}}
                </td>
              </ng-container>
              
              <!-- Unit Size Column -->
              <ng-container matColumnDef="unitSize">
                <th mat-header-cell *matHeaderCellDef>Unit Size</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('unitSize').value}}
                </td>
              </ng-container>
              
              <!-- Ordered Qty Column -->
              <ng-container matColumnDef="orderedQty">
                <th mat-header-cell *matHeaderCellDef>Ordered Qty</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('orderedQty').value}}
                </td>
              </ng-container>
              
              <!-- Received Qty Column -->
              <ng-container matColumnDef="receivedQty">
                <th mat-header-cell *matHeaderCellDef>Received Qty</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('receivedQty')">
                    <mat-error *ngIf="item.get('receivedQty')?.hasError('required')">
                      Required
                    </mat-error>
                    <mat-error *ngIf="item.get('receivedQty')?.hasError('min')">
                      Min 0
                    </mat-error>
                    <mat-error *ngIf="item.get('receivedQty')?.hasError('max')">
                      Max {{item.get('orderedQty').value}}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>
              
              <!-- Unit Price Column -->
              <ng-container matColumnDef="unitPrice">
                <th mat-header-cell *matHeaderCellDef>Unit Price</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('unitPrice').value | currency}}
                </td>
              </ng-container>
              
              <!-- Total Column -->
              <ng-container matColumnDef="total">
                <th mat-header-cell *matHeaderCellDef>Total</th>
                <td mat-cell *matCellDef="let item">
                  {{item.get('total').value | currency}}
                </td>
              </ng-container>
              
              <!-- Notes Column -->
              <ng-container matColumnDef="notes">
                <th mat-header-cell *matHeaderCellDef>Notes</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput [formControl]="item.get('notes')">
                  </mat-form-field>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
          
          <div class="totals-section">
            <div class="totals-row">
              <span>Total:</span>
              <span class="total-value">{{calculateGrandTotal() | currency}}</span>
            </div>
          </div>
        </form>
      </div>
    </mat-tab>
    
    <mat-tab label="Goods Receipt History">
      <div class="tab-content">
        <div class="page-subheader">
          <h2>Goods Receipt History</h2>
        </div>
        
        <div class="table-container mat-elevation-z2">
          <table mat-table [dataSource]="goodsReceipts" class="history-table">
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>GR ID</th>
              <td mat-cell *matCellDef="let gr">{{gr.id}}</td>
            </ng-container>
            
            <!-- Date Column -->
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef>Date</th>
              <td mat-cell *matCellDef="let gr">{{gr.date | date:'shortDate'}}</td>
            </ng-container>
            
            <!-- PO Number Column -->
            <ng-container matColumnDef="poNumber">
              <th mat-header-cell *matHeaderCellDef>PO Number</th>
              <td mat-cell *matCellDef="let gr">{{gr.poNumber}}</td>
            </ng-container>
            
            <!-- Supplier Column -->
            <ng-container matColumnDef="supplier">
              <th mat-header-cell *matHeaderCellDef>Supplier</th>
              <td mat-cell *matCellDef="let gr">{{gr.supplier}}</td>
            </ng-container>
            
            <!-- Cost Center Column -->
            <ng-container matColumnDef="costCenter">
              <th mat-header-cell *matHeaderCellDef>Cost Center</th>
              <td mat-cell *matCellDef="let gr">{{gr.costCenter}}</td>
            </ng-container>
            
            <!-- Received By Column -->
            <ng-container matColumnDef="receivedBy">
              <th mat-header-cell *matHeaderCellDef>Received By</th>
              <td mat-cell *matCellDef="let gr">{{gr.receivedBy}}</td>
            </ng-container>
            
            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let gr" [ngClass]="{'status-completed': gr.status === 'Completed'}">
                {{gr.status}}
              </td>
            </ng-container>
            
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let gr">
                <button mat-icon-button color="primary" (click)="viewGoodsReceipt(gr)">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>
            
            <tr mat-header-row *matHeaderRowDef="historyColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: historyColumns;"></tr>
          </table>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
