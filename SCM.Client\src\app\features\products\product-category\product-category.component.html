<div class="page-container">
  <div class="page-header">
    <h1>Product Categories</h1>
  </div>
  
  <div class="content-container">
    <div class="form-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>{{ isEditMode ? 'Edit Category' : 'Add Category' }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="categoryForm" class="category-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Category Name</mat-label>
              <input matInput formControlName="name">
              <mat-error *ngIf="categoryForm.get('name')?.hasError('required')">
                Category Name is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" rows="3"></textarea>
            </mat-form-field>
            
            <div class="form-actions">
              <button mat-button *ngIf="isEditMode" (click)="cancelEdit()">Cancel</button>
              <button mat-raised-button color="primary" (click)="saveCategory()" [disabled]="categoryForm.invalid">
                {{ isEditMode ? 'Update' : 'Add' }}
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
    
    <div class="table-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Categories</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="table-container mat-elevation-z0">
            <table mat-table [dataSource]="categories" class="category-table">
              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let category">{{category.id}}</td>
              </ng-container>
              
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let category">{{category.name}}</td>
              </ng-container>
              
              <!-- Description Column -->
              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let category">{{category.description}}</td>
              </ng-container>
              
              <!-- Product Count Column -->
              <ng-container matColumnDef="productCount">
                <th mat-header-cell *matHeaderCellDef>Products</th>
                <td mat-cell *matCellDef="let category">{{category.productCount}}</td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let category">
                  <button mat-icon-button color="primary" (click)="editCategory(category)">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteCategory(category)" [disabled]="category.productCount > 0">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
