namespace SCM.Application.DTOs;

public class LocationDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? StoreId { get; set; }
    public string? StoreName { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<CostCenterDto>? CostCenters { get; set; }
}

public class CreateLocationDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? StoreId { get; set; }
}

public class UpdateLocationDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? StoreId { get; set; }
    public bool IsActive { get; set; }
}
