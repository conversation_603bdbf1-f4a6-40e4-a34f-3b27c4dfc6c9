using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionType : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Direction { get; set; } = "In"; // In, Out, Transfer
    public bool AffectsStock { get; set; } = true;
    public bool RequiresApproval { get; set; } = false;
    
    // Navigation properties
    public virtual ICollection<TransactionProcess> Processes { get; set; } = new List<TransactionProcess>();
    public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; } = new List<TransactionHeader>();
}
