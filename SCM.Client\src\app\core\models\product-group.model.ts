export interface ProductGroup {
  id: number;
  name: string;
  description?: string;
  departmentId: number;
  departmentName?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateProductGroup {
  name: string;
  description?: string;
  departmentId: number;
}

export interface UpdateProductGroup {
  id: number;
  name: string;
  description?: string;
  departmentId: number;
  isActive: boolean;
}
