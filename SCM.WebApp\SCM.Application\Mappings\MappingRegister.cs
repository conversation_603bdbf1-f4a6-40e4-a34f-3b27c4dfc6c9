using Mapster;
using MapsterMapper;
using Microsoft.Extensions.DependencyInjection;
using SCM.Application.DTOs;
using SCM.Domain.Entities;
using System.Reflection;

namespace SCM.Application.Mappings
{
    public static class MappingRegister
    {
        public static void AddMappings(this IServiceCollection services)
        {
            // Create a type adapter config
            var config = TypeAdapterConfig.GlobalSettings;

            // Scan for all assemblies in the Application project
            config.Scan(Assembly.GetExecutingAssembly());

            // Register the mappings
            RegisterMappings(config);

            // Register the mapper as a singleton
            services.AddSingleton(config);
            services.AddScoped<MapsterMapper.IMapper, ServiceMapper>();
        }

        private static void RegisterMappings(TypeAdapterConfig config)
        {
            // Department mappings
            config.NewConfig<Department, DepartmentDto>()
                .Map(dest => dest.ProductGroupCount, src => src.ProductGroups != null ? src.ProductGroups.Count : 0);
            config.NewConfig<CreateDepartmentDto, Department>();
            config.NewConfig<UpdateDepartmentDto, Department>();

            // UnitGroup mappings
            config.NewConfig<UnitGroup, UnitGroupDto>()
                .Map(dest => dest.UnitCount, src => src.Units != null ? src.Units.Count : 0);
            config.NewConfig<CreateUnitGroupDto, UnitGroup>();
            config.NewConfig<UpdateUnitGroupDto, UnitGroup>();

            // Unit mappings
            config.NewConfig<Unit, UnitDto>()
                .Map(dest => dest.UnitGroupName, src => src.UnitGroup != null ? src.UnitGroup.Name : null);
            config.NewConfig<CreateUnitDto, Unit>();
            config.NewConfig<UpdateUnitDto, Unit>();

            // ProductGroup mappings
            config.NewConfig<ProductGroup, ProductGroupDto>()
                .Map(dest => dest.DepartmentName, src => src.Department != null ? src.Department.Name : null)
                .Map(dest => dest.SubGroupCount, src => src.SubGroups != null ? src.SubGroups.Count : 0);
            config.NewConfig<CreateProductGroupDto, ProductGroup>();
            config.NewConfig<UpdateProductGroupDto, ProductGroup>();

            // ProductSubGroup mappings
            config.NewConfig<ProductSubGroup, ProductSubGroupDto>()
                .Map(dest => dest.GroupName, src => src.Group != null ? src.Group.Name : null)
                .Map(dest => dest.DepartmentName, src => src.Group != null && src.Group.Department != null ? src.Group.Department.Name : null);
            config.NewConfig<CreateProductSubGroupDto, ProductSubGroup>();
            config.NewConfig<UpdateProductSubGroupDto, ProductSubGroup>();

            // Brand mappings
            config.NewConfig<Brand, BrandDto>();
            config.NewConfig<CreateBrandDto, Brand>();
            config.NewConfig<UpdateBrandDto, Brand>();

            // Tax mappings
            config.NewConfig<Tax, TaxDto>();
            config.NewConfig<CreateTaxDto, Tax>();
            config.NewConfig<UpdateTaxDto, Tax>();

            // Product mappings
            config.NewConfig<Product, ProductDto>()
                .Map(dest => dest.BrandName, src => src.Brand != null ? src.Brand.Name : null)
                .Map(dest => dest.UnitName, src => src.Unit != null ? src.Unit.Name : null)
                .Map(dest => dest.UnitGroupName, src => src.Unit != null && src.Unit.UnitGroup != null ? src.Unit.UnitGroup.Name : null)
                .Map(dest => dest.DepartmentName, src => src.Department != null ? src.Department.Name : null)
                .Map(dest => dest.GroupName, src => src.Group != null ? src.Group.Name : null)
                .Map(dest => dest.SubGroupName, src => src.SubGroup != null ? src.SubGroup.Name : null)
                .Map(dest => dest.TaxName, src => src.Tax != null ? src.Tax.Name : null)
                .Map(dest => dest.TaxRate, src => src.Tax != null ? (decimal?)src.Tax.Rate : null)
                .Map(dest => dest.SalesUnitName, src => src.SalesUnit != null ? src.SalesUnit.Name : null);
            config.NewConfig<CreateProductDto, Product>();
            config.NewConfig<UpdateProductDto, Product>();

            // Store mappings
            config.NewConfig<Store, StoreDto>();
            config.NewConfig<CreateStoreDto, Store>();
            config.NewConfig<UpdateStoreDto, Store>();

            // Location mappings
            config.NewConfig<Location, LocationDto>()
                .Map(dest => dest.StoreName, src => src.Store != null ? src.Store.Name : null);
            config.NewConfig<CreateLocationDto, Location>();
            config.NewConfig<UpdateLocationDto, Location>();

            // TransactionProcess mappings
            config.NewConfig<TransactionProcess, TransactionProcessDto>();
            config.NewConfig<TransactionProcessDto, TransactionProcess>();
        }
    }
}
