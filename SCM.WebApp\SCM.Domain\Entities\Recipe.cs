using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Recipe : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public decimal Yield { get; set; } = 1;
    public int? UnitId { get; set; }
    public string? Instructions { get; set; }
    public string? Notes { get; set; }
    public decimal? Cost { get; set; }
    public bool IsSubRecipe { get; set; } = false;
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual Product Product { get; set; } = null!;
    public virtual Unit? Unit { get; set; }
    public virtual ICollection<RecipeIngredient> Ingredients { get; set; } = new List<RecipeIngredient>();
}
