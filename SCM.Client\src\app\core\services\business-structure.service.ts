import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiService } from './api.service';

export interface BusinessUnit {
  id: number;
  name: string;
  children?: BusinessLocation[];
}

export interface BusinessLocation {
  id: number;
  name: string;
  businessUnitId: number;
  children?: Store[];
}

export interface Store {
  id: number;
  name: string;
  locationId: number;
  children?: Department[];
}

export interface Department {
  id: number;
  name: string;
  storeId: number;
  children?: any[]; // Optional children property for nested departments if needed
}

@Injectable({
  providedIn: 'root'
})
export class BusinessStructureService {
  private readonly path = 'business-structure';

  constructor(private apiService: ApiService) { }

  // In a real implementation, these would be API calls
  // For now, we'll use mock data to match the old application structure
  getBusinessStructure(): Observable<BusinessUnit[]> {
    // Mock data based on the screenshots
    const mockData: BusinessUnit[] = [
      {
        id: 1,
        name: 'Business Unit',
        children: [
          {
            id: 1,
            name: 'AS MIRANDA',
            businessUnitId: 1,
            children: [
              {
                id: 1,
                name: 'DNA',
                locationId: 1,
                children: [
                  {
                    id: 1,
                    name: 'Main Store',
                    storeId: 1,
                    children: [
                      { id: 1, name: 'Food Store', storeId: 1 },
                      { id: 2, name: 'Beverage Store', storeId: 1 },
                      { id: 3, name: 'General Store', storeId: 1 }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ];

    return of(mockData);
  }

  // These methods would call the API in a real implementation
  getBusinessUnits(): Observable<BusinessUnit[]> {
    return this.apiService.get<BusinessUnit[]>(`${this.path}/units`);
  }

  getLocationsByBusinessUnit(businessUnitId: number): Observable<BusinessLocation[]> {
    return this.apiService.get<BusinessLocation[]>(`${this.path}/units/${businessUnitId}/locations`);
  }

  getStoresByLocation(locationId: number): Observable<Store[]> {
    return this.apiService.get<Store[]>(`${this.path}/locations/${locationId}/stores`);
  }

  getDepartmentsByStore(storeId: number): Observable<Department[]> {
    return this.apiService.get<Department[]>(`${this.path}/stores/${storeId}/departments`);
  }
}
