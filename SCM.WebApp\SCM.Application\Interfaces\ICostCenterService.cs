using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface ICostCenterService
{
    Task<IEnumerable<CostCenterDto>> GetAllCostCentersAsync();
    Task<CostCenterDto?> GetCostCenterByIdAsync(int id);
    Task<IEnumerable<CostCenterDto>> GetCostCentersByStoreIdAsync(int storeId);
    Task<IEnumerable<CostCenterDto>> GetCostCentersByLocationIdAsync(int locationId);
    Task<IEnumerable<CostCenterDto>> GetCostCentersByTypeIdAsync(int typeId);
    Task<IEnumerable<CostCenterDto>> GetSalesPointsAsync();
    Task<CostCenterDto> CreateCostCenterAsync(CreateCostCenterDto createCostCenterDto);
    Task UpdateCostCenterAsync(UpdateCostCenterDto updateCostCenterDto);
    Task DeleteCostCenterAsync(int id);
}
