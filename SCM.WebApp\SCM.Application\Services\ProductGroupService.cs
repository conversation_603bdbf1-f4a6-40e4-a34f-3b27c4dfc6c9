using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class ProductGroupService : IProductGroupService
{
    private readonly IRepository<ProductGroup> _productGroupRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public ProductGroupService(
        IRepository<ProductGroup> productGroupRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _productGroupRepository = productGroupRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<ProductGroupDto>> GetAllProductGroupsAsync()
    {
        var productGroups = await _dbContext.ProductGroups
            .Include(pg => pg.Department)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<ProductGroupDto>>(productGroups);
    }

    public async Task<ProductGroupDto?> GetProductGroupByIdAsync(int id)
    {
        var productGroup = await _dbContext.ProductGroups
            .Include(pg => pg.Department)
            .FirstOrDefaultAsync(pg => pg.Id == id);
            
        return productGroup != null ? _mapper.Map<ProductGroupDto>(productGroup) : null;
    }

    public async Task<IEnumerable<ProductGroupDto>> GetProductGroupsByDepartmentIdAsync(int departmentId)
    {
        var productGroups = await _dbContext.ProductGroups
            .Include(pg => pg.Department)
            .Where(pg => pg.DepartmentId == departmentId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<ProductGroupDto>>(productGroups);
    }

    public async Task<ProductGroupDto?> GetProductGroupWithSubGroupsAsync(int id)
    {
        var productGroup = await _dbContext.ProductGroups
            .Include(pg => pg.Department)
            .Include(pg => pg.SubGroups)
            .FirstOrDefaultAsync(pg => pg.Id == id);
            
        return productGroup != null ? _mapper.Map<ProductGroupDto>(productGroup) : null;
    }

    public async Task<ProductGroupDto> CreateProductGroupAsync(CreateProductGroupDto createProductGroupDto)
    {
        var productGroup = _mapper.Map<ProductGroup>(createProductGroupDto);
        await _productGroupRepository.AddAsync(productGroup);
        
        // Reload the product group with the department
        var createdProductGroup = await _dbContext.ProductGroups
            .Include(pg => pg.Department)
            .FirstOrDefaultAsync(pg => pg.Id == productGroup.Id);
            
        return _mapper.Map<ProductGroupDto>(createdProductGroup);
    }

    public async Task UpdateProductGroupAsync(UpdateProductGroupDto updateProductGroupDto)
    {
        var productGroup = await _productGroupRepository.GetByIdAsync(updateProductGroupDto.Id);
        if (productGroup == null)
            throw new KeyNotFoundException($"ProductGroup with ID {updateProductGroupDto.Id} not found.");

        _mapper.Map(updateProductGroupDto, productGroup);
        await _productGroupRepository.UpdateAsync(productGroup);
    }

    public async Task DeleteProductGroupAsync(int id)
    {
        var productGroup = await _productGroupRepository.GetByIdAsync(id);
        if (productGroup == null)
            throw new KeyNotFoundException($"ProductGroup with ID {id} not found.");

        // Check if there are any products using this product group
        var hasProducts = await _dbContext.Products.AnyAsync(p => p.GroupId == id);
        if (hasProducts)
            throw new InvalidOperationException($"Cannot delete ProductGroup with ID {id} because it is being used by one or more products.");

        // Check if there are any sub-groups using this product group
        var hasSubGroups = await _dbContext.ProductSubGroups.AnyAsync(sg => sg.GroupId == id);
        if (hasSubGroups)
            throw new InvalidOperationException($"Cannot delete ProductGroup with ID {id} because it has one or more sub-groups.");

        await _productGroupRepository.DeleteAsync(productGroup);
    }
}
