import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiService } from './api.service';
import { AuthService } from './auth.service';

export interface Permission {
  id: number;
  name: string;
  description?: string;
}

export interface RolePermission {
  roleId: number;
  roleName: string;
  permissions: Permission[];
}

export interface UserPermission {
  userId: number;
  username: string;
  permissions: Permission[];
}

export interface FormPermission {
  formName: string;
  actions: {
    name: string;
    allowed: boolean;
  }[];
}

@Injectable({
  providedIn: 'root'
})
export class PermissionsService {
  private readonly path = 'permissions';
  private cachedUserPermissions: { [userId: number]: Permission[] } = {};
  private cachedRolePermissions: { [roleId: number]: Permission[] } = {};
  private cachedFormPermissions: { [formName: string]: FormPermission } = {};

  constructor(
    private apiService: ApiService,
    private authService: AuthService
  ) { }

  // Get all permissions
  getAllPermissions(): Observable<Permission[]> {
    return this.apiService.get<Permission[]>(this.path);
  }

  // Get permissions for a specific role
  getRolePermissions(roleId: number): Observable<Permission[]> {
    if (this.cachedRolePermissions[roleId]) {
      return of(this.cachedRolePermissions[roleId]);
    }
    return this.apiService.get<Permission[]>(`${this.path}/roles/${roleId}`);
  }

  // Get permissions for a specific user
  getUserPermissions(userId: number): Observable<Permission[]> {
    if (this.cachedUserPermissions[userId]) {
      return of(this.cachedUserPermissions[userId]);
    }
    return this.apiService.get<Permission[]>(`${this.path}/users/${userId}`);
  }

  // Get form permissions for the current user
  getFormPermissions(formName: string): Observable<FormPermission> {
    if (this.cachedFormPermissions[formName]) {
      return of(this.cachedFormPermissions[formName]);
    }
    
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      return of({
        formName,
        actions: []
      });
    }
    
    return this.apiService.get<FormPermission>(`${this.path}/forms/${formName}/users/${currentUser.id}`);
  }

  // Check if the current user has a specific permission
  hasPermission(permissionName: string): boolean {
    // Admin users have all permissions
    if (this.authService.hasRole('Admin')) {
      return true;
    }
    
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      return false;
    }
    
    // For now, we'll implement a simple check based on the user's role
    // In a real implementation, this would check against the cached permissions
    if (permissionName.startsWith('product.')) {
      return true; // All users can access product features
    }
    
    if (permissionName.startsWith('inventory.')) {
      return true; // All users can access inventory features
    }
    
    if (permissionName.startsWith('transaction.')) {
      return true; // All users can access transaction features
    }
    
    if (permissionName.startsWith('user.') && !this.authService.hasRole('Admin')) {
      return false; // Only admins can access user management
    }
    
    if (permissionName.startsWith('config.') && !this.authService.hasRole('Admin')) {
      return false; // Only admins can access configuration
    }
    
    return false;
  }

  // Check if the current user can perform an action on a form
  canPerformAction(formName: string, actionName: string): boolean {
    // Admin users can perform all actions
    if (this.authService.hasRole('Admin')) {
      return true;
    }
    
    const formPermission = this.cachedFormPermissions[formName];
    if (!formPermission) {
      return false;
    }
    
    const action = formPermission.actions.find(a => a.name === actionName);
    return action ? action.allowed : false;
  }
}
