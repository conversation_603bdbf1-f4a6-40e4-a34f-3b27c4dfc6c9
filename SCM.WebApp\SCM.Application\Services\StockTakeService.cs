using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class StockTakeService : IStockTakeService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IStockService _stockService;

    public StockTakeService(
        ApplicationDbContext dbContext,
        IMapper mapper,
        IStockService stockService)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _stockService = stockService;
    }

    public async Task<IEnumerable<StockTakeHeaderDto>> GetAllStockTakesAsync()
    {
        var stockTakes = await _dbContext.StockTakeHeaders
            .Include(st => st.CostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .OrderByDescending(st => st.StockTakeDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTakeHeaderDto>>(stockTakes);
    }

    public async Task<StockTakeHeaderDto?> GetStockTakeByIdAsync(int id)
    {
        var stockTake = await _dbContext.StockTakeHeaders
            .Include(st => st.CostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .Include(st => st.Details)
                .ThenInclude(d => d.Product)
            .Include(st => st.Details)
                .ThenInclude(d => d.Batch)
            .Include(st => st.Details)
                .ThenInclude(d => d.Unit)
            .FirstOrDefaultAsync(st => st.Id == id);
            
        return stockTake != null ? _mapper.Map<StockTakeHeaderDto>(stockTake) : null;
    }

    public async Task<IEnumerable<StockTakeHeaderDto>> GetStockTakesByCostCenterIdAsync(int costCenterId)
    {
        var stockTakes = await _dbContext.StockTakeHeaders
            .Include(st => st.CostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .Where(st => st.CostCenterId == costCenterId)
            .OrderByDescending(st => st.StockTakeDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTakeHeaderDto>>(stockTakes);
    }

    public async Task<IEnumerable<StockTakeHeaderDto>> GetStockTakesByStatusAsync(string status)
    {
        var stockTakes = await _dbContext.StockTakeHeaders
            .Include(st => st.CostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .Where(st => st.Status == status)
            .OrderByDescending(st => st.StockTakeDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTakeHeaderDto>>(stockTakes);
    }

    public async Task<StockTakeHeaderDto> CreateStockTakeAsync(CreateStockTakeHeaderDto createStockTakeHeaderDto)
    {
        // Generate reference number
        var referenceNumber = await GenerateReferenceNumberAsync();
        
        var stockTake = _mapper.Map<StockTakeHeader>(createStockTakeHeaderDto);
        stockTake.ReferenceNumber = referenceNumber;
        stockTake.Status = "Draft";
        stockTake.CreatedById = 1; // TODO: Get from current user
        
        _dbContext.StockTakeHeaders.Add(stockTake);
        await _dbContext.SaveChangesAsync();
        
        var createdStockTake = await _dbContext.StockTakeHeaders
            .Include(st => st.CostCenter)
            .Include(st => st.CreatedBy)
            .FirstOrDefaultAsync(st => st.Id == stockTake.Id);
            
        return _mapper.Map<StockTakeHeaderDto>(createdStockTake);
    }

    public async Task UpdateStockTakeAsync(UpdateStockTakeHeaderDto updateStockTakeHeaderDto)
    {
        var stockTake = await _dbContext.StockTakeHeaders.FindAsync(updateStockTakeHeaderDto.Id);
        if (stockTake == null)
            throw new KeyNotFoundException($"StockTake with ID {updateStockTakeHeaderDto.Id} not found.");
            
        if (stockTake.Status != "Draft")
            throw new InvalidOperationException("Only stock takes in Draft status can be updated.");
            
        _mapper.Map(updateStockTakeHeaderDto, stockTake);
        
        _dbContext.StockTakeHeaders.Update(stockTake);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteStockTakeAsync(int id)
    {
        var stockTake = await _dbContext.StockTakeHeaders.FindAsync(id);
        if (stockTake == null)
            throw new KeyNotFoundException($"StockTake with ID {id} not found.");
            
        if (stockTake.Status != "Draft")
            throw new InvalidOperationException("Only stock takes in Draft status can be deleted.");
            
        // Delete all details first
        var details = await _dbContext.StockTakeDetails.Where(d => d.StockTakeHeaderId == id).ToListAsync();
        _dbContext.StockTakeDetails.RemoveRange(details);
        
        _dbContext.StockTakeHeaders.Remove(stockTake);
        await _dbContext.SaveChangesAsync();
    }

    public async Task CompleteStockTakeAsync(CompleteStockTakeDto completeStockTakeDto)
    {
        var stockTake = await _dbContext.StockTakeHeaders
            .Include(st => st.Details)
            .FirstOrDefaultAsync(st => st.Id == completeStockTakeDto.Id);
            
        if (stockTake == null)
            throw new KeyNotFoundException($"StockTake with ID {completeStockTakeDto.Id} not found.");
            
        if (stockTake.Status != "Draft" && stockTake.Status != "In Progress")
            throw new InvalidOperationException("Only stock takes in Draft or In Progress status can be completed.");
            
        if (!stockTake.Details.Any())
            throw new InvalidOperationException("Cannot complete a stock take with no details.");
            
        // Update stock based on counted quantities
        foreach (var detail in stockTake.Details)
        {
            await _stockService.AdjustStockAsync(new StockAdjustmentDto
            {
                ProductId = detail.ProductId,
                CostCenterId = stockTake.CostCenterId,
                BatchId = detail.BatchId ?? 0, // Default batch if none specified
                UnitId = detail.UnitId,
                Quantity = detail.CountedQuantity,
                Reason = "Stock Take Adjustment",
                Notes = $"Stock Take #{stockTake.ReferenceNumber}"
            });
        }
        
        // Update stock take header
        stockTake.Status = "Completed";
        stockTake.CompletedById = 1; // TODO: Get from current user
        stockTake.CompletedAt = DateTime.UtcNow;
        stockTake.Notes = completeStockTakeDto.Notes ?? stockTake.Notes;
        
        _dbContext.StockTakeHeaders.Update(stockTake);
        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelStockTakeAsync(int id)
    {
        var stockTake = await _dbContext.StockTakeHeaders.FindAsync(id);
        if (stockTake == null)
            throw new KeyNotFoundException($"StockTake with ID {id} not found.");
            
        if (stockTake.Status == "Completed")
            throw new InvalidOperationException("Cannot cancel a completed stock take.");
            
        stockTake.Status = "Cancelled";
        
        _dbContext.StockTakeHeaders.Update(stockTake);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<StockTakeDetailDto>> GetStockTakeDetailsAsync(int stockTakeHeaderId)
    {
        var details = await _dbContext.StockTakeDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .Where(d => d.StockTakeHeaderId == stockTakeHeaderId)
            .OrderBy(d => d.Product.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTakeDetailDto>>(details);
    }

    public async Task<StockTakeDetailDto?> GetStockTakeDetailByIdAsync(int id)
    {
        var detail = await _dbContext.StockTakeDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        return detail != null ? _mapper.Map<StockTakeDetailDto>(detail) : null;
    }

    public async Task<StockTakeDetailDto> CreateStockTakeDetailAsync(CreateStockTakeDetailDto createStockTakeDetailDto)
    {
        var stockTake = await _dbContext.StockTakeHeaders.FindAsync(createStockTakeDetailDto.StockTakeHeaderId);
        if (stockTake == null)
            throw new KeyNotFoundException($"StockTake with ID {createStockTakeDetailDto.StockTakeHeaderId} not found.");
            
        if (stockTake.Status != "Draft" && stockTake.Status != "In Progress")
            throw new InvalidOperationException("Cannot add details to a stock take that is not in Draft or In Progress status.");
            
        // Check if detail already exists for this product and batch
        var existingDetail = await _dbContext.StockTakeDetails
            .FirstOrDefaultAsync(d => 
                d.StockTakeHeaderId == createStockTakeDetailDto.StockTakeHeaderId && 
                d.ProductId == createStockTakeDetailDto.ProductId && 
                d.BatchId == createStockTakeDetailDto.BatchId);
                
        if (existingDetail != null)
            throw new InvalidOperationException("A detail for this product and batch already exists in this stock take.");
            
        var detail = _mapper.Map<StockTakeDetail>(createStockTakeDetailDto);
        detail.Variance = detail.CountedQuantity - detail.SystemQuantity;
        
        _dbContext.StockTakeDetails.Add(detail);
        await _dbContext.SaveChangesAsync();
        
        // Update stock take status if it's the first detail
        if (stockTake.Status == "Draft")
        {
            stockTake.Status = "In Progress";
            _dbContext.StockTakeHeaders.Update(stockTake);
            await _dbContext.SaveChangesAsync();
        }
        
        var createdDetail = await _dbContext.StockTakeDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == detail.Id);
            
        return _mapper.Map<StockTakeDetailDto>(createdDetail);
    }

    public async Task UpdateStockTakeDetailAsync(UpdateStockTakeDetailDto updateStockTakeDetailDto)
    {
        var detail = await _dbContext.StockTakeDetails
            .Include(d => d.StockTakeHeader)
            .FirstOrDefaultAsync(d => d.Id == updateStockTakeDetailDto.Id);
            
        if (detail == null)
            throw new KeyNotFoundException($"StockTakeDetail with ID {updateStockTakeDetailDto.Id} not found.");
            
        if (detail.StockTakeHeader.Status != "Draft" && detail.StockTakeHeader.Status != "In Progress")
            throw new InvalidOperationException("Cannot update details of a stock take that is not in Draft or In Progress status.");
            
        _mapper.Map(updateStockTakeDetailDto, detail);
        detail.Variance = detail.CountedQuantity - detail.SystemQuantity;
        
        _dbContext.StockTakeDetails.Update(detail);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteStockTakeDetailAsync(int id)
    {
        var detail = await _dbContext.StockTakeDetails
            .Include(d => d.StockTakeHeader)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        if (detail == null)
            throw new KeyNotFoundException($"StockTakeDetail with ID {id} not found.");
            
        if (detail.StockTakeHeader.Status != "Draft" && detail.StockTakeHeader.Status != "In Progress")
            throw new InvalidOperationException("Cannot delete details of a stock take that is not in Draft or In Progress status.");
            
        _dbContext.StockTakeDetails.Remove(detail);
        await _dbContext.SaveChangesAsync();
        
        // Check if this was the last detail and update header status if needed
        var remainingDetails = await _dbContext.StockTakeDetails
            .AnyAsync(d => d.StockTakeHeaderId == detail.StockTakeHeaderId);
            
        if (!remainingDetails && detail.StockTakeHeader.Status == "In Progress")
        {
            detail.StockTakeHeader.Status = "Draft";
            _dbContext.StockTakeHeaders.Update(detail.StockTakeHeader);
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task GenerateStockTakeDetailsAsync(int stockTakeHeaderId)
    {
        var stockTake = await _dbContext.StockTakeHeaders.FindAsync(stockTakeHeaderId);
        if (stockTake == null)
            throw new KeyNotFoundException($"StockTake with ID {stockTakeHeaderId} not found.");
            
        if (stockTake.Status != "Draft")
            throw new InvalidOperationException("Can only generate details for stock takes in Draft status.");
            
        // Get current stock for the cost center
        var stockItems = await _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.Batch)
            .Include(s => s.Unit)
            .Where(s => s.CostCenterId == stockTake.CostCenterId && s.Quantity > 0)
            .ToListAsync();
            
        // Create stock take details for each stock item
        foreach (var stockItem in stockItems)
        {
            var detail = new StockTakeDetail
            {
                StockTakeHeaderId = stockTakeHeaderId,
                ProductId = stockItem.ProductId,
                BatchId = stockItem.BatchId,
                UnitId = stockItem.UnitId,
                SystemQuantity = stockItem.Quantity,
                CountedQuantity = 0, // Will be filled during counting
                Variance = -stockItem.Quantity // Initial variance is negative (missing all stock)
            };
            
            _dbContext.StockTakeDetails.Add(detail);
        }
        
        await _dbContext.SaveChangesAsync();
        
        // Update stock take status
        stockTake.Status = "In Progress";
        _dbContext.StockTakeHeaders.Update(stockTake);
        await _dbContext.SaveChangesAsync();
    }
    
    private async Task<string> GenerateReferenceNumberAsync()
    {
        var today = DateTime.UtcNow.ToString("yyyyMMdd");
        var prefix = $"ST{today}";
        
        var lastStockTake = await _dbContext.StockTakeHeaders
            .Where(st => st.ReferenceNumber.StartsWith(prefix))
            .OrderByDescending(st => st.ReferenceNumber)
            .FirstOrDefaultAsync();
            
        int sequence = 1;
        
        if (lastStockTake != null)
        {
            var lastSequence = lastStockTake.ReferenceNumber.Substring(prefix.Length);
            if (int.TryParse(lastSequence, out int lastSeq))
            {
                sequence = lastSeq + 1;
            }
        }
        
        return $"{prefix}{sequence:D4}";
    }
}
