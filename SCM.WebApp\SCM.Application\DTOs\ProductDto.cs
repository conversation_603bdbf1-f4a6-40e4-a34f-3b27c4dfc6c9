namespace SCM.Application.DTOs;

public class ProductDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int? BrandId { get; set; }
    public string? BrandName { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public int? UnitGroupId { get; set; }
    public string? UnitGroupName { get; set; }
    public int? DepartmentId { get; set; }
    public string? DepartmentName { get; set; }
    public int? GroupId { get; set; }
    public string? GroupName { get; set; }
    public int? SubGroupId { get; set; }
    public string? SubGroupName { get; set; }
    public decimal? CostPrice { get; set; }
    public decimal? AverageCost { get; set; }
    public decimal? SalesPrice { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public string? Notes { get; set; }
    public bool IsStockItem { get; set; }
    public bool IsRecipe { get; set; }
    public bool HasExpiry { get; set; }
    public bool IsProduction { get; set; }
    public bool IsSaleable { get; set; }
    public int? TaxId { get; set; }
    public string? TaxName { get; set; }
    public decimal? TaxRate { get; set; }
    public int? SalesUnitId { get; set; }
    public string? SalesUnitName { get; set; }
    public decimal? SalesUnitConversionFactor { get; set; }
    public bool AllowDiscount { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<BarcodeDto>? Barcodes { get; set; }
}

public class CreateProductDto
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int? BrandId { get; set; }
    public int? UnitId { get; set; }
    public int? UnitGroupId { get; set; }
    public int? DepartmentId { get; set; }
    public int? GroupId { get; set; }
    public int? SubGroupId { get; set; }
    public decimal? CostPrice { get; set; }
    public decimal? SalesPrice { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public string? Notes { get; set; }
    public bool IsStockItem { get; set; } = true;
    public bool IsRecipe { get; set; } = false;
    public bool HasExpiry { get; set; } = false;
    public bool IsProduction { get; set; } = false;
    public bool IsSaleable { get; set; } = true;
    public int? TaxId { get; set; }
    public int? SalesUnitId { get; set; }
    public decimal? SalesUnitConversionFactor { get; set; }
    public bool AllowDiscount { get; set; } = true;
}

public class UpdateProductDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int? BrandId { get; set; }
    public int? UnitId { get; set; }
    public int? UnitGroupId { get; set; }
    public int? DepartmentId { get; set; }
    public int? GroupId { get; set; }
    public int? SubGroupId { get; set; }
    public decimal? CostPrice { get; set; }
    public decimal? SalesPrice { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public string? Notes { get; set; }
    public bool IsStockItem { get; set; }
    public bool IsRecipe { get; set; }
    public bool HasExpiry { get; set; }
    public bool IsProduction { get; set; }
    public bool IsSaleable { get; set; }
    public int? TaxId { get; set; }
    public int? SalesUnitId { get; set; }
    public decimal? SalesUnitConversionFactor { get; set; }
    public bool AllowDiscount { get; set; }
    public bool IsActive { get; set; }
}
