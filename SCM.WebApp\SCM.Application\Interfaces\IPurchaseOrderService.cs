using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IPurchaseOrderService
{
    Task<IEnumerable<PurchaseOrderListDto>> GetAllPurchaseOrdersAsync();
    Task<PurchaseOrderDto?> GetPurchaseOrderByIdAsync(int id);
    Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersBySupplierAsync(int supplierId);
    Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersByCostCenterAsync(int costCenterId);
    Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersByStatusAsync(string status);
    Task<PurchaseOrderDto> CreatePurchaseOrderAsync(CreatePurchaseOrderDto createPurchaseOrderDto);
    Task UpdatePurchaseOrderAsync(UpdatePurchaseOrderDto updatePurchaseOrderDto);
    Task DeletePurchaseOrderAsync(int id);
    Task ApprovePurchaseOrderAsync(int id);
    Task RejectPurchaseOrderAsync(int id, string reason);
    Task CancelPurchaseOrderAsync(int id, string reason);
    Task<PurchaseOrderDetailDto> AddPurchaseOrderDetailAsync(CreatePurchaseOrderDetailDto createDetailDto);
    Task UpdatePurchaseOrderDetailAsync(UpdatePurchaseOrderDetailDto updateDetailDto);
    Task DeletePurchaseOrderDetailAsync(int id);
}
