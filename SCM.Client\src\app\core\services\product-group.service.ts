import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { ProductGroup, CreateProductGroup, UpdateProductGroup } from '../models/product-group.model';

@Injectable({
  providedIn: 'root'
})
export class ProductGroupService {
  private readonly path = 'productgroups';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<ProductGroup[]> {
    return this.apiService.get<ProductGroup[]>(this.path);
  }

  getById(id: number): Observable<ProductGroup> {
    return this.apiService.get<ProductGroup>(`${this.path}/${id}`);
  }

  getByDepartmentId(departmentId: number): Observable<ProductGroup[]> {
    return this.apiService.get<ProductGroup[]>(`${this.path}/department/${departmentId}`);
  }

  create(productGroup: CreateProductGroup): Observable<ProductGroup> {
    return this.apiService.post<ProductGroup, CreateProductGroup>(this.path, productGroup);
  }

  update(id: number, productGroup: UpdateProductGroup): Observable<void> {
    return this.apiService.put<void, UpdateProductGroup>(`${this.path}/${id}`, productGroup);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
