<div class="app-container">
  <mat-sidenav-container>
    <mat-sidenav #sidenav mode="side" opened class="app-sidenav">
      <div class="sidenav-header">
        <img src="assets/logo.svg" alt="SCM Logo" class="logo">
      </div>

      <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
        <!-- Leaf nodes (no children) -->
        <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
          <button mat-icon-button disabled></button>
          <mat-icon class="node-icon">{{node.icon}}</mat-icon>
          <a *ngIf="node.path" class="node-label clickable" [routerLink]="node.path" (click)="$event.preventDefault(); navigateTo(node.path)">{{node.name}}</a>
          <span *ngIf="!node.path" class="node-label">{{node.name}}</span>
        </mat-tree-node>

        <!-- Parent nodes (with children) -->
        <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
          <button mat-icon-button [attr.aria-label]="'Toggle ' + node.name" matTreeNodeToggle>
            <mat-icon>
              {{treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right'}}
            </mat-icon>
          </button>
          <mat-icon class="node-icon">{{node.icon}}</mat-icon>
          <a *ngIf="node.path" class="node-label clickable" [routerLink]="node.path" (click)="$event.preventDefault(); navigateTo(node.path)">{{node.name}}</a>
          <span *ngIf="!node.path" class="node-label">{{node.name}}</span>
        </mat-tree-node>
      </mat-tree>
    </mat-sidenav>

    <mat-sidenav-content>
      <mat-toolbar color="primary" class="app-toolbar">
        <button mat-icon-button (click)="sidenav.toggle()">
          <mat-icon>menu</mat-icon>
        </button>

        <span class="toolbar-spacer"></span>

        <nav mat-tab-nav-bar [tabPanel]="tabPanel">
          <a mat-tab-link
             *ngFor="let link of navLinks"
             [routerLink]="link.path"
             routerLinkActive #rla="routerLinkActive"
             [active]="rla.isActive">
            {{link.label}}
          </a>
        </nav>
        <mat-tab-nav-panel #tabPanel></mat-tab-nav-panel>

        <span class="toolbar-spacer"></span>

        <button mat-icon-button [matMenuTriggerFor]="userMenu">
          <mat-icon>account_circle</mat-icon>
        </button>
        <mat-menu #userMenu="matMenu">
          <div class="user-menu-header">
            <mat-icon>account_circle</mat-icon>
            <span>{{ userDisplayName }}</span>
          </div>
          <mat-divider></mat-divider>
          <button mat-menu-item>
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>
          <button mat-menu-item>
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()">
            <mat-icon>exit_to_app</mat-icon>
            <span>Logout</span>
          </button>
        </mat-menu>
      </mat-toolbar>

      <div class="content-container">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
