-- Role Permissions Migration Script
-- This script adds the Permissions column to the Role table

USE [InventoryManagement]
GO

-- Check if Permissions column exists in Role table
IF NOT EXISTS (
    SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID(N'[dbo].[Role]') AND name = 'Permissions'
)
BEGIN
    -- Add Permissions column to Role table
    ALTER TABLE [dbo].[Role]
    ADD [Permissions] [nvarchar](max) NULL;
    
    PRINT 'Permissions column added to Role table successfully.'
END
ELSE
BEGIN
    PRINT 'Permissions column already exists in Role table.'
END
GO

-- Check if there are any roles in the database
IF NOT EXISTS (SELECT TOP 1 * FROM [dbo].[Role])
BEGIN
    -- Insert default roles
    INSERT INTO [dbo].[Role] ([Name], [Description], [Permissions], [CreatedAt], [IsActive])
    VALUES
        ('Admin', 'Administrator with full access', '{"all": true}', GETDATE(), 1),
        ('Manager', 'Manager with limited administrative access', '{"users": {"view": true, "create": true, "edit": true}, "products": {"view": true, "create": true, "edit": true}, "transactions": {"view": true, "create": true, "edit": true, "approve": true}}', GETDATE(), 1),
        ('User', 'Regular user with basic access', '{"products": {"view": true}, "transactions": {"view": true, "create": true}}', GETDATE(), 1);
    
    PRINT 'Default roles created successfully.'
END
ELSE
BEGIN
    PRINT 'Roles already exist in the database.'
END
GO

PRINT 'Role permissions migration completed successfully.'
GO
