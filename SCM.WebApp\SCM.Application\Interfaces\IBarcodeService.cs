using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IBarcodeService
{
    Task<IEnumerable<BarcodeDto>> GetAllBarcodesAsync();
    Task<BarcodeDto?> GetBarcodeByIdAsync(int id);
    Task<BarcodeDto?> GetBarcodeByValueAsync(string barcodeValue);
    Task<IEnumerable<BarcodeDto>> GetBarcodesByProductIdAsync(int productId);
    Task<BarcodeDto?> GetPrimaryBarcodeByProductIdAsync(int productId);
    Task<BarcodeDto> CreateBarcodeAsync(CreateBarcodeDto createBarcodeDto);
    Task UpdateBarcodeAsync(UpdateBarcodeDto updateBarcodeDto);
    Task DeleteBarcodeAsync(int id);
}
