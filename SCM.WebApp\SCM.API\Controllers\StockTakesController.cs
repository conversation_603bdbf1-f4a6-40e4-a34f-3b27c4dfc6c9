using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class StockTakesController : ApiControllerBase
{
    private readonly IStockTakeService _stockTakeService;

    public StockTakesController(IStockTakeService stockTakeService)
    {
        _stockTakeService = stockTakeService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<StockTakeHeaderDto>>> GetAll()
    {
        var stockTakes = await _stockTakeService.GetAllStockTakesAsync();
        return Ok(stockTakes);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<StockTakeHeaderDto>> GetById(int id)
    {
        var stockTake = await _stockTakeService.GetStockTakeByIdAsync(id);
        if (stockTake == null)
            return NotFound();

        return Ok(stockTake);
    }

    [HttpGet("costcenter/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<StockTakeHeaderDto>>> GetByCostCenterId(int costCenterId)
    {
        var stockTakes = await _stockTakeService.GetStockTakesByCostCenterIdAsync(costCenterId);
        return Ok(stockTakes);
    }

    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<StockTakeHeaderDto>>> GetByStatus(string status)
    {
        var stockTakes = await _stockTakeService.GetStockTakesByStatusAsync(status);
        return Ok(stockTakes);
    }

    [HttpPost]
    public async Task<ActionResult<StockTakeHeaderDto>> Create(CreateStockTakeHeaderDto createStockTakeHeaderDto)
    {
        var stockTake = await _stockTakeService.CreateStockTakeAsync(createStockTakeHeaderDto);
        return CreatedAtAction(nameof(GetById), new { id = stockTake.Id }, stockTake);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateStockTakeHeaderDto updateStockTakeHeaderDto)
    {
        if (id != updateStockTakeHeaderDto.Id)
            return BadRequest();

        try
        {
            await _stockTakeService.UpdateStockTakeAsync(updateStockTakeHeaderDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _stockTakeService.DeleteStockTakeAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/complete")]
    public async Task<IActionResult> Complete(int id, CompleteStockTakeDto completeStockTakeDto)
    {
        if (id != completeStockTakeDto.Id)
            return BadRequest();

        try
        {
            await _stockTakeService.CompleteStockTakeAsync(completeStockTakeDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/cancel")]
    public async Task<IActionResult> Cancel(int id)
    {
        try
        {
            await _stockTakeService.CancelStockTakeAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpGet("{stockTakeHeaderId}/details")]
    public async Task<ActionResult<IEnumerable<StockTakeDetailDto>>> GetDetails(int stockTakeHeaderId)
    {
        var details = await _stockTakeService.GetStockTakeDetailsAsync(stockTakeHeaderId);
        return Ok(details);
    }

    [HttpGet("details/{id}")]
    public async Task<ActionResult<StockTakeDetailDto>> GetDetailById(int id)
    {
        var detail = await _stockTakeService.GetStockTakeDetailByIdAsync(id);
        if (detail == null)
            return NotFound();

        return Ok(detail);
    }

    [HttpPost("details")]
    public async Task<ActionResult<StockTakeDetailDto>> CreateDetail(CreateStockTakeDetailDto createStockTakeDetailDto)
    {
        try
        {
            var detail = await _stockTakeService.CreateStockTakeDetailAsync(createStockTakeDetailDto);
            return CreatedAtAction(nameof(GetDetailById), new { id = detail.Id }, detail);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("details/{id}")]
    public async Task<IActionResult> UpdateDetail(int id, UpdateStockTakeDetailDto updateStockTakeDetailDto)
    {
        if (id != updateStockTakeDetailDto.Id)
            return BadRequest();

        try
        {
            await _stockTakeService.UpdateStockTakeDetailAsync(updateStockTakeDetailDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("details/{id}")]
    public async Task<IActionResult> DeleteDetail(int id)
    {
        try
        {
            await _stockTakeService.DeleteStockTakeDetailAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{stockTakeHeaderId}/generate-details")]
    public async Task<IActionResult> GenerateDetails(int stockTakeHeaderId)
    {
        try
        {
            await _stockTakeService.GenerateStockTakeDetailsAsync(stockTakeHeaderId);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
