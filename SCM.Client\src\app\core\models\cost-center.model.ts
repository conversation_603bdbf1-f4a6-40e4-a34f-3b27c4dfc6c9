export interface CostCenter {
  id: number;
  code: string;
  name: string;
  typeId: number;
  typeName?: string;
  storeId: number;
  storeName?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateCostCenter {
  code: string;
  name: string;
  typeId: number;
  storeId: number;
}

export interface UpdateCostCenter {
  id: number;
  code: string;
  name: string;
  typeId: number;
  storeId: number;
  isActive: boolean;
}

export interface CostCenterType {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateCostCenterType {
  name: string;
  description?: string;
}

export interface UpdateCostCenterType {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
}

export interface Store {
  id: number;
  name: string;
  code: string;
  address?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateStore {
  name: string;
  code: string;
  address?: string;
  phone?: string;
  email?: string;
}

export interface UpdateStore {
  id: number;
  name: string;
  code: string;
  address?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
}
