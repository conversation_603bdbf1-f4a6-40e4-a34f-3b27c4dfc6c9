import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

export interface Unit {
  id: number;
  name: string;
  abbreviation: string;
  unitGroupId?: number;
  unitGroupName?: string;
  isActive: boolean;
}

export interface CreateUnit {
  name: string;
  abbreviation: string;
  unitGroupId?: number;
}

export interface UpdateUnit {
  id: number;
  name: string;
  abbreviation: string;
  unitGroupId?: number;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class UnitService {
  private readonly path = 'units';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<Unit[]> {
    return this.apiService.get<Unit[]>(this.path);
  }

  getById(id: number): Observable<Unit> {
    return this.apiService.get<Unit>(`${this.path}/${id}`);
  }

  getByUnitGroupId(unitGroupId: number): Observable<Unit[]> {
    return this.apiService.get<Unit[]>(`${this.path}/by-unit-group/${unitGroupId}`);
  }

  create(unit: CreateUnit): Observable<Unit> {
    return this.apiService.post<Unit, CreateUnit>(this.path, unit);
  }

  update(id: number, unit: UpdateUnit): Observable<void> {
    return this.apiService.put<void, UpdateUnit>(`${this.path}/${id}`, unit);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
