using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IProductCostCenterLinkService
{
    Task<IEnumerable<ProductCostCenterLinkDto>> GetAllProductCostCenterLinksAsync();
    Task<ProductCostCenterLinkDto?> GetProductCostCenterLinkByIdAsync(int id);
    Task<ProductCostCenterLinkDto?> GetProductCostCenterLinkAsync(int productId, int costCenterId);
    Task<IEnumerable<ProductCostCenterLinkDto>> GetProductCostCenterLinksByProductIdAsync(int productId);
    Task<IEnumerable<ProductCostCenterLinkDto>> GetProductCostCenterLinksByCostCenterIdAsync(int costCenterId);
    Task<ProductCostCenterLinkDto> CreateProductCostCenterLinkAsync(CreateProductCostCenterLinkDto createProductCostCenterLinkDto);
    Task UpdateProductCostCenterLinkAsync(UpdateProductCostCenterLinkDto updateProductCostCenterLinkDto);
    Task DeleteProductCostCenterLinkAsync(int id);
}
