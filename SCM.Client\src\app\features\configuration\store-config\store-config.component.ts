import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';

@Component({
  selector: 'app-store-config',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTabsModule
  ],
  templateUrl: './store-config.component.html',
  styleUrls: ['./store-config.component.scss']
})
export class StoreConfigComponent implements OnInit {
  storeForm!: FormGroup;
  
  regions: string[] = ['Egypt', 'Other Regions'];
  storeTypes: string[] = ['Hotel', 'Restaurant', 'Retail'];
  taxRates: string[] = ['None', '5%', '10%', '15%', '20%'];
  sellingPriceTargets: string[] = ['Cost %', 'Retail %', 'Fixed'];
  
  constructor(private fb: FormBuilder) {}
  
  ngOnInit(): void {
    this.initForm();
  }
  
  initForm(): void {
    this.storeForm = this.fb.group({
      branchNo: ['1', Validators.required],
      storeId: ['1', Validators.required],
      branchName: ['Casa Mare Resort', Validators.required],
      region: ['Egypt'],
      storeType: ['Hotel'],
      inHouseBarcodePrefix: ['99'],
      documentPrefix: ['RTLP'],
      taxRate: ['None'],
      sellingPriceTarget: ['Cost %'],
      address: ['K.450km Marsa Alam\nEgypt\nAlam\n55555'],
      branchExportPath: [''],
      scaleExportPath: ['']
    });
  }
  
  saveStore(): void {
    if (this.storeForm.valid) {
      console.log('Store configuration saved:', this.storeForm.value);
    } else {
      console.log('Form is invalid');
    }
  }
  
  deleteStore(): void {
    console.log('Delete store clicked');
  }
  
  uploadImage(): void {
    console.log('Upload image clicked');
  }
}
