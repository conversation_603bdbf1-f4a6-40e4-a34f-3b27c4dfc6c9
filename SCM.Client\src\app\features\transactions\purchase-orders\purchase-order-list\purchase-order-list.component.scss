.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-field {
  min-width: 300px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.table-container {
  overflow-x: auto;
  border-radius: 4px;
}

.no-data-message {
  padding: 20px;
  text-align: center;
  color: #757575;
}

table {
  width: 100%;
}

.clickable-row {
  cursor: pointer;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.status-chip {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-draft {
  background-color: #e0e0e0;
  color: #616161;
}

.status-pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.status-approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-rejected {
  background-color: #ffebee;
  color: #c62828;
}

.status-completed {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-cancelled {
  background-color: #f5f5f5;
  color: #757575;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-field {
    min-width: auto;
  }
  
  .mat-column-expectedDeliveryDate,
  .mat-column-costCenterName {
    display: none;
  }
}
