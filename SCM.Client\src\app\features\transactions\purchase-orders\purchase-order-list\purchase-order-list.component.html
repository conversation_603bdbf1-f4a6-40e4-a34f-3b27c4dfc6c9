<div class="page-container">
  <div class="page-header">
    <h1>Purchase Orders</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="createPurchaseOrder()">
        <mat-icon>add</mat-icon>
        New Purchase Order
      </button>
    </div>
  </div>
  
  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by PO #, supplier, or cost center">
          <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; applyFilter()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Supplier</mat-label>
          <mat-select [(ngModel)]="selectedSupplier" (selectionChange)="applyFilter()">
            <mat-option value="">All Suppliers</mat-option>
            <mat-option *ngFor="let supplier of suppliers" [value]="supplier.name">
              {{supplier.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Cost Center</mat-label>
          <mat-select [(ngModel)]="selectedCostCenter" (selectionChange)="applyFilter()">
            <mat-option value="">All Cost Centers</mat-option>
            <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.name">
              {{costCenter.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilter()">
            <mat-option *ngFor="let option of statusOptions" [value]="option.value">
              {{option.label}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <button mat-button color="primary" (click)="clearFilters()" [disabled]="!searchTerm && !selectedSupplier && !selectedCostCenter && !selectedStatus">
          Clear Filters
        </button>
      </div>
    </mat-card-content>
  </mat-card>
  
  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>
  
  <div *ngIf="!isLoading" class="table-container mat-elevation-z2">
    <div *ngIf="filteredPurchaseOrders.length === 0" class="no-data-message">
      No purchase orders found.
    </div>
    
    <table mat-table [dataSource]="filteredPurchaseOrders" *ngIf="filteredPurchaseOrders.length > 0">
      <!-- Document Number Column -->
      <ng-container matColumnDef="documentNumber">
        <th mat-header-cell *matHeaderCellDef>PO #</th>
        <td mat-cell *matCellDef="let order">{{order.documentNumber}}</td>
      </ng-container>
      
      <!-- Supplier Column -->
      <ng-container matColumnDef="supplierName">
        <th mat-header-cell *matHeaderCellDef>Supplier</th>
        <td mat-cell *matCellDef="let order">{{order.supplierName}}</td>
      </ng-container>
      
      <!-- Cost Center Column -->
      <ng-container matColumnDef="costCenterName">
        <th mat-header-cell *matHeaderCellDef>Cost Center</th>
        <td mat-cell *matCellDef="let order">{{order.costCenterName}}</td>
      </ng-container>
      
      <!-- Order Date Column -->
      <ng-container matColumnDef="orderDate">
        <th mat-header-cell *matHeaderCellDef>Order Date</th>
        <td mat-cell *matCellDef="let order">{{order.orderDate | date:'shortDate'}}</td>
      </ng-container>
      
      <!-- Expected Delivery Date Column -->
      <ng-container matColumnDef="expectedDeliveryDate">
        <th mat-header-cell *matHeaderCellDef>Expected Delivery</th>
        <td mat-cell *matCellDef="let order">{{order.expectedDeliveryDate | date:'shortDate' || '-'}}</td>
      </ng-container>
      
      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let order">
          <span class="status-chip" [ngClass]="getStatusClass(order.status)">
            {{order.status}}
          </span>
        </td>
      </ng-container>
      
      <!-- Total Amount Column -->
      <ng-container matColumnDef="totalAmount">
        <th mat-header-cell *matHeaderCellDef>Total Amount</th>
        <td mat-cell *matCellDef="let order">{{order.totalAmount | currency}}</td>
      </ng-container>
      
      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let order">
          <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="viewPurchaseOrder(order)">
              <mat-icon>visibility</mat-icon>
              <span>View</span>
            </button>
            <button mat-menu-item (click)="editPurchaseOrder(order)" *ngIf="order.status === 'Draft'">
              <mat-icon>edit</mat-icon>
              <span>Edit</span>
            </button>
            <button mat-menu-item (click)="deletePurchaseOrder(order)" *ngIf="order.status === 'Draft'">
              <mat-icon>delete</mat-icon>
              <span>Delete</span>
            </button>
          </mat-menu>
        </td>
      </ng-container>
      
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;" (click)="viewPurchaseOrder(row)" class="clickable-row"></tr>
    </table>
  </div>
</div>
