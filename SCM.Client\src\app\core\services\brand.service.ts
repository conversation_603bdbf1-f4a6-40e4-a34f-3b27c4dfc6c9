import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

export interface Brand {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
}

export interface CreateBrand {
  name: string;
  description?: string;
}

export interface UpdateBrand {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class BrandService {
  private readonly path = 'brands';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<Brand[]> {
    return this.apiService.get<Brand[]>(this.path);
  }

  getById(id: number): Observable<Brand> {
    return this.apiService.get<Brand>(`${this.path}/${id}`);
  }

  create(brand: CreateBrand): Observable<Brand> {
    return this.apiService.post<Brand, CreateBrand>(this.path, brand);
  }

  update(id: number, brand: UpdateBrand): Observable<void> {
    return this.apiService.put<void, UpdateBrand>(`${this.path}/${id}`, brand);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
