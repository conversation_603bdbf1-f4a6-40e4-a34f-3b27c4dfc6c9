using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class BarcodeService : IBarcodeService
{
    private readonly IRepository<Barcode> _barcodeRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public BarcodeService(
        IRepository<Barcode> barcodeRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _barcodeRepository = barcodeRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<BarcodeDto>> GetAllBarcodesAsync()
    {
        var barcodes = await _dbContext.Barcodes
            .Include(b => b.Product)
            .Include(b => b.Unit)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<BarcodeDto>>(barcodes);
    }

    public async Task<BarcodeDto?> GetBarcodeByIdAsync(int id)
    {
        var barcode = await _dbContext.Barcodes
            .Include(b => b.Product)
            .Include(b => b.Unit)
            .FirstOrDefaultAsync(b => b.Id == id);
            
        return barcode != null ? _mapper.Map<BarcodeDto>(barcode) : null;
    }

    public async Task<BarcodeDto?> GetBarcodeByValueAsync(string barcodeValue)
    {
        var barcode = await _dbContext.Barcodes
            .Include(b => b.Product)
            .Include(b => b.Unit)
            .FirstOrDefaultAsync(b => b.BarcodeValue == barcodeValue);
            
        return barcode != null ? _mapper.Map<BarcodeDto>(barcode) : null;
    }

    public async Task<IEnumerable<BarcodeDto>> GetBarcodesByProductIdAsync(int productId)
    {
        var barcodes = await _dbContext.Barcodes
            .Include(b => b.Product)
            .Include(b => b.Unit)
            .Where(b => b.ProductId == productId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<BarcodeDto>>(barcodes);
    }

    public async Task<BarcodeDto?> GetPrimaryBarcodeByProductIdAsync(int productId)
    {
        var barcode = await _dbContext.Barcodes
            .Include(b => b.Product)
            .Include(b => b.Unit)
            .FirstOrDefaultAsync(b => b.ProductId == productId && b.IsPrimary);
            
        return barcode != null ? _mapper.Map<BarcodeDto>(barcode) : null;
    }

    public async Task<BarcodeDto> CreateBarcodeAsync(CreateBarcodeDto createBarcodeDto)
    {
        // Check if the barcode value already exists
        var existingBarcode = await _dbContext.Barcodes.FirstOrDefaultAsync(b => b.BarcodeValue == createBarcodeDto.BarcodeValue);
        if (existingBarcode != null)
            throw new InvalidOperationException($"Barcode with value {createBarcodeDto.BarcodeValue} already exists.");

        var barcode = _mapper.Map<Barcode>(createBarcodeDto);
        
        // If this barcode is set as primary, unset any existing primary barcode for this product
        if (barcode.IsPrimary)
        {
            var existingPrimaryBarcodes = await _dbContext.Barcodes
                .Where(b => b.ProductId == barcode.ProductId && b.IsPrimary)
                .ToListAsync();
                
            foreach (var existingPrimaryBarcode in existingPrimaryBarcodes)
            {
                existingPrimaryBarcode.IsPrimary = false;
                _dbContext.Barcodes.Update(existingPrimaryBarcode);
            }
        }
        
        await _barcodeRepository.AddAsync(barcode);
        
        // Reload the barcode with the product and unit
        var createdBarcode = await _dbContext.Barcodes
            .Include(b => b.Product)
            .Include(b => b.Unit)
            .FirstOrDefaultAsync(b => b.Id == barcode.Id);
            
        return _mapper.Map<BarcodeDto>(createdBarcode);
    }

    public async Task UpdateBarcodeAsync(UpdateBarcodeDto updateBarcodeDto)
    {
        var barcode = await _barcodeRepository.GetByIdAsync(updateBarcodeDto.Id);
        if (barcode == null)
            throw new KeyNotFoundException($"Barcode with ID {updateBarcodeDto.Id} not found.");

        // Check if the barcode value already exists (if it's being changed)
        if (barcode.BarcodeValue != updateBarcodeDto.BarcodeValue)
        {
            var existingBarcode = await _dbContext.Barcodes.FirstOrDefaultAsync(b => 
                b.BarcodeValue == updateBarcodeDto.BarcodeValue && 
                b.Id != updateBarcodeDto.Id);
                
            if (existingBarcode != null)
                throw new InvalidOperationException($"Barcode with value {updateBarcodeDto.BarcodeValue} already exists.");
        }

        // If this barcode is being set as primary, unset any existing primary barcode for this product
        if (updateBarcodeDto.IsPrimary && !barcode.IsPrimary)
        {
            var existingPrimaryBarcodes = await _dbContext.Barcodes
                .Where(b => b.ProductId == updateBarcodeDto.ProductId && b.IsPrimary && b.Id != updateBarcodeDto.Id)
                .ToListAsync();
                
            foreach (var existingPrimaryBarcode in existingPrimaryBarcodes)
            {
                existingPrimaryBarcode.IsPrimary = false;
                _dbContext.Barcodes.Update(existingPrimaryBarcode);
            }
        }

        _mapper.Map(updateBarcodeDto, barcode);
        await _barcodeRepository.UpdateAsync(barcode);
    }

    public async Task DeleteBarcodeAsync(int id)
    {
        var barcode = await _barcodeRepository.GetByIdAsync(id);
        if (barcode == null)
            throw new KeyNotFoundException($"Barcode with ID {id} not found.");

        await _barcodeRepository.DeleteAsync(barcode);
    }
}
