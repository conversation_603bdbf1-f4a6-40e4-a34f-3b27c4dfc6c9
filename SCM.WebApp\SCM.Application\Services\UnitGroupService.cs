using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class UnitGroupService : IUnitGroupService
{
    private readonly IRepository<UnitGroup> _unitGroupRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public UnitGroupService(
        IRepository<UnitGroup> unitGroupRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _unitGroupRepository = unitGroupRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<UnitGroupDto>> GetAllUnitGroupsAsync()
    {
        var unitGroups = await _unitGroupRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<UnitGroupDto>>(unitGroups);
    }

    public async Task<UnitGroupDto?> GetUnitGroupByIdAsync(int id)
    {
        var unitGroup = await _unitGroupRepository.GetByIdAsync(id);
        return unitGroup != null ? _mapper.Map<UnitGroupDto>(unitGroup) : null;
    }

    public async Task<UnitGroupDto?> GetUnitGroupWithUnitsAsync(int id)
    {
        var unitGroup = await _dbContext.UnitGroups
            .Include(ug => ug.Units)
            .FirstOrDefaultAsync(ug => ug.Id == id);

        return unitGroup != null ? _mapper.Map<UnitGroupDto>(unitGroup) : null;
    }

    public async Task<UnitGroupDto> CreateUnitGroupAsync(CreateUnitGroupDto createUnitGroupDto)
    {
        var unitGroup = _mapper.Map<UnitGroup>(createUnitGroupDto);
        await _unitGroupRepository.AddAsync(unitGroup);
        return _mapper.Map<UnitGroupDto>(unitGroup);
    }

    public async Task UpdateUnitGroupAsync(UpdateUnitGroupDto updateUnitGroupDto)
    {
        var unitGroup = await _unitGroupRepository.GetByIdAsync(updateUnitGroupDto.Id);
        if (unitGroup == null)
            throw new KeyNotFoundException($"UnitGroup with ID {updateUnitGroupDto.Id} not found.");

        _mapper.Map(updateUnitGroupDto, unitGroup);
        await _unitGroupRepository.UpdateAsync(unitGroup);
    }

    public async Task DeleteUnitGroupAsync(int id)
    {
        var unitGroup = await _unitGroupRepository.GetByIdAsync(id);
        if (unitGroup == null)
            throw new KeyNotFoundException($"UnitGroup with ID {id} not found.");

        // Check if there are any units using this unit group
        var hasUnits = await _dbContext.Units.AnyAsync(u => u.UnitGroupId == id);
        if (hasUnits)
            throw new InvalidOperationException($"Cannot delete UnitGroup with ID {id} because it is being used by one or more units.");

        await _unitGroupRepository.DeleteAsync(unitGroup);
    }
}
