using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionHeader : BaseEntity
{
    public string ReferenceNumber { get; set; } = string.Empty;
    public int TransactionTypeId { get; set; }
    public int TransactionProcessId { get; set; }
    public int CostCenterId { get; set; }
    public int? ToCostCenterId { get; set; }
    public int? SupplierId { get; set; }
    public int? CustomerId { get; set; }
    public DateTime TransactionDate { get; set; } = DateTime.UtcNow;
    public DateTime? RequiredDate { get; set; }
    public string Status { get; set; } = "Draft"; // Draft, Pending, Approved, Completed, Cancelled
    public string? Notes { get; set; }
    public decimal? TotalAmount { get; set; }
    public decimal? TaxAmount { get; set; }
    public decimal? DiscountAmount { get; set; }
    public decimal? NetAmount { get; set; }
    public int? CreatedById { get; set; }
    public int? ApprovedById { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public int? CompletedById { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ExternalReference { get; set; }
    public int? CurrencyId { get; set; }
    public decimal? ExchangeRate { get; set; } = 1;
    
    // Navigation properties
    public virtual TransactionType TransactionType { get; set; } = null!;
    public virtual TransactionProcess TransactionProcess { get; set; } = null!;
    public virtual CostCenter CostCenter { get; set; } = null!;
    public virtual CostCenter? ToCostCenter { get; set; }
    public virtual Supplier? Supplier { get; set; }
    public virtual Customer? Customer { get; set; }
    public virtual User? CreatedBy { get; set; }
    public virtual User? ApprovedBy { get; set; }
    public virtual User? CompletedBy { get; set; }
    public virtual Currency? Currency { get; set; }
    public virtual ICollection<TransactionDetail> Details { get; set; } = new List<TransactionDetail>();
    public virtual ICollection<TransactionStage> Stages { get; set; } = new List<TransactionStage>();
    public virtual ICollection<PaymentDetail> PaymentDetails { get; set; } = new List<PaymentDetail>();
}
