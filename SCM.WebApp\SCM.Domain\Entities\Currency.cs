using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Currency : BaseEntity
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Symbol { get; set; } = string.Empty;
    public bool IsDefault { get; set; } = false;
    public decimal ExchangeRate { get; set; } = 1;
    
    // Navigation properties
    public virtual ICollection<TransactionHeader> Transactions { get; set; } = new List<TransactionHeader>();
}
