import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

interface Role {
  id: number;
  name: string;
  description: string;
  userCount: number;
}

@Component({
  selector: 'app-role-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatTableModule,
    MatSnackBarModule,
    MatCheckboxModule,
    MatTabsModule,
    MatDialogModule
  ],
  templateUrl: './role-management.component.html',
  styleUrls: ['./role-management.component.scss']
})
export class RoleManagementComponent implements OnInit {
  roleForm!: FormGroup;
  displayedColumns: string[] = ['id', 'name', 'description', 'userCount', 'actions'];
  roles: Role[] = [];
  selectedRole: Role | null = null;
  isEditMode: boolean = false;
  
  permissions = [
    { id: 1, name: 'View Products', category: 'Products', selected: false },
    { id: 2, name: 'Create Products', category: 'Products', selected: false },
    { id: 3, name: 'Edit Products', category: 'Products', selected: false },
    { id: 4, name: 'Delete Products', category: 'Products', selected: false },
    { id: 5, name: 'View Inventory', category: 'Inventory', selected: false },
    { id: 6, name: 'Adjust Inventory', category: 'Inventory', selected: false },
    { id: 7, name: 'Transfer Inventory', category: 'Inventory', selected: false },
    { id: 8, name: 'View Purchases', category: 'Purchases', selected: false },
    { id: 9, name: 'Create Purchase Orders', category: 'Purchases', selected: false },
    { id: 10, name: 'Approve Purchase Orders', category: 'Purchases', selected: false },
    { id: 11, name: 'Receive Goods', category: 'Purchases', selected: false },
    { id: 12, name: 'View Sales', category: 'Sales', selected: false },
    { id: 13, name: 'Create Sales', category: 'Sales', selected: false },
    { id: 14, name: 'View Reports', category: 'Reports', selected: false },
    { id: 15, name: 'Export Reports', category: 'Reports', selected: false },
    { id: 16, name: 'View Users', category: 'Users', selected: false },
    { id: 17, name: 'Create Users', category: 'Users', selected: false },
    { id: 18, name: 'Edit Users', category: 'Users', selected: false },
    { id: 19, name: 'Delete Users', category: 'Users', selected: false },
    { id: 20, name: 'View Recipes', category: 'Recipes', selected: false },
    { id: 21, name: 'Create Recipes', category: 'Recipes', selected: false },
    { id: 22, name: 'Edit Recipes', category: 'Recipes', selected: false },
    { id: 23, name: 'Delete Recipes', category: 'Recipes', selected: false }
  ];
  
  permissionCategories: string[] = [...new Set(this.permissions.map(p => p.category))];

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    // Mock data for demonstration
    this.roles = [
      {
        id: 1,
        name: 'Administrator',
        description: 'Full system access',
        userCount: 1
      },
      {
        id: 2,
        name: 'Manager',
        description: 'Management access to all modules',
        userCount: 2
      },
      {
        id: 3,
        name: 'Supervisor',
        description: 'Supervise inventory and purchasing',
        userCount: 3
      },
      {
        id: 4,
        name: 'Inventory Clerk',
        description: 'Manage inventory',
        userCount: 5
      },
      {
        id: 5,
        name: 'Purchasing Officer',
        description: 'Create and manage purchase orders',
        userCount: 2
      },
      {
        id: 6,
        name: 'Sales Representative',
        description: 'Create and manage sales',
        userCount: 4
      },
      {
        id: 7,
        name: 'Kitchen Staff',
        description: 'Access to recipes and inventory',
        userCount: 8
      },
      {
        id: 8,
        name: 'Viewer',
        description: 'View-only access',
        userCount: 3
      }
    ];
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.roleForm = this.fb.group({
      name: ['', Validators.required],
      description: ['']
    });
  }

  togglePermission(permission: any): void {
    permission.selected = !permission.selected;
  }

  getPermissionsByCategory(category: string): any[] {
    return this.permissions.filter(p => p.category === category);
  }

  saveRole(): void {
    if (this.roleForm.valid) {
      // Get selected permissions
      const selectedPermissions = this.permissions
        .filter(p => p.selected)
        .map(p => p.id);
      
      if (this.isEditMode && this.selectedRole) {
        // Update existing role
        const index = this.roles.findIndex(r => r.id === this.selectedRole!.id);
        if (index !== -1) {
          this.roles[index] = {
            ...this.selectedRole,
            name: this.roleForm.value.name,
            description: this.roleForm.value.description
          };
          
          this.snackBar.open('Role updated successfully', 'Close', {
            duration: 3000
          });
        }
      } else {
        // Add new role
        const newId = Math.max(...this.roles.map(r => r.id)) + 1;
        this.roles.push({
          id: newId,
          name: this.roleForm.value.name,
          description: this.roleForm.value.description,
          userCount: 0
        });
        
        this.snackBar.open('Role added successfully', 'Close', {
          duration: 3000
        });
      }
      
      this.resetForm();
    } else {
      this.markFormGroupTouched(this.roleForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  editRole(role: Role): void {
    this.selectedRole = role;
    this.isEditMode = true;
    this.roleForm.patchValue({
      name: role.name,
      description: role.description
    });
    
    // Set permissions based on role
    this.resetPermissions();
    if (role.name === 'Administrator') {
      // All permissions for admin
      this.permissions.forEach(p => p.selected = true);
    } else if (role.name === 'Inventory Clerk') {
      // Specific permissions for inventory clerk
      this.permissions.forEach(p => {
        if (p.category === 'Inventory' || p.name === 'View Products' || p.name === 'View Reports') {
          p.selected = true;
        }
      });
    }
  }

  deleteRole(role: Role): void {
    // In a real application, you would show a confirmation dialog
    if (role.userCount > 0) {
      this.snackBar.open(`Cannot delete role "${role.name}" because it is assigned to ${role.userCount} users.`, 'Close', {
        duration: 5000
      });
      return;
    }
    
    const index = this.roles.findIndex(r => r.id === role.id);
    if (index !== -1) {
      this.roles.splice(index, 1);
      
      this.snackBar.open('Role deleted successfully', 'Close', {
        duration: 3000
      });
      
      if (this.selectedRole && this.selectedRole.id === role.id) {
        this.resetForm();
      }
    }
  }

  resetForm(): void {
    this.roleForm.reset();
    this.selectedRole = null;
    this.isEditMode = false;
    this.resetPermissions();
  }

  resetPermissions(): void {
    this.permissions.forEach(p => p.selected = false);
  }

  cancelEdit(): void {
    this.resetForm();
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
