namespace SCM.Application.DTOs;

public class BarcodeDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string BarcodeValue { get; set; } = string.Empty;
    public string BarcodeType { get; set; } = "EAN13";
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal Quantity { get; set; } = 1;
    public bool IsPrimary { get; set; } = false;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateBarcodeDto
{
    public int ProductId { get; set; }
    public string BarcodeValue { get; set; } = string.Empty;
    public string BarcodeType { get; set; } = "EAN13";
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; } = 1;
    public bool IsPrimary { get; set; } = false;
}

public class UpdateBarcodeDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string BarcodeValue { get; set; } = string.Empty;
    public string BarcodeType { get; set; } = "EAN13";
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; } = 1;
    public bool IsPrimary { get; set; } = false;
    public bool IsActive { get; set; }
}
