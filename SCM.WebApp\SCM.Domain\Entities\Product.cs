using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Product : BaseEntity
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int? BrandId { get; set; }
    public int? UnitId { get; set; }
    public int? UnitGroupId { get; set; }
    public int? DepartmentId { get; set; }
    public int? GroupId { get; set; }
    public int? SubGroupId { get; set; }
    public decimal? CostPrice { get; set; }
    public decimal? AverageCost { get; set; }
    public decimal? SalesPrice { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public string? Notes { get; set; }
    public bool IsStockItem { get; set; } = true;
    public bool IsRecipe { get; set; } = false;
    public bool HasExpiry { get; set; } = false;
    public bool IsProduction { get; set; } = false;
    public bool IsSaleable { get; set; } = true;
    public int? TaxId { get; set; }
    public int? SalesUnitId { get; set; }
    public decimal? SalesUnitConversionFactor { get; set; }
    public bool AllowDiscount { get; set; } = true;
    
    // Navigation properties
    public virtual Brand? Brand { get; set; }
    public virtual Unit? Unit { get; set; }
    public virtual Department? Department { get; set; }
    public virtual ProductGroup? Group { get; set; }
    public virtual ProductSubGroup? SubGroup { get; set; }
    public virtual Tax? Tax { get; set; }
    public virtual Unit? SalesUnit { get; set; }
    public virtual ICollection<Recipe> Recipes { get; set; } = new List<Recipe>();
    public virtual ICollection<RecipeIngredient> RecipeIngredients { get; set; } = new List<RecipeIngredient>();
    public virtual ICollection<StockOnHand> StockOnHands { get; set; } = new List<StockOnHand>();
    public virtual ICollection<ProductCostCenterLink> CostCenterLinks { get; set; } = new List<ProductCostCenterLink>();
    public virtual ICollection<Batch> Batches { get; set; } = new List<Batch>();
    public virtual ICollection<TransactionDetail> TransactionDetails { get; set; } = new List<TransactionDetail>();
    public virtual ICollection<StockTakeDetail> StockTakeDetails { get; set; } = new List<StockTakeDetail>();
    public virtual ICollection<Barcode> Barcodes { get; set; } = new List<Barcode>();
}
