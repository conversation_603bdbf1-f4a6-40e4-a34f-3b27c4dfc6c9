using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class ProductGroup : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? DepartmentId { get; set; }
    
    // Navigation properties
    public virtual Department? Department { get; set; }
    public virtual ICollection<ProductSubGroup> SubGroups { get; set; } = new List<ProductSubGroup>();
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}
