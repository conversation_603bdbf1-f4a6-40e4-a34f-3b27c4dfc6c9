using System.ComponentModel.DataAnnotations;

namespace SCM.Application.DTOs;

public class PurchaseOrderDto
{
    public int Id { get; set; }
    public string DocumentNumber { get; set; } = string.Empty;
    public int SupplierId { get; set; }
    public string SupplierName { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public DateTime? ExpectedDeliveryDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public decimal TotalAmount { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<PurchaseOrderDetailDto> Details { get; set; } = new();
}

public class PurchaseOrderListDto
{
    public int Id { get; set; }
    public string DocumentNumber { get; set; } = string.Empty;
    public string SupplierName { get; set; } = string.Empty;
    public string CostCenterName { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public DateTime? ExpectedDeliveryDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
}

public class PurchaseOrderDetailDto
{
    public int Id { get; set; }
    public int PurchaseOrderId { get; set; }
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public string? Notes { get; set; }
}

public class CreatePurchaseOrderDto
{
    [Required]
    public int SupplierId { get; set; }
    
    [Required]
    public int CostCenterId { get; set; }
    
    [Required]
    public DateTime OrderDate { get; set; }
    
    public DateTime? ExpectedDeliveryDate { get; set; }
    
    public string? Notes { get; set; }
    
    [Required]
    public List<CreatePurchaseOrderDetailDto> Details { get; set; } = new();
}

public class CreatePurchaseOrderDetailDto
{
    [Required]
    public int PurchaseOrderId { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
    public decimal Quantity { get; set; }
    
    [Required]
    [Range(0, double.MaxValue, ErrorMessage = "Unit price must be greater than or equal to 0")]
    public decimal UnitPrice { get; set; }
    
    public string? Notes { get; set; }
}

public class UpdatePurchaseOrderDto
{
    [Required]
    public int Id { get; set; }
    
    [Required]
    public int SupplierId { get; set; }
    
    [Required]
    public int CostCenterId { get; set; }
    
    [Required]
    public DateTime OrderDate { get; set; }
    
    public DateTime? ExpectedDeliveryDate { get; set; }
    
    public string? Status { get; set; }
    
    public string? Notes { get; set; }
}

public class UpdatePurchaseOrderDetailDto
{
    [Required]
    public int Id { get; set; }
    
    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
    public decimal Quantity { get; set; }
    
    [Required]
    [Range(0, double.MaxValue, ErrorMessage = "Unit price must be greater than or equal to 0")]
    public decimal UnitPrice { get; set; }
    
    public string? Notes { get; set; }
}
