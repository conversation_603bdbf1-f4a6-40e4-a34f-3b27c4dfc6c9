<div class="page-container">
  <div class="page-header">
    <h1>Stock Adjustment</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="saveAdjustment()">Save</button>
    </div>
  </div>
  
  <form [formGroup]="adjustmentForm">
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="picker" formControlName="adjustmentDate">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>
      
      <div class="spacer"></div>
      
      <div class="reference-number">
        <span>Adjustment No.</span>
        <span class="reference-value">{{adjustmentNo}}</span>
      </div>
    </div>
    
    <div class="form-row">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Cost Center</mat-label>
        <mat-select formControlName="costCenter">
          <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
            {{costCenter.name}}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="adjustmentForm.get('costCenter')?.hasError('required')">
          Cost Center is required
        </mat-error>
      </mat-form-field>
    </div>
    
    <div class="form-row">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Notes</mat-label>
        <textarea matInput formControlName="notes" rows="2"></textarea>
      </mat-form-field>
    </div>
    
    <div class="table-container mat-elevation-z2">
      <table mat-table [dataSource]="items.controls">
        <!-- Product ID Column -->
        <ng-container matColumnDef="productId">
          <th mat-header-cell *matHeaderCellDef>Product</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productId')" [matAutocomplete]="auto">
              <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayProductFn">
                <mat-option *ngFor="let product of filteredProducts[i] | async" [value]="product.id">
                  {{product.id}} - {{product.name}} ({{product.unitSize}})
                </mat-option>
              </mat-autocomplete>
              <mat-error *ngIf="item.get('productId')?.hasError('required')">
                Product is required
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>
        
        <!-- Product Name Column -->
        <ng-container matColumnDef="productName">
          <th mat-header-cell *matHeaderCellDef>Description</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productName')" readonly>
            </mat-form-field>
          </td>
        </ng-container>
        
        <!-- Current Stock Column -->
        <ng-container matColumnDef="currentStock">
          <th mat-header-cell *matHeaderCellDef>Current Stock</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('currentStock')" readonly>
            </mat-form-field>
          </td>
        </ng-container>
        
        <!-- Adjustment Qty Column -->
        <ng-container matColumnDef="adjustmentQty">
          <th mat-header-cell *matHeaderCellDef>Adjustment Qty</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('adjustmentQty')">
              <mat-error *ngIf="item.get('adjustmentQty')?.hasError('required')">
                Adjustment quantity is required
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>
        
        <!-- New Stock Column -->
        <ng-container matColumnDef="newStock">
          <th mat-header-cell *matHeaderCellDef>New Stock</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('newStock')" readonly>
            </mat-form-field>
          </td>
        </ng-container>
        
        <!-- Reason Column -->
        <ng-container matColumnDef="reason">
          <th mat-header-cell *matHeaderCellDef>Reason</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <mat-select [formControl]="item.get('reason')">
                <mat-option *ngFor="let reason of adjustmentReasons" [value]="reason">
                  {{reason}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="item.get('reason')?.hasError('required')">
                Reason is required
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>
        
        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let item; let i = index">
            <button mat-icon-button color="warn" (click)="removeItem(i)">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>
        
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
      
      <div class="table-actions">
        <button mat-button color="primary" (click)="addItem()">
          <mat-icon>add</mat-icon> Add Item
        </button>
      </div>
    </div>
  </form>
</div>
