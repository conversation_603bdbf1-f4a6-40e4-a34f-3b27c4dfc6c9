{"version": 3, "sources": ["../../../../../node_modules/@angular/cdk/fesm2022/tree.mjs"], "sourcesContent": ["import { SelectionModel, isDataSource } from '@angular/cdk/collections';\nimport { isObservable, Subject, BehaviorSubject, of } from 'rxjs';\nimport { take, filter, takeUntil } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ViewChild, ContentChildren, numberAttribute, booleanAttribute, NgModule } from '@angular/core';\nimport * as i2 from '@angular/cdk/bidi';\n\n/** Base tree control. It has basic toggle/expand/collapse operations on a single data node. */\nclass BaseTreeControl {\n  constructor() {\n    /** A selection model with multi-selection to track expansion status. */\n    this.expansionModel = new SelectionModel(true);\n  }\n  /** Toggles one single data node's expanded/collapsed state. */\n  toggle(dataNode) {\n    this.expansionModel.toggle(this._trackByValue(dataNode));\n  }\n  /** Expands one single data node. */\n  expand(dataNode) {\n    this.expansionModel.select(this._trackByValue(dataNode));\n  }\n  /** Collapses one single data node. */\n  collapse(dataNode) {\n    this.expansionModel.deselect(this._trackByValue(dataNode));\n  }\n  /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n  isExpanded(dataNode) {\n    return this.expansionModel.isSelected(this._trackByValue(dataNode));\n  }\n  /** Toggles a subtree rooted at `node` recursively. */\n  toggleDescendants(dataNode) {\n    this.expansionModel.isSelected(this._trackByValue(dataNode)) ? this.collapseDescendants(dataNode) : this.expandDescendants(dataNode);\n  }\n  /** Collapse all dataNodes in the tree. */\n  collapseAll() {\n    this.expansionModel.clear();\n  }\n  /** Expands a subtree rooted at given data node recursively. */\n  expandDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  /** Collapses a subtree rooted at given data node recursively. */\n  collapseDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  _trackByValue(value) {\n    return this.trackBy ? this.trackBy(value) : value;\n  }\n}\n\n/** Flat tree control. Able to expand/collapse a subtree recursively for flattened tree. */\nclass FlatTreeControl extends BaseTreeControl {\n  /** Construct with flat tree data node functions getLevel and isExpandable. */\n  constructor(getLevel, isExpandable, options) {\n    super();\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n  /**\n   * Gets a list of the data node's subtree of descendent data nodes.\n   *\n   * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n   * with correct levels.\n   */\n  getDescendants(dataNode) {\n    const startIndex = this.dataNodes.indexOf(dataNode);\n    const results = [];\n    // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n    // The level of descendants of a tree node must be greater than the level of the given\n    // tree node.\n    // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n    // If we reach a node whose level is greater than the level of the tree node, we hit a\n    // sibling of an ancestor.\n    for (let i = startIndex + 1; i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]); i++) {\n      results.push(this.dataNodes[i]);\n    }\n    return results;\n  }\n  /**\n   * Expands all data nodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n  }\n}\n\n/** Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type. */\nclass NestedTreeControl extends BaseTreeControl {\n  /** Construct with nested tree function getChildren. */\n  constructor(getChildren, options) {\n    super();\n    this.getChildren = getChildren;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n  /**\n   * Expands all dataNodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.clear();\n    const allNodes = this.dataNodes.reduce((accumulator, dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode], []);\n    this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n  }\n  /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n  getDescendants(dataNode) {\n    const descendants = [];\n    this._getDescendants(descendants, dataNode);\n    // Remove the node itself\n    return descendants.splice(1);\n  }\n  /** A helper function to get descendants recursively. */\n  _getDescendants(descendants, dataNode) {\n    descendants.push(dataNode);\n    const childrenNodes = this.getChildren(dataNode);\n    if (Array.isArray(childrenNodes)) {\n      childrenNodes.forEach(child => this._getDescendants(descendants, child));\n    } else if (isObservable(childrenNodes)) {\n      // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n      // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n      childrenNodes.pipe(take(1), filter(Boolean)).subscribe(children => {\n        for (const child of children) {\n          this._getDescendants(descendants, child);\n        }\n      });\n    }\n  }\n}\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst CDK_TREE_NODE_OUTLET_NODE = new InjectionToken('CDK_TREE_NODE_OUTLET_NODE');\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass CdkTreeNodeOutlet {\n  constructor(viewContainer, _node) {\n    this.viewContainer = viewContainer;\n    this._node = _node;\n  }\n  static {\n    this.ɵfac = function CdkTreeNodeOutlet_Factory(t) {\n      return new (t || CdkTreeNodeOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_TREE_NODE_OUTLET_NODE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNodeOutlet,\n      selectors: [[\"\", \"cdkTreeNodeOutlet\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_TREE_NODE_OUTLET_NODE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/** Context provided to the tree node component. */\nclass CdkTreeNodeOutletContext {\n  constructor(data) {\n    this.$implicit = data;\n  }\n}\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass CdkTreeNodeDef {\n  /** @docs-private */\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkTreeNodeDef_Factory(t) {\n      return new (t || CdkTreeNodeDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNodeDef,\n      selectors: [[\"\", \"cdkTreeNodeDef\", \"\"]],\n      inputs: {\n        when: [i0.ɵɵInputFlags.None, \"cdkTreeNodeDefWhen\", \"when\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeDef]',\n      inputs: [{\n        name: 'when',\n        alias: 'cdkTreeNodeDefWhen'\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nfunction getTreeNoValidDataSourceError() {\n  return Error(`A valid data source must be provided.`);\n}\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nfunction getTreeMultipleDefaultNodeDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nfunction getTreeMissingMatchingNodeDefError() {\n  return Error(`Could not find a matching node definition for the provided node data.`);\n}\n/**\n * Returns an error to be thrown when there are tree control.\n * @docs-private\n */\nfunction getTreeControlMissingError() {\n  return Error(`Could not find a tree control for the tree.`);\n}\n/**\n * Returns an error to be thrown when tree control did not implement functions for flat/nested node.\n * @docs-private\n */\nfunction getTreeControlFunctionsMissingError() {\n  return Error(`Could not find functions for nested/flat tree in tree control.`);\n}\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\nclass CdkTree {\n  /**\n   * Provides a stream containing the latest data array to render. Influenced by the tree's\n   * stream of view window (what dataNodes are currently on screen).\n   * Data source can be an observable of data array, or a data array to render.\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  constructor(_differs, _changeDetectorRef) {\n    this._differs = _differs;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Subject that emits when the component has been destroyed. */\n    this._onDestroy = new Subject();\n    /** Level of nodes */\n    this._levels = new Map();\n    // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n    //     Remove the MAX_VALUE in viewChange\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     */\n    this.viewChange = new BehaviorSubject({\n      start: 0,\n      end: Number.MAX_VALUE\n    });\n  }\n  ngOnInit() {\n    this._dataDiffer = this._differs.find([]).create(this.trackBy);\n    if (!this.treeControl && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeControlMissingError();\n    }\n  }\n  ngOnDestroy() {\n    this._nodeOutlet.viewContainer.clear();\n    this.viewChange.complete();\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n      this.dataSource.disconnect(this);\n    }\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n  }\n  ngAfterContentChecked() {\n    const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n    if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMultipleDefaultNodeDefsError();\n    }\n    this._defaultNodeDef = defaultNodeDefs[0];\n    if (this.dataSource && this._nodeDefs && !this._dataSubscription) {\n      this._observeRenderChanges();\n    }\n  }\n  // TODO(tinayuangao): Work on keyboard traversal and actions, make sure it's working for RTL\n  //     and nested trees.\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the node outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n      this.dataSource.disconnect(this);\n    }\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n    // Remove the all dataNodes if there is now no data source\n    if (!dataSource) {\n      this._nodeOutlet.viewContainer.clear();\n    }\n    this._dataSource = dataSource;\n    if (this._nodeDefs) {\n      this._observeRenderChanges();\n    }\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _observeRenderChanges() {\n    let dataStream;\n    if (isDataSource(this._dataSource)) {\n      dataStream = this._dataSource.connect(this);\n    } else if (isObservable(this._dataSource)) {\n      dataStream = this._dataSource;\n    } else if (Array.isArray(this._dataSource)) {\n      dataStream = of(this._dataSource);\n    }\n    if (dataStream) {\n      this._dataSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => this.renderNodeChanges(data));\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTreeNoValidDataSourceError();\n    }\n  }\n  /** Check for changes made in the data and render each change (node added/removed/moved). */\n  renderNodeChanges(data, dataDiffer = this._dataDiffer, viewContainer = this._nodeOutlet.viewContainer, parentData) {\n    const changes = dataDiffer.diff(data);\n    if (!changes) {\n      return;\n    }\n    changes.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n      if (item.previousIndex == null) {\n        this.insertNode(data[currentIndex], currentIndex, viewContainer, parentData);\n      } else if (currentIndex == null) {\n        viewContainer.remove(adjustedPreviousIndex);\n        this._levels.delete(item.item);\n      } else {\n        const view = viewContainer.get(adjustedPreviousIndex);\n        viewContainer.move(view, currentIndex);\n      }\n    });\n    this._changeDetectorRef.detectChanges();\n  }\n  /**\n   * Finds the matching node definition that should be used for this node data. If there is only\n   * one node definition, it is returned. Otherwise, find the node definition that has a when\n   * predicate that returns true with the data. If none return true, return the default node\n   * definition.\n   */\n  _getNodeDef(data, i) {\n    if (this._nodeDefs.length === 1) {\n      return this._nodeDefs.first;\n    }\n    const nodeDef = this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n    if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMissingMatchingNodeDefError();\n    }\n    return nodeDef;\n  }\n  /**\n   * Create the embedded view for the data node template and place it in the correct index location\n   * within the data node view container.\n   */\n  insertNode(nodeData, index, viewContainer, parentData) {\n    const node = this._getNodeDef(nodeData, index);\n    // Node context that will be provided to created embedded view\n    const context = new CdkTreeNodeOutletContext(nodeData);\n    // If the tree is flat tree, then use the `getLevel` function in flat tree control\n    // Otherwise, use the level of parent node.\n    if (this.treeControl.getLevel) {\n      context.level = this.treeControl.getLevel(nodeData);\n    } else if (typeof parentData !== 'undefined' && this._levels.has(parentData)) {\n      context.level = this._levels.get(parentData) + 1;\n    } else {\n      context.level = 0;\n    }\n    this._levels.set(nodeData, context.level);\n    // Use default tree nodeOutlet, or nested node's nodeOutlet\n    const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n    container.createEmbeddedView(node.template, context, index);\n    // Set the data to just created `CdkTreeNode`.\n    // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n    //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n    if (CdkTreeNode.mostRecentTreeNode) {\n      CdkTreeNode.mostRecentTreeNode.data = nodeData;\n    }\n  }\n  static {\n    this.ɵfac = function CdkTree_Factory(t) {\n      return new (t || CdkTree)(i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkTree,\n      selectors: [[\"cdk-tree\"]],\n      contentQueries: function CdkTree_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeDef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeDefs = _t);\n        }\n      },\n      viewQuery: function CdkTree_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkTreeNodeOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"tree\", 1, \"cdk-tree\"],\n      inputs: {\n        dataSource: \"dataSource\",\n        treeControl: \"treeControl\",\n        trackBy: \"trackBy\"\n      },\n      exportAs: [\"cdkTree\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkTreeNodeOutlet\", \"\"]],\n      template: function CdkTree_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkTreeNodeOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTree, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-tree',\n      exportAs: 'cdkTree',\n      template: `<ng-container cdkTreeNodeOutlet></ng-container>`,\n      host: {\n        'class': 'cdk-tree',\n        'role': 'tree'\n      },\n      encapsulation: ViewEncapsulation.None,\n      // The \"OnPush\" status for the `CdkTree` component is effectively a noop, so we are removing it.\n      // The view for `CdkTree` consists entirely of templates declared in other views. As they are\n      // declared elsewhere, they are checked when their declaration points are checked.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      standalone: true,\n      imports: [CdkTreeNodeOutlet]\n    }]\n  }], () => [{\n    type: i0.IterableDiffers\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    dataSource: [{\n      type: Input\n    }],\n    treeControl: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    _nodeOutlet: [{\n      type: ViewChild,\n      args: [CdkTreeNodeOutlet, {\n        static: true\n      }]\n    }],\n    _nodeDefs: [{\n      type: ContentChildren,\n      args: [CdkTreeNodeDef, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\nclass CdkTreeNode {\n  /**\n   * The role of the tree node.\n   * @deprecated The correct role is 'treeitem', 'group' should not be used. This input will be\n   *   removed in a future version.\n   * @breaking-change 12.0.0 Remove this input\n   */\n  get role() {\n    return 'treeitem';\n  }\n  set role(_role) {\n    // TODO: move to host after View Engine deprecation\n    this._elementRef.nativeElement.setAttribute('role', _role);\n  }\n  /**\n   * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n   * in `CdkTree` and set the data to it.\n   */\n  static {\n    this.mostRecentTreeNode = null;\n  }\n  /** The tree node's data. */\n  get data() {\n    return this._data;\n  }\n  set data(value) {\n    if (value !== this._data) {\n      this._data = value;\n      this._setRoleFromData();\n      this._dataChanges.next();\n    }\n  }\n  get isExpanded() {\n    return this._tree.treeControl.isExpanded(this._data);\n  }\n  get level() {\n    // If the treeControl has a getLevel method, use it to get the level. Otherwise read the\n    // aria-level off the parent node and use it as the level for this node (note aria-level is\n    // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n    return this._tree.treeControl.getLevel ? this._tree.treeControl.getLevel(this._data) : this._parentNodeAriaLevel;\n  }\n  constructor(_elementRef, _tree) {\n    this._elementRef = _elementRef;\n    this._tree = _tree;\n    /** Subject that emits when the component has been destroyed. */\n    this._destroyed = new Subject();\n    /** Emits when the node's data has changed. */\n    this._dataChanges = new Subject();\n    CdkTreeNode.mostRecentTreeNode = this;\n    this.role = 'treeitem';\n  }\n  ngOnInit() {\n    this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n    this._elementRef.nativeElement.setAttribute('aria-level', `${this.level + 1}`);\n  }\n  ngOnDestroy() {\n    // If this is the last tree node being destroyed,\n    // clear out the reference to avoid leaking memory.\n    if (CdkTreeNode.mostRecentTreeNode === this) {\n      CdkTreeNode.mostRecentTreeNode = null;\n    }\n    this._dataChanges.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Focuses the menu item. Implements for FocusableOption. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  // TODO: role should eventually just be set in the component host\n  _setRoleFromData() {\n    if (!this._tree.treeControl.isExpandable && !this._tree.treeControl.getChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeControlFunctionsMissingError();\n    }\n    this.role = 'treeitem';\n  }\n  static {\n    this.ɵfac = function CdkTreeNode_Factory(t) {\n      return new (t || CdkTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CdkTree));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNode,\n      selectors: [[\"cdk-tree-node\"]],\n      hostAttrs: [1, \"cdk-tree-node\"],\n      hostVars: 1,\n      hostBindings: function CdkTreeNode_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-expanded\", ctx.isExpanded);\n        }\n      },\n      inputs: {\n        role: \"role\"\n      },\n      exportAs: [\"cdkTreeNode\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-tree-node',\n      exportAs: 'cdkTreeNode',\n      host: {\n        'class': 'cdk-tree-node',\n        '[attr.aria-expanded]': 'isExpanded'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: CdkTree\n  }], {\n    role: [{\n      type: Input\n    }]\n  });\n})();\nfunction getParentNodeAriaLevel(nodeElement) {\n  let parent = nodeElement.parentElement;\n  while (parent && !isNodeElement(parent)) {\n    parent = parent.parentElement;\n  }\n  if (!parent) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw Error('Incorrect tree structure containing detached node.');\n    } else {\n      return -1;\n    }\n  } else if (parent.classList.contains('cdk-nested-tree-node')) {\n    return numberAttribute(parent.getAttribute('aria-level'));\n  } else {\n    // The ancestor element is the cdk-tree itself\n    return 0;\n  }\n}\nfunction isNodeElement(element) {\n  const classList = element.classList;\n  return !!(classList?.contains('cdk-nested-tree-node') || classList?.contains('cdk-tree'));\n}\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\nclass CdkNestedTreeNode extends CdkTreeNode {\n  constructor(elementRef, tree, _differs) {\n    super(elementRef, tree);\n    this._differs = _differs;\n  }\n  ngAfterContentInit() {\n    this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n    if (!this._tree.treeControl.getChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeControlFunctionsMissingError();\n    }\n    const childrenNodes = this._tree.treeControl.getChildren(this.data);\n    if (Array.isArray(childrenNodes)) {\n      this.updateChildrenNodes(childrenNodes);\n    } else if (isObservable(childrenNodes)) {\n      childrenNodes.pipe(takeUntil(this._destroyed)).subscribe(result => this.updateChildrenNodes(result));\n    }\n    this.nodeOutlet.changes.pipe(takeUntil(this._destroyed)).subscribe(() => this.updateChildrenNodes());\n  }\n  // This is a workaround for https://github.com/angular/angular/issues/23091\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  ngOnInit() {\n    super.ngOnInit();\n  }\n  ngOnDestroy() {\n    this._clear();\n    super.ngOnDestroy();\n  }\n  /** Add children dataNodes to the NodeOutlet */\n  updateChildrenNodes(children) {\n    const outlet = this._getNodeOutlet();\n    if (children) {\n      this._children = children;\n    }\n    if (outlet && this._children) {\n      const viewContainer = outlet.viewContainer;\n      this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n    } else {\n      // Reset the data differ if there's no children nodes displayed\n      this._dataDiffer.diff([]);\n    }\n  }\n  /** Clear the children dataNodes. */\n  _clear() {\n    const outlet = this._getNodeOutlet();\n    if (outlet) {\n      outlet.viewContainer.clear();\n      this._dataDiffer.diff([]);\n    }\n  }\n  /** Gets the outlet for the current node. */\n  _getNodeOutlet() {\n    const outlets = this.nodeOutlet;\n    // Note that since we use `descendants: true` on the query, we have to ensure\n    // that we don't pick up the outlet of a child node by accident.\n    return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n  }\n  static {\n    this.ɵfac = function CdkNestedTreeNode_Factory(t) {\n      return new (t || CdkNestedTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkNestedTreeNode,\n      selectors: [[\"cdk-nested-tree-node\"]],\n      contentQueries: function CdkNestedTreeNode_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeOutlet, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeOutlet = _t);\n        }\n      },\n      hostAttrs: [1, \"cdk-nested-tree-node\"],\n      exportAs: [\"cdkNestedTreeNode\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNode,\n        useExisting: CdkNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: CdkNestedTreeNode\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNestedTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-nested-tree-node',\n      exportAs: 'cdkNestedTreeNode',\n      providers: [{\n        provide: CdkTreeNode,\n        useExisting: CdkNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: CdkNestedTreeNode\n      }],\n      host: {\n        'class': 'cdk-nested-tree-node'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: CdkTree\n  }, {\n    type: i0.IterableDiffers\n  }], {\n    nodeOutlet: [{\n      type: ContentChildren,\n      args: [CdkTreeNodeOutlet, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\nclass CdkTreeNodePadding {\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  get level() {\n    return this._level;\n  }\n  set level(value) {\n    this._setLevelInput(value);\n  }\n  /**\n   * The indent for each level. Can be a number or a CSS string.\n   * Default number 40px from material design menu sub-menu spec.\n   */\n  get indent() {\n    return this._indent;\n  }\n  set indent(indent) {\n    this._setIndentInput(indent);\n  }\n  constructor(_treeNode, _tree, _element, _dir) {\n    this._treeNode = _treeNode;\n    this._tree = _tree;\n    this._element = _element;\n    this._dir = _dir;\n    /** Subject that emits when the component has been destroyed. */\n    this._destroyed = new Subject();\n    /** CSS units used for the indentation value. */\n    this.indentUnits = 'px';\n    this._indent = 40;\n    this._setPadding();\n    if (_dir) {\n      _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n    }\n    // In Ivy the indentation binding might be set before the tree node's data has been added,\n    // which means that we'll miss the first render. We have to subscribe to changes in the\n    // data to ensure that everything is up to date.\n    _treeNode._dataChanges.subscribe(() => this._setPadding());\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n  _paddingIndent() {\n    const nodeLevel = this._treeNode.data && this._tree.treeControl.getLevel ? this._tree.treeControl.getLevel(this._treeNode.data) : null;\n    const level = this._level == null ? nodeLevel : this._level;\n    return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n  }\n  _setPadding(forceChange = false) {\n    const padding = this._paddingIndent();\n    if (padding !== this._currentPadding || forceChange) {\n      const element = this._element.nativeElement;\n      const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n      const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n      element.style[paddingProp] = padding || '';\n      element.style[resetProp] = '';\n      this._currentPadding = padding;\n    }\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setLevelInput(value) {\n    // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n    // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n    // they set 0 explicitly.\n    this._level = isNaN(value) ? null : value;\n    this._setPadding();\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setIndentInput(indent) {\n    let value = indent;\n    let units = 'px';\n    if (typeof indent === 'string') {\n      const parts = indent.split(cssUnitPattern);\n      value = parts[0];\n      units = parts[1] || units;\n    }\n    this.indentUnits = units;\n    this._indent = numberAttribute(value);\n    this._setPadding();\n  }\n  static {\n    this.ɵfac = function CdkTreeNodePadding_Factory(t) {\n      return new (t || CdkTreeNodePadding)(i0.ɵɵdirectiveInject(CdkTreeNode), i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNodePadding,\n      selectors: [[\"\", \"cdkTreeNodePadding\", \"\"]],\n      inputs: {\n        level: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkTreeNodePadding\", \"level\", numberAttribute],\n        indent: [i0.ɵɵInputFlags.None, \"cdkTreeNodePaddingIndent\", \"indent\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodePadding, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodePadding]',\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkTreeNode\n  }, {\n    type: CdkTree\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    level: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTreeNodePadding',\n        transform: numberAttribute\n      }]\n    }],\n    indent: [{\n      type: Input,\n      args: ['cdkTreeNodePaddingIndent']\n    }]\n  });\n})();\n\n/**\n * Node toggle to expand/collapse the node.\n */\nclass CdkTreeNodeToggle {\n  constructor(_tree, _treeNode) {\n    this._tree = _tree;\n    this._treeNode = _treeNode;\n    /** Whether expand/collapse the node recursively. */\n    this.recursive = false;\n  }\n  _toggle(event) {\n    this.recursive ? this._tree.treeControl.toggleDescendants(this._treeNode.data) : this._tree.treeControl.toggle(this._treeNode.data);\n    event.stopPropagation();\n  }\n  static {\n    this.ɵfac = function CdkTreeNodeToggle_Factory(t) {\n      return new (t || CdkTreeNodeToggle)(i0.ɵɵdirectiveInject(CdkTree), i0.ɵɵdirectiveInject(CdkTreeNode));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTreeNodeToggle,\n      selectors: [[\"\", \"cdkTreeNodeToggle\", \"\"]],\n      hostBindings: function CdkTreeNodeToggle_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkTreeNodeToggle_click_HostBindingHandler($event) {\n            return ctx._toggle($event);\n          });\n        }\n      },\n      inputs: {\n        recursive: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkTreeNodeToggleRecursive\", \"recursive\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeToggle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeToggle]',\n      host: {\n        '(click)': '_toggle($event)'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkTree\n  }, {\n    type: CdkTreeNode\n  }], {\n    recursive: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTreeNodeToggleRecursive',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet];\nclass CdkTreeModule {\n  static {\n    this.ɵfac = function CdkTreeModule_Factory(t) {\n      return new (t || CdkTreeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkTreeModule,\n      imports: [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet],\n      exports: [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: EXPORTED_DECLARATIONS,\n      exports: EXPORTED_DECLARATIONS\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseTreeControl, CDK_TREE_NODE_OUTLET_NODE, CdkNestedTreeNode, CdkTree, CdkTreeModule, CdkTreeNode, CdkTreeNodeDef, CdkTreeNodeOutlet, CdkTreeNodeOutletContext, CdkTreeNodePadding, CdkTreeNodeToggle, FlatTreeControl, NestedTreeControl, getTreeControlFunctionsMissingError, getTreeControlMissingError, getTreeMissingMatchingNodeDefError, getTreeMultipleDefaultNodeDefsError, getTreeNoValidDataSourceError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,kBAAN,MAAsB;AAAA,EACpB,cAAc;AAEZ,SAAK,iBAAiB,IAAI,eAAe,IAAI;AAAA,EAC/C;AAAA;AAAA,EAEA,OAAO,UAAU;AACf,SAAK,eAAe,OAAO,KAAK,cAAc,QAAQ,CAAC;AAAA,EACzD;AAAA;AAAA,EAEA,OAAO,UAAU;AACf,SAAK,eAAe,OAAO,KAAK,cAAc,QAAQ,CAAC;AAAA,EACzD;AAAA;AAAA,EAEA,SAAS,UAAU;AACjB,SAAK,eAAe,SAAS,KAAK,cAAc,QAAQ,CAAC;AAAA,EAC3D;AAAA;AAAA,EAEA,WAAW,UAAU;AACnB,WAAO,KAAK,eAAe,WAAW,KAAK,cAAc,QAAQ,CAAC;AAAA,EACpE;AAAA;AAAA,EAEA,kBAAkB,UAAU;AAC1B,SAAK,eAAe,WAAW,KAAK,cAAc,QAAQ,CAAC,IAAI,KAAK,oBAAoB,QAAQ,IAAI,KAAK,kBAAkB,QAAQ;AAAA,EACrI;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,eAAe,MAAM;AAAA,EAC5B;AAAA;AAAA,EAEA,kBAAkB,UAAU;AAC1B,QAAI,gBAAgB,CAAC,QAAQ;AAC7B,kBAAc,KAAK,GAAG,KAAK,eAAe,QAAQ,CAAC;AACnD,SAAK,eAAe,OAAO,GAAG,cAAc,IAAI,WAAS,KAAK,cAAc,KAAK,CAAC,CAAC;AAAA,EACrF;AAAA;AAAA,EAEA,oBAAoB,UAAU;AAC5B,QAAI,gBAAgB,CAAC,QAAQ;AAC7B,kBAAc,KAAK,GAAG,KAAK,eAAe,QAAQ,CAAC;AACnD,SAAK,eAAe,SAAS,GAAG,cAAc,IAAI,WAAS,KAAK,cAAc,KAAK,CAAC,CAAC;AAAA,EACvF;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AAAA,EAC9C;AACF;AAGA,IAAM,kBAAN,cAA8B,gBAAgB;AAAA;AAAA,EAE5C,YAAY,UAAU,cAAc,SAAS;AAC3C,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU,KAAK,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAAU;AACvB,UAAM,aAAa,KAAK,UAAU,QAAQ,QAAQ;AAClD,UAAM,UAAU,CAAC;AAOjB,aAAS,IAAI,aAAa,GAAG,IAAI,KAAK,UAAU,UAAU,KAAK,SAAS,QAAQ,IAAI,KAAK,SAAS,KAAK,UAAU,CAAC,CAAC,GAAG,KAAK;AACzH,cAAQ,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,SAAK,eAAe,OAAO,GAAG,KAAK,UAAU,IAAI,UAAQ,KAAK,cAAc,IAAI,CAAC,CAAC;AAAA,EACpF;AACF;AAGA,IAAM,oBAAN,cAAgC,gBAAgB;AAAA;AAAA,EAE9C,YAAY,aAAa,SAAS;AAChC,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU,KAAK,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,SAAK,eAAe,MAAM;AAC1B,UAAM,WAAW,KAAK,UAAU,OAAO,CAAC,aAAa,aAAa,CAAC,GAAG,aAAa,GAAG,KAAK,eAAe,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;AAClI,SAAK,eAAe,OAAO,GAAG,SAAS,IAAI,UAAQ,KAAK,cAAc,IAAI,CAAC,CAAC;AAAA,EAC9E;AAAA;AAAA,EAEA,eAAe,UAAU;AACvB,UAAM,cAAc,CAAC;AACrB,SAAK,gBAAgB,aAAa,QAAQ;AAE1C,WAAO,YAAY,OAAO,CAAC;AAAA,EAC7B;AAAA;AAAA,EAEA,gBAAgB,aAAa,UAAU;AACrC,gBAAY,KAAK,QAAQ;AACzB,UAAM,gBAAgB,KAAK,YAAY,QAAQ;AAC/C,QAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,oBAAc,QAAQ,WAAS,KAAK,gBAAgB,aAAa,KAAK,CAAC;AAAA,IACzE,WAAW,aAAa,aAAa,GAAG;AAGtC,oBAAc,KAAK,KAAK,CAAC,GAAG,OAAO,OAAO,CAAC,EAAE,UAAU,cAAY;AACjE,mBAAW,SAAS,UAAU;AAC5B,eAAK,gBAAgB,aAAa,KAAK;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAOA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAKhF,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,eAAe,OAAO;AAChC,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,gBAAgB,GAAM,kBAAkB,2BAA2B,CAAC,CAAC;AAAA,IACnI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YAAY,MAAM;AAChB,SAAK,YAAY;AAAA,EACnB;AACF;AAKA,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,kBAAqB,WAAW,CAAC;AAAA,IACvE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,MACtC,QAAQ;AAAA,QACN,MAAM,CAAI,WAAa,MAAM,sBAAsB,MAAM;AAAA,MAC3D;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,SAAS,gCAAgC;AACvC,SAAO,MAAM,uCAAuC;AACtD;AAKA,SAAS,sCAAsC;AAC7C,SAAO,MAAM,sEAAsE;AACrF;AAKA,SAAS,qCAAqC;AAC5C,SAAO,MAAM,uEAAuE;AACtF;AAKA,SAAS,6BAA6B;AACpC,SAAO,MAAM,6CAA6C;AAC5D;AAKA,SAAS,sCAAsC;AAC7C,SAAO,MAAM,gEAAgE;AAC/E;AAMA,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,QAAI,KAAK,gBAAgB,YAAY;AACnC,WAAK,kBAAkB,UAAU;AAAA,IACnC;AAAA,EACF;AAAA,EACA,YAAY,UAAU,oBAAoB;AACxC,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAE1B,SAAK,aAAa,IAAI,QAAQ;AAE9B,SAAK,UAAU,oBAAI,IAAI;AAOvB,SAAK,aAAa,IAAI,gBAAgB;AAAA,MACpC,OAAO;AAAA,MACP,KAAK,OAAO;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,cAAc,KAAK,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,OAAO;AAC7D,QAAI,CAAC,KAAK,gBAAgB,OAAO,cAAc,eAAe,YAAY;AACxE,YAAM,2BAA2B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,cAAc,MAAM;AACrC,SAAK,WAAW,SAAS;AACzB,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,QAAI,KAAK,eAAe,OAAO,KAAK,YAAY,eAAe,YAAY;AACzE,WAAK,WAAW,WAAW,IAAI;AAAA,IACjC;AACA,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,YAAY;AACnC,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,UAAM,kBAAkB,KAAK,UAAU,OAAO,SAAO,CAAC,IAAI,IAAI;AAC9D,QAAI,gBAAgB,SAAS,MAAM,OAAO,cAAc,eAAe,YAAY;AACjF,YAAM,oCAAoC;AAAA,IAC5C;AACA,SAAK,kBAAkB,gBAAgB,CAAC;AACxC,QAAI,KAAK,cAAc,KAAK,aAAa,CAAC,KAAK,mBAAmB;AAChE,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,YAAY;AAC5B,QAAI,KAAK,eAAe,OAAO,KAAK,YAAY,eAAe,YAAY;AACzE,WAAK,WAAW,WAAW,IAAI;AAAA,IACjC;AACA,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,YAAY;AACnC,WAAK,oBAAoB;AAAA,IAC3B;AAEA,QAAI,CAAC,YAAY;AACf,WAAK,YAAY,cAAc,MAAM;AAAA,IACvC;AACA,SAAK,cAAc;AACnB,QAAI,KAAK,WAAW;AAClB,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI;AACJ,QAAI,aAAa,KAAK,WAAW,GAAG;AAClC,mBAAa,KAAK,YAAY,QAAQ,IAAI;AAAA,IAC5C,WAAW,aAAa,KAAK,WAAW,GAAG;AACzC,mBAAa,KAAK;AAAA,IACpB,WAAW,MAAM,QAAQ,KAAK,WAAW,GAAG;AAC1C,mBAAa,GAAG,KAAK,WAAW;AAAA,IAClC;AACA,QAAI,YAAY;AACd,WAAK,oBAAoB,WAAW,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,UAAQ,KAAK,kBAAkB,IAAI,CAAC;AAAA,IACrH,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,YAAM,8BAA8B;AAAA,IACtC;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,MAAM,aAAa,KAAK,aAAa,gBAAgB,KAAK,YAAY,eAAe,YAAY;AACjH,UAAM,UAAU,WAAW,KAAK,IAAI;AACpC,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,YAAQ,iBAAiB,CAAC,MAAM,uBAAuB,iBAAiB;AACtE,UAAI,KAAK,iBAAiB,MAAM;AAC9B,aAAK,WAAW,KAAK,YAAY,GAAG,cAAc,eAAe,UAAU;AAAA,MAC7E,WAAW,gBAAgB,MAAM;AAC/B,sBAAc,OAAO,qBAAqB;AAC1C,aAAK,QAAQ,OAAO,KAAK,IAAI;AAAA,MAC/B,OAAO;AACL,cAAM,OAAO,cAAc,IAAI,qBAAqB;AACpD,sBAAc,KAAK,MAAM,YAAY;AAAA,MACvC;AAAA,IACF,CAAC;AACD,SAAK,mBAAmB,cAAc;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,GAAG;AACnB,QAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,UAAM,UAAU,KAAK,UAAU,KAAK,SAAO,IAAI,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK;AAClF,QAAI,CAAC,YAAY,OAAO,cAAc,eAAe,YAAY;AAC/D,YAAM,mCAAmC;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,UAAU,OAAO,eAAe,YAAY;AACrD,UAAM,OAAO,KAAK,YAAY,UAAU,KAAK;AAE7C,UAAM,UAAU,IAAI,yBAAyB,QAAQ;AAGrD,QAAI,KAAK,YAAY,UAAU;AAC7B,cAAQ,QAAQ,KAAK,YAAY,SAAS,QAAQ;AAAA,IACpD,WAAW,OAAO,eAAe,eAAe,KAAK,QAAQ,IAAI,UAAU,GAAG;AAC5E,cAAQ,QAAQ,KAAK,QAAQ,IAAI,UAAU,IAAI;AAAA,IACjD,OAAO;AACL,cAAQ,QAAQ;AAAA,IAClB;AACA,SAAK,QAAQ,IAAI,UAAU,QAAQ,KAAK;AAExC,UAAM,YAAY,gBAAgB,gBAAgB,KAAK,YAAY;AACnE,cAAU,mBAAmB,KAAK,UAAU,SAAS,KAAK;AAI1D,QAAI,YAAY,oBAAoB;AAClC,kBAAY,mBAAmB,OAAO;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,aAAO,KAAK,KAAK,UAAY,kBAAqB,eAAe,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IAChH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,gBAAgB,SAAS,uBAAuB,IAAI,KAAK,UAAU;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,gBAAgB,CAAC;AAAA,QAC/C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,mBAAmB,CAAC;AAAA,QACrC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,QAAQ,QAAQ,GAAG,UAAU;AAAA,MACzC,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,CAAC;AAAA,MAClC,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,YAAI,KAAK,GAAG;AACV,UAAG,mBAAmB,GAAG,CAAC;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,cAAc,CAAC,iBAAiB;AAAA,MAChC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,eAAe,oBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA;AAAA;AAAA,QAGrB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA,EACA,IAAI,KAAK,OAAO;AAEd,SAAK,YAAY,cAAc,aAAa,QAAQ,KAAK;AAAA,EAC3D;AAAA,EAKA,OAAO;AACL,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,QAAI,UAAU,KAAK,OAAO;AACxB,WAAK,QAAQ;AACb,WAAK,iBAAiB;AACtB,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,MAAM,YAAY,WAAW,KAAK,KAAK;AAAA,EACrD;AAAA,EACA,IAAI,QAAQ;AAIV,WAAO,KAAK,MAAM,YAAY,WAAW,KAAK,MAAM,YAAY,SAAS,KAAK,KAAK,IAAI,KAAK;AAAA,EAC9F;AAAA,EACA,YAAY,aAAa,OAAO;AAC9B,SAAK,cAAc;AACnB,SAAK,QAAQ;AAEb,SAAK,aAAa,IAAI,QAAQ;AAE9B,SAAK,eAAe,IAAI,QAAQ;AAChC,iBAAY,qBAAqB;AACjC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,uBAAuB,uBAAuB,KAAK,YAAY,aAAa;AACjF,SAAK,YAAY,cAAc,aAAa,cAAc,GAAG,KAAK,QAAQ,CAAC,EAAE;AAAA,EAC/E;AAAA,EACA,cAAc;AAGZ,QAAI,aAAY,uBAAuB,MAAM;AAC3C,mBAAY,qBAAqB;AAAA,IACnC;AACA,SAAK,aAAa,SAAS;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,YAAY,cAAc,MAAM;AAAA,EACvC;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,CAAC,KAAK,MAAM,YAAY,gBAAgB,CAAC,KAAK,MAAM,YAAY,gBAAgB,OAAO,cAAc,eAAe,YAAY;AAClI,YAAM,oCAAoC;AAAA,IAC5C;AACA,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAgB,kBAAqB,UAAU,GAAM,kBAAkB,OAAO,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,UAAU;AAAA,MACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,UAAU;AAAA,QAChD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,uBAAuB,aAAa;AAC3C,MAAI,SAAS,YAAY;AACzB,SAAO,UAAU,CAAC,cAAc,MAAM,GAAG;AACvC,aAAS,OAAO;AAAA,EAClB;AACA,MAAI,CAAC,QAAQ;AACX,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAM,MAAM,oDAAoD;AAAA,IAClE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,WAAW,OAAO,UAAU,SAAS,sBAAsB,GAAG;AAC5D,WAAO,gBAAgB,OAAO,aAAa,YAAY,CAAC;AAAA,EAC1D,OAAO;AAEL,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,SAAS;AAC9B,QAAM,YAAY,QAAQ;AAC1B,SAAO,CAAC,EAAE,WAAW,SAAS,sBAAsB,KAAK,WAAW,SAAS,UAAU;AACzF;AAQA,IAAM,oBAAN,MAAM,2BAA0B,YAAY;AAAA,EAC1C,YAAY,YAAY,MAAM,UAAU;AACtC,UAAM,YAAY,IAAI;AACtB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,qBAAqB;AACnB,SAAK,cAAc,KAAK,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,MAAM,OAAO;AACnE,QAAI,CAAC,KAAK,MAAM,YAAY,gBAAgB,OAAO,cAAc,eAAe,YAAY;AAC1F,YAAM,oCAAoC;AAAA,IAC5C;AACA,UAAM,gBAAgB,KAAK,MAAM,YAAY,YAAY,KAAK,IAAI;AAClE,QAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,WAAK,oBAAoB,aAAa;AAAA,IACxC,WAAW,aAAa,aAAa,GAAG;AACtC,oBAAc,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU,KAAK,oBAAoB,MAAM,CAAC;AAAA,IACrG;AACA,SAAK,WAAW,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,oBAAoB,CAAC;AAAA,EACrG;AAAA;AAAA;AAAA,EAGA,WAAW;AACT,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,cAAc;AACZ,SAAK,OAAO;AACZ,UAAM,YAAY;AAAA,EACpB;AAAA;AAAA,EAEA,oBAAoB,UAAU;AAC5B,UAAM,SAAS,KAAK,eAAe;AACnC,QAAI,UAAU;AACZ,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,UAAU,KAAK,WAAW;AAC5B,YAAM,gBAAgB,OAAO;AAC7B,WAAK,MAAM,kBAAkB,KAAK,WAAW,KAAK,aAAa,eAAe,KAAK,KAAK;AAAA,IAC1F,OAAO;AAEL,WAAK,YAAY,KAAK,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,UAAM,SAAS,KAAK,eAAe;AACnC,QAAI,QAAQ;AACV,aAAO,cAAc,MAAM;AAC3B,WAAK,YAAY,KAAK,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,UAAU,KAAK;AAGrB,WAAO,WAAW,QAAQ,KAAK,YAAU,CAAC,OAAO,SAAS,OAAO,UAAU,IAAI;AAAA,EACjF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,UAAU,GAAM,kBAAkB,OAAO,GAAM,kBAAqB,eAAe,CAAC;AAAA,IAClJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,mBAAmB,CAAC;AAAA,QAClD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa;AAAA,QAChE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA;AAAA;AAAA,QAGxB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,iBAAiB;AAKvB,IAAM,qBAAN,MAAM,oBAAmB;AAAA;AAAA,EAEvB,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,gBAAgB,MAAM;AAAA,EAC7B;AAAA,EACA,YAAY,WAAW,OAAO,UAAU,MAAM;AAC5C,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,OAAO;AAEZ,SAAK,aAAa,IAAI,QAAQ;AAE9B,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,QAAI,MAAM;AACR,WAAK,OAAO,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,YAAY,IAAI,CAAC;AAAA,IACrF;AAIA,cAAU,aAAa,UAAU,MAAM,KAAK,YAAY,CAAC;AAAA,EAC3D;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,YAAY,KAAK,UAAU,QAAQ,KAAK,MAAM,YAAY,WAAW,KAAK,MAAM,YAAY,SAAS,KAAK,UAAU,IAAI,IAAI;AAClI,UAAM,QAAQ,KAAK,UAAU,OAAO,YAAY,KAAK;AACrD,WAAO,OAAO,UAAU,WAAW,GAAG,QAAQ,KAAK,OAAO,GAAG,KAAK,WAAW,KAAK;AAAA,EACpF;AAAA,EACA,YAAY,cAAc,OAAO;AAC/B,UAAM,UAAU,KAAK,eAAe;AACpC,QAAI,YAAY,KAAK,mBAAmB,aAAa;AACnD,YAAM,UAAU,KAAK,SAAS;AAC9B,YAAM,cAAc,KAAK,QAAQ,KAAK,KAAK,UAAU,QAAQ,iBAAiB;AAC9E,YAAM,YAAY,gBAAgB,gBAAgB,iBAAiB;AACnE,cAAQ,MAAM,WAAW,IAAI,WAAW;AACxC,cAAQ,MAAM,SAAS,IAAI;AAC3B,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,OAAO;AAIpB,SAAK,SAAS,MAAM,KAAK,IAAI,OAAO;AACpC,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,QAAQ;AACtB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,OAAO,WAAW,UAAU;AAC9B,YAAM,QAAQ,OAAO,MAAM,cAAc;AACzC,cAAQ,MAAM,CAAC;AACf,cAAQ,MAAM,CAAC,KAAK;AAAA,IACtB;AACA,SAAK,cAAc;AACnB,SAAK,UAAU,gBAAgB,KAAK;AACpC,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAkB,WAAW,GAAM,kBAAkB,OAAO,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACxL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,MAC1C,QAAQ;AAAA,QACN,OAAO,CAAI,WAAa,4BAA4B,sBAAsB,SAAS,eAAe;AAAA,QAClG,QAAQ,CAAI,WAAa,MAAM,4BAA4B,QAAQ;AAAA,MACrE;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,wBAAwB;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,OAAO,WAAW;AAC5B,SAAK,QAAQ;AACb,SAAK,YAAY;AAEjB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,YAAY,KAAK,MAAM,YAAY,kBAAkB,KAAK,UAAU,IAAI,IAAI,KAAK,MAAM,YAAY,OAAO,KAAK,UAAU,IAAI;AAClI,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAkB,OAAO,GAAM,kBAAkB,WAAW,CAAC;AAAA,IACtG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,2CAA2C,QAAQ;AACjF,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW,CAAI,WAAa,4BAA4B,8BAA8B,aAAa,gBAAgB;AAAA,MACrH;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,wBAAwB;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB,CAAC,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAChJ,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAAA,MAC3H,SAAS,CAAC,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAAA,IAC7H,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}