.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-sidenav {
  width: 280px;
  background-color: #ffffff;
  border-right: 1px solid #e0e0e0;
}

.sidenav-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #e0e0e0;
}

.logo {
  height: 40px;
}

.app-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
}

.toolbar-spacer {
  flex: 1 1 auto;
}

.content-container {
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 64px);
}

.node-icon {
  margin-right: 8px;
  color: #1976d2;
}

.node-label {
  text-decoration: none;

  &.clickable {
    cursor: pointer;
    color: #1976d2;
    display: inline-block;

    &:hover {
      text-decoration: underline;
    }
  }
}

mat-tree {
  padding: 8px;
}

mat-tree-node {
  min-height: 36px !important;
}

.mat-mdc-tab-link {
  min-width: 100px;
  padding: 0 16px;
}

mat-toolbar {
  height: 64px;
}

mat-sidenav-container {
  height: 100%;
}

.user-menu-header {
  display: flex;
  align-items: center;
  padding: 16px;

  mat-icon {
    margin-right: 8px;
  }

  span {
    font-weight: 500;
  }
}
