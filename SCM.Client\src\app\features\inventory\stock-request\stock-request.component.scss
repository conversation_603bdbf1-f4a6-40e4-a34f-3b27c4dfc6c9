.page-container {
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
}

.spinner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  border-radius: 4px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;

  &.two-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}

.full-width {
  width: 100%;
}

.spacer {
  flex: 1;
}

.reference-number {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.reference-value {
  font-weight: bold;
  font-size: 1.2em;
  color: #1976d2;
}

.table-container {
  margin: 20px 0;
  overflow-x: auto;
  border-radius: 4px;
}

table {
  width: 100%;
}

.table-form-field {
  width: 100%;
  margin: 0;
  font-size: 14px;
}

.table-form-field ::ng-deep .mat-mdc-form-field-infix {
  padding: 8px 0;
  width: auto;
}

.table-form-field ::ng-deep .mat-mdc-text-field-wrapper {
  padding: 0 8px;
}

.table-actions {
  padding: 8px;
  display: flex;
  justify-content: flex-start;
}

.totals-section {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.totals-row {
  display: flex;
  justify-content: space-between;
  width: 300px;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.total-value {
  font-weight: bold;
  color: #1976d2;
}

// Responsive adjustments
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    margin-top: 10px;
  }

  .form-row {
    flex-direction: column;
    align-items: stretch;
  }

  .reference-number {
    align-items: flex-start;
    margin-top: 10px;
  }

  .totals-section {
    align-items: stretch;
  }

  .totals-row {
    width: 100%;
  }
}
