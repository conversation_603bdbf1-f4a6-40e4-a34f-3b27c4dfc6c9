import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, RouterOutlet, Router } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatTreeModule } from '@angular/material/tree';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { AuthService, User } from '../../core/services/auth.service';
import { BusinessStructureService } from '../../core/services/business-structure.service';
import { Subscription } from 'rxjs';

interface NavigationNode {
  name: string;
  icon?: string;
  children?: NavigationNode[];
  path?: string;
}

interface FlatNode {
  expandable: boolean;
  name: string;
  level: number;
  icon?: string;
  path?: string;
}

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    RouterOutlet,
    MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatTreeModule,
    MatTabsModule,
    MatDividerModule
  ],
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss']
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  private _transformer = (node: NavigationNode, level: number): FlatNode => {
    return {
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      level: level,
      icon: node.icon,
      path: node.path
    };
  };

  treeControl = new FlatTreeControl<FlatNode>(
    node => node.level,
    node => node.expandable
  );

  treeFlattener = new MatTreeFlattener(
    this._transformer,
    node => node.level,
    node => node.expandable,
    node => node.children
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  navLinks = [
    { path: '/', label: 'Dashboard' },
    { path: '/products', label: 'Products' },
    { path: '/inventory', label: 'Inventory' },
    { path: '/transactions', label: 'Transactions' },
    { path: '/recipes', label: 'Recipes' },
    { path: '/users', label: 'Users', roles: ['Admin'] },
    { path: '/configuration', label: 'Configuration' }
  ];

  currentUser: User | null = null;
  private userSubscription: Subscription | null = null;

  businessStructure: NavigationNode[] = [];
  applicationMenu: NavigationNode[] = [];

  constructor(
    private authService: AuthService,
    private router: Router,
    private businessStructureService: BusinessStructureService
  ) {
    // Initialize application menu
    this.applicationMenu = [
      {
        name: 'Dashboard',
        icon: 'dashboard',
        path: '/'
      },
      {
        name: 'Products',
        icon: 'inventory_2',
        children: [
          { name: 'Product List', icon: 'list', path: '/products' },
          { name: 'Add Product', icon: 'add', path: '/products/new' },
          { name: 'Categories', icon: 'category', path: '/products/categories' }
        ]
      },
      {
        name: 'Inventory',
        icon: 'inventory',
        children: [
          { name: 'Stock Request', icon: 'shopping_cart', path: '/inventory/stock-request' },
          { name: 'Stock Adjustment', icon: 'tune', path: '/inventory/stock-adjustment' },
          { name: 'Stock Taking', icon: 'fact_check', path: '/inventory/stock-taking' },
          { name: 'Stock Transfer', icon: 'swap_horiz', path: '/inventory/stock-transfer' }
        ]
      },
      {
        name: 'Transactions',
        icon: 'receipt_long',
        children: [
          { name: 'Purchase Orders', icon: 'shopping_cart', path: '/transactions/purchase-orders' },
          { name: 'Goods Receiving', icon: 'local_shipping', path: '/transactions/goods-receiving' },
          { name: 'Sales', icon: 'point_of_sale', path: '/transactions/sales' }
        ]
      },
      {
        name: 'Recipes',
        icon: 'menu_book',
        children: [
          { name: 'Recipe List', icon: 'list', path: '/recipes' },
          { name: 'Add Recipe', icon: 'add', path: '/recipes/new' }
        ]
      },
      {
        name: 'Users',
        icon: 'people',
        children: [
          { name: 'User List', icon: 'list', path: '/users' },
          { name: 'Add User', icon: 'person_add', path: '/users/new' },
          { name: 'Role Management', icon: 'admin_panel_settings', path: '/users/roles' }
        ]
      },
      {
        name: 'Configuration',
        icon: 'settings',
        children: [
          { name: 'Store Configuration', icon: 'store', path: '/configuration/store-config' },
          { name: 'Departments', icon: 'category', path: '/configuration/departments' },
          { name: 'Cost Centers', icon: 'attach_money', path: '/configuration/cost-centers' },
          { name: 'User Permissions', icon: 'security', path: '/configuration/permissions' }
        ]
      }
    ];

    // Load business structure
    this.loadBusinessStructure();
  }

  private loadBusinessStructure(): void {
    this.businessStructureService.getBusinessStructure().subscribe({
      next: (structure) => {
        // Transform the business structure into navigation nodes
        this.businessStructure = this.transformBusinessStructure(structure);

        // Combine business structure with application menu
        this.updateNavigationTree();
      },
      error: (error) => {
        console.error('Error loading business structure:', error);
        // If there's an error, just use the application menu
        this.updateNavigationTree();
      }
    });
  }

  private transformBusinessStructure(structure: any[]): NavigationNode[] {
    return structure.map(unit => ({
      name: unit.name,
      icon: 'business',
      children: unit.children?.map((location: any) => ({
        name: location.name,
        icon: 'location_city',
        children: location.children?.map((store: any) => ({
          name: store.name,
          icon: 'store',
          children: store.children?.map((department: any) => ({
            name: department.name,
            icon: 'category'
          }))
        }))
      }))
    }));
  }

  private updateNavigationTree(): void {
    // Combine business structure with application menu
    this.dataSource.data = [
      ...this.businessStructure,
      ...this.applicationMenu
    ];
  }

  ngOnInit(): void {
    // Initialize the navigation tree
    this.updateNavigationTree();

    // Subscribe to user changes
    this.userSubscription = this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.updateNavigationBasedOnRole();

      // Expand the first level nodes by default after data is loaded
      setTimeout(() => {
        this.treeControl.dataNodes.forEach(node => {
          if (node.level === 0) {
            this.treeControl.expand(node);
          }
        });
      }, 100);
    });
  }

  ngOnDestroy(): void {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  updateNavigationBasedOnRole(): void {
    // Filter navLinks based on user role
    this.navLinks = this.navLinks.filter(link => {
      if (!link.roles) return true;
      return this.currentUser && link.roles.some(role =>
        this.authService.hasRole(role)
      );
    });
  }

  hasChild = (_: number, node: FlatNode) => node.expandable;

  logout(): void {
    this.authService.logout();
  }

  get userDisplayName(): string {
    return this.currentUser?.fullName || this.currentUser?.username || 'User';
  }

  navigateTo(path: string | undefined): void {
    if (path) {
      this.router.navigate([path]);
    }
  }
}
