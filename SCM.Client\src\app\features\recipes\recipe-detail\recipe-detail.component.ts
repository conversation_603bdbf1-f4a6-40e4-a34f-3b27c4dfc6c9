import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON>rray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Observable, map, startWith } from 'rxjs';

interface Ingredient {
  id: string;
  name: string;
  unitSize: string;
  unitCost: number;
}

@Component({
  selector: 'app-recipe-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatTabsModule,
    MatCheckboxModule,
    RouterModule
  ],
  templateUrl: './recipe-detail.component.html',
  styleUrls: ['./recipe-detail.component.scss']
})
export class RecipeDetailComponent implements OnInit {
  recipeForm!: FormGroup;
  isEditMode: boolean = false;
  recipeId: string | null = null;
  
  displayedColumns: string[] = [
    'ingredient', 
    'quantity', 
    'unit', 
    'unitCost', 
    'totalCost', 
    'actions'
  ];
  
  categories: string[] = [
    'Main Course', 
    'Appetizer', 
    'Dessert', 
    'Beverage', 
    'Breakfast', 
    'Lunch', 
    'Dinner'
  ];
  
  yieldUnits: string[] = [
    'servings',
    'pieces',
    'slices',
    'glasses',
    'cups',
    'portions'
  ];
  
  ingredients: Ingredient[] = [
    { id: 'P001', name: 'Rice', unitSize: '25kg', unitCost: 45.50 },
    { id: 'P002', name: 'Flour', unitSize: '10kg', unitCost: 22.75 },
    { id: 'P003', name: 'Soft Drinks', unitSize: '24x330ml', unitCost: 36.00 },
    { id: 'P004', name: 'Cleaning Liquid', unitSize: '5L', unitCost: 18.25 },
    { id: 'P005', name: 'Light Bulbs', unitSize: '10pcs', unitCost: 42.00 },
    { id: 'P006', name: 'Cigarettes', unitSize: '200pcs', unitCost: 120.00 },
    { id: 'P007', name: 'Paper Towels', unitSize: '12 rolls', unitCost: 24.50 },
    { id: 'P008', name: 'Coffee', unitSize: '1kg', unitCost: 65.00 },
    { id: 'P009', name: 'Sugar', unitSize: '5kg', unitCost: 15.50 },
    { id: 'P010', name: 'Salt', unitSize: '1kg', unitCost: 3.25 },
    { id: 'P011', name: 'Pepper', unitSize: '500g', unitCost: 8.75 },
    { id: 'P012', name: 'Olive Oil', unitSize: '1L', unitCost: 12.50 },
    { id: 'P013', name: 'Tomatoes', unitSize: '1kg', unitCost: 4.50 },
    { id: 'P014', name: 'Onions', unitSize: '1kg', unitCost: 3.00 },
    { id: 'P015', name: 'Garlic', unitSize: '500g', unitCost: 5.25 }
  ];
  
  filteredIngredients: Observable<Ingredient[]>[] = [];
  
  units: string[] = [
    'g',
    'kg',
    'ml',
    'L',
    'tsp',
    'tbsp',
    'cup',
    'piece',
    'slice',
    'pinch'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
    
    this.route.paramMap.subscribe(params => {
      this.recipeId = params.get('id');
      if (this.recipeId) {
        this.isEditMode = true;
        this.loadRecipeData(this.recipeId);
      }
    });
  }

  initForm(): void {
    this.recipeForm = this.fb.group({
      id: [{ value: '', disabled: this.isEditMode }, Validators.required],
      name: ['', Validators.required],
      category: ['', Validators.required],
      yield: [1, [Validators.required, Validators.min(1)]],
      yieldUnit: ['servings', Validators.required],
      description: [''],
      instructions: [''],
      isActive: [true],
      ingredients: this.fb.array([])
    });
    
    // Add an empty ingredient row by default
    this.addIngredient();
  }

  get ingredients_(): FormArray {
    return this.recipeForm.get('ingredients') as FormArray;
  }

  addIngredient(): void {
    const ingredientForm = this.fb.group({
      ingredient: ['', Validators.required],
      quantity: [1, [Validators.required, Validators.min(0.01)]],
      unit: ['', Validators.required],
      unitCost: [{ value: 0, disabled: true }],
      totalCost: [{ value: 0, disabled: true }]
    });
    
    // Set up ingredient autocomplete filtering
    const index = this.ingredients_.length;
    this.setupIngredientAutocomplete(ingredientForm, index);
    
    // Auto-calculate total cost when quantity changes
    ingredientForm.get('quantity')?.valueChanges.subscribe(() => {
      this.calculateIngredientCost(ingredientForm);
    });
    
    this.ingredients_.push(ingredientForm);
  }

  setupIngredientAutocomplete(ingredientForm: FormGroup, index: number): void {
    const ingredientControl = ingredientForm.get('ingredient');
    
    this.filteredIngredients[index] = ingredientControl!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterIngredients(value || ''))
    );
    
    ingredientControl?.valueChanges.subscribe(ingredientId => {
      const ingredient = this.ingredients.find(i => i.id === ingredientId);
      if (ingredient) {
        ingredientForm.patchValue({
          unitCost: ingredient.unitCost
        });
        this.calculateIngredientCost(ingredientForm);
      } else {
        ingredientForm.patchValue({
          unitCost: 0
        });
      }
    });
  }

  private _filterIngredients(value: string): Ingredient[] {
    const filterValue = value.toLowerCase();
    return this.ingredients.filter(ingredient => 
      ingredient.id.toLowerCase().includes(filterValue) || 
      ingredient.name.toLowerCase().includes(filterValue)
    );
  }

  calculateIngredientCost(ingredientForm: FormGroup): void {
    const quantity = ingredientForm.get('quantity')?.value || 0;
    const unitCost = ingredientForm.get('unitCost')?.value || 0;
    const totalCost = quantity * unitCost;
    
    ingredientForm.get('totalCost')?.setValue(totalCost);
    this.calculateTotalCost();
  }

  calculateTotalCost(): number {
    let total = 0;
    for (const ingredient of this.ingredients_.controls) {
      total += ingredient.get('totalCost')?.value || 0;
    }
    return total;
  }

  calculateCostPerUnit(): number {
    const totalCost = this.calculateTotalCost();
    const yield_ = this.recipeForm.get('yield')?.value || 1;
    return totalCost / yield_;
  }

  removeIngredient(index: number): void {
    this.ingredients_.removeAt(index);
    this.filteredIngredients.splice(index, 1);
    this.calculateTotalCost();
  }

  displayIngredientFn(ingredientId: string): string {
    if (!ingredientId) return '';
    const ingredient = this.ingredients.find(i => i.id === ingredientId);
    return ingredient ? `${ingredient.id} - ${ingredient.name}` : '';
  }

  loadRecipeData(id: string): void {
    // In a real application, this would be an API call
    // For now, we'll just simulate loading data
    if (id === 'R001') {
      this.recipeForm.patchValue({
        id: 'R001',
        name: 'Spaghetti Bolognese',
        category: 'Main Course',
        yield: 4,
        yieldUnit: 'servings',
        description: 'Classic Italian pasta dish with a rich meat sauce.',
        instructions: '1. Cook the pasta according to package instructions.\n2. In a large pan, heat olive oil and sauté onions and garlic.\n3. Add ground beef and cook until browned.\n4. Add tomatoes, tomato paste, and herbs. Simmer for 30 minutes.\n5. Serve sauce over pasta with grated cheese.',
        isActive: true
      });
      
      // Clear existing ingredients
      while (this.ingredients_.length !== 0) {
        this.ingredients_.removeAt(0);
      }
      
      // Add ingredients
      const ingredients = [
        { ingredient: 'P013', quantity: 0.5, unit: 'kg', unitCost: 4.50 },
        { ingredient: 'P014', quantity: 0.2, unit: 'kg', unitCost: 3.00 },
        { ingredient: 'P015', quantity: 0.05, unit: 'kg', unitCost: 5.25 },
        { ingredient: 'P012', quantity: 0.1, unit: 'L', unitCost: 12.50 }
      ];
      
      ingredients.forEach(ing => {
        const ingredientForm = this.fb.group({
          ingredient: [ing.ingredient, Validators.required],
          quantity: [ing.quantity, [Validators.required, Validators.min(0.01)]],
          unit: [ing.unit, Validators.required],
          unitCost: [{ value: ing.unitCost, disabled: true }],
          totalCost: [{ value: ing.quantity * ing.unitCost, disabled: true }]
        });
        
        const index = this.ingredients_.length;
        this.setupIngredientAutocomplete(ingredientForm, index);
        
        ingredientForm.get('quantity')?.valueChanges.subscribe(() => {
          this.calculateIngredientCost(ingredientForm);
        });
        
        this.ingredients_.push(ingredientForm);
      });
    }
  }

  saveRecipe(): void {
    if (this.recipeForm.valid) {
      console.log('Recipe data:', this.recipeForm.getRawValue());
      
      this.snackBar.open('Recipe saved successfully', 'Close', {
        duration: 3000
      });
      
      if (!this.isEditMode) {
        this.router.navigate(['/recipes']);
      }
    } else {
      this.markFormGroupTouched(this.recipeForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/recipes']);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
