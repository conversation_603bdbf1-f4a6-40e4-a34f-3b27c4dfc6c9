using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class ProductCostCenterLink : BaseEntity
{
    public int ProductId { get; set; }
    public int CostCenterId { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public bool AutoReorder { get; set; } = false;
    
    // Navigation properties
    public virtual Product Product { get; set; } = null!;
    public virtual CostCenter CostCenter { get; set; } = null!;
}
