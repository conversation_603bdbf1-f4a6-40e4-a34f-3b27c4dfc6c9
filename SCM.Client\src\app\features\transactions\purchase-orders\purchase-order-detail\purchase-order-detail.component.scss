.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

mat-card {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row mat-form-field {
  flex: 1;
}

.full-width {
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24px 0 16px;
}

.table-container {
  overflow-x: auto;
  border-radius: 4px;
  margin-bottom: 20px;
}

.no-data-message {
  padding: 20px;
  text-align: center;
  color: #757575;
}

table {
  width: 100%;
}

.order-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px;
  font-size: 18px;
  font-weight: 500;
}

.total-amount {
  margin-left: 16px;
  font-size: 20px;
  font-weight: 700;
  color: #1976d2;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.status-chip {
  margin-left: 8px;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-draft {
  background-color: #e0e0e0;
  color: #616161;
}

.status-pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.status-approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-rejected {
  background-color: #ffebee;
  color: #c62828;
}

.status-completed {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-cancelled {
  background-color: #f5f5f5;
  color: #757575;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .header-actions {
    flex-direction: column;
  }
}
