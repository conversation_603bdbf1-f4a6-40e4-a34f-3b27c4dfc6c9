using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class StockService : IStockService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public StockService(
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<StockOnHandDto>> GetStockOnHandAsync()
    {
        var stockOnHand = await _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Batch)
            .Include(s => s.Unit)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockOnHandDto>>(stockOnHand);
    }

    public async Task<IEnumerable<StockOnHandDto>> GetStockOnHandByProductIdAsync(int productId)
    {
        var stockOnHand = await _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Batch)
            .Include(s => s.Unit)
            .Where(s => s.ProductId == productId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockOnHandDto>>(stockOnHand);
    }

    public async Task<IEnumerable<StockOnHandDto>> GetStockOnHandByCostCenterIdAsync(int costCenterId)
    {
        var stockOnHand = await _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Batch)
            .Include(s => s.Unit)
            .Where(s => s.CostCenterId == costCenterId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockOnHandDto>>(stockOnHand);
    }

    public async Task<StockOnHandDto?> GetStockOnHandAsync(int productId, int costCenterId, int batchId)
    {
        var stockOnHand = await _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Batch)
            .Include(s => s.Unit)
            .FirstOrDefaultAsync(s => 
                s.ProductId == productId && 
                s.CostCenterId == costCenterId && 
                s.BatchId == batchId);
                
        return stockOnHand != null ? _mapper.Map<StockOnHandDto>(stockOnHand) : null;
    }

    public async Task<IEnumerable<StockOnHandSummaryDto>> GetStockSummaryByCostCenterIdAsync(int costCenterId)
    {
        var stockSummary = await _dbContext.StockOnHand
            .Where(s => s.CostCenterId == costCenterId)
            .GroupBy(s => new { s.ProductId, s.CostCenterId })
            .Select(g => new
            {
                ProductId = g.Key.ProductId,
                CostCenterId = g.Key.CostCenterId,
                TotalQuantity = g.Sum(s => s.Quantity),
                AverageCostPrice = g.Average(s => s.CostPrice)
            })
            .ToListAsync();
            
        var result = new List<StockOnHandSummaryDto>();
        
        foreach (var item in stockSummary)
        {
            var product = await _dbContext.Products
                .Include(p => p.Unit)
                .FirstOrDefaultAsync(p => p.Id == item.ProductId);
                
            var costCenter = await _dbContext.CostCenters
                .FirstOrDefaultAsync(c => c.Id == item.CostCenterId);
                
            var productCostCenterLink = await _dbContext.ProductCostCenterLinks
                .FirstOrDefaultAsync(pcl => 
                    pcl.ProductId == item.ProductId && 
                    pcl.CostCenterId == item.CostCenterId);
                
            var minStock = productCostCenterLink?.MinStock ?? product?.MinStock;
            var maxStock = productCostCenterLink?.MaxStock ?? product?.MaxStock;
            var reorderPoint = productCostCenterLink?.ReorderPoint ?? product?.ReorderPoint;
            
            var summary = new StockOnHandSummaryDto
            {
                ProductId = item.ProductId,
                ProductName = product?.Name ?? string.Empty,
                ProductCode = product?.Code ?? string.Empty,
                CostCenterId = item.CostCenterId,
                CostCenterName = costCenter?.Name ?? string.Empty,
                TotalQuantity = item.TotalQuantity,
                AverageCostPrice = item.AverageCostPrice,
                UnitName = product?.Unit?.Name,
                MinStock = minStock,
                MaxStock = maxStock,
                ReorderPoint = reorderPoint,
                IsLowStock = minStock.HasValue && item.TotalQuantity < minStock.Value,
                IsOverStock = maxStock.HasValue && item.TotalQuantity > maxStock.Value,
                NeedsReorder = reorderPoint.HasValue && item.TotalQuantity <= reorderPoint.Value
            };
            
            result.Add(summary);
        }
        
        return result;
    }

    public async Task<IEnumerable<StockOnHandSummaryDto>> GetLowStockItemsAsync(int? costCenterId = null)
    {
        var allStockSummaries = new List<StockOnHandSummaryDto>();
        
        if (costCenterId.HasValue)
        {
            allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(costCenterId.Value));
        }
        else
        {
            var costCenters = await _dbContext.CostCenters.ToListAsync();
            foreach (var cc in costCenters)
            {
                allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(cc.Id));
            }
        }
        
        return allStockSummaries.Where(s => s.IsLowStock).ToList();
    }

    public async Task<IEnumerable<StockOnHandSummaryDto>> GetOverStockItemsAsync(int? costCenterId = null)
    {
        var allStockSummaries = new List<StockOnHandSummaryDto>();
        
        if (costCenterId.HasValue)
        {
            allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(costCenterId.Value));
        }
        else
        {
            var costCenters = await _dbContext.CostCenters.ToListAsync();
            foreach (var cc in costCenters)
            {
                allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(cc.Id));
            }
        }
        
        return allStockSummaries.Where(s => s.IsOverStock).ToList();
    }

    public async Task<IEnumerable<StockOnHandSummaryDto>> GetReorderItemsAsync(int? costCenterId = null)
    {
        var allStockSummaries = new List<StockOnHandSummaryDto>();
        
        if (costCenterId.HasValue)
        {
            allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(costCenterId.Value));
        }
        else
        {
            var costCenters = await _dbContext.CostCenters.ToListAsync();
            foreach (var cc in costCenters)
            {
                allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(cc.Id));
            }
        }
        
        return allStockSummaries.Where(s => s.NeedsReorder).ToList();
    }

    public async Task<IEnumerable<StockOnHandDto>> GetExpiringStockAsync(int daysToExpiry, int? costCenterId = null)
    {
        var expiryDate = DateTime.UtcNow.AddDays(daysToExpiry);
        
        var query = _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Batch)
            .Include(s => s.Unit)
            .Where(s => 
                s.Batch.ExpiryDate.HasValue && 
                s.Batch.ExpiryDate <= expiryDate &&
                s.Quantity > 0);
                
        if (costCenterId.HasValue)
        {
            query = query.Where(s => s.CostCenterId == costCenterId.Value);
        }
        
        var expiringStock = await query.ToListAsync();
        return _mapper.Map<IEnumerable<StockOnHandDto>>(expiringStock);
    }

    public async Task AdjustStockAsync(StockAdjustmentDto stockAdjustmentDto)
    {
        var stockOnHand = await _dbContext.StockOnHand
            .FirstOrDefaultAsync(s => 
                s.ProductId == stockAdjustmentDto.ProductId && 
                s.CostCenterId == stockAdjustmentDto.CostCenterId && 
                s.BatchId == stockAdjustmentDto.BatchId);
                
        if (stockOnHand == null)
        {
            // Create new stock record if it doesn't exist
            stockOnHand = new StockOnHand
            {
                ProductId = stockAdjustmentDto.ProductId,
                CostCenterId = stockAdjustmentDto.CostCenterId,
                BatchId = stockAdjustmentDto.BatchId,
                UnitId = stockAdjustmentDto.UnitId,
                Quantity = stockAdjustmentDto.Quantity,
                CostPrice = stockAdjustmentDto.CostPrice,
                LastUpdated = DateTime.UtcNow
            };
            
            _dbContext.StockOnHand.Add(stockOnHand);
        }
        else
        {
            // Update existing stock record
            stockOnHand.Quantity = stockAdjustmentDto.Quantity;
            if (stockAdjustmentDto.CostPrice.HasValue)
            {
                stockOnHand.CostPrice = stockAdjustmentDto.CostPrice;
            }
            stockOnHand.LastUpdated = DateTime.UtcNow;
            
            _dbContext.StockOnHand.Update(stockOnHand);
        }
        
        // Update product average cost if needed
        if (stockAdjustmentDto.CostPrice.HasValue)
        {
            await UpdateProductAverageCostAsync(stockAdjustmentDto.ProductId);
        }
        
        await _dbContext.SaveChangesAsync();
        
        // TODO: Create stock adjustment transaction record
    }
    
    private async Task UpdateProductAverageCostAsync(int productId)
    {
        var product = await _dbContext.Products.FindAsync(productId);
        if (product == null)
            return;
            
        var stockItems = await _dbContext.StockOnHand
            .Where(s => s.ProductId == productId && s.Quantity > 0 && s.CostPrice.HasValue)
            .ToListAsync();
            
        if (stockItems.Any())
        {
            var totalValue = stockItems.Sum(s => s.Quantity * s.CostPrice.GetValueOrDefault());
            var totalQuantity = stockItems.Sum(s => s.Quantity);
            
            if (totalQuantity > 0)
            {
                product.AverageCost = totalValue / totalQuantity;
                _dbContext.Products.Update(product);
            }
        }
    }
}
