import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

export interface Supplier {
  id: number;
  code: string;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  taxNumber?: string;
  isActive: boolean;
}

export interface CreateSupplier {
  code: string;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  taxNumber?: string;
}

export interface UpdateSupplier {
  id: number;
  code: string;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  taxNumber?: string;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SupplierService {
  private readonly path = 'suppliers';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<Supplier[]> {
    return this.apiService.get<Supplier[]>(this.path);
  }

  getById(id: number): Observable<Supplier> {
    return this.apiService.get<Supplier>(`${this.path}/${id}`);
  }

  getByCode(code: string): Observable<Supplier> {
    return this.apiService.get<Supplier>(`${this.path}/code/${code}`);
  }

  create(supplier: CreateSupplier): Observable<Supplier> {
    return this.apiService.post<Supplier, CreateSupplier>(this.path, supplier);
  }

  update(id: number, supplier: UpdateSupplier): Observable<void> {
    return this.apiService.put<void, UpdateSupplier>(`${this.path}/${id}`, supplier);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
