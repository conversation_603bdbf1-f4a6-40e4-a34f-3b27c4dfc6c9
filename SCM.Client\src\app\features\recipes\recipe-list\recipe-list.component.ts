import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';

interface Recipe {
  id: string;
  name: string;
  category: string;
  yield: number;
  yieldUnit: string;
  ingredientCount: number;
  costPerUnit: number;
  status: string;
}

@Component({
  selector: 'app-recipe-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatChipsModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './recipe-list.component.html',
  styleUrls: ['./recipe-list.component.scss']
})
export class RecipeListComponent implements OnInit {
  displayedColumns: string[] = ['id', 'name', 'category', 'yield', 'ingredientCount', 'costPerUnit', 'status', 'actions'];
  recipes: Recipe[] = [];
  filteredRecipes: Recipe[] = [];
  searchTerm: string = '';
  selectedCategory: string = '';
  
  categories: string[] = [
    'Main Course', 
    'Appetizer', 
    'Dessert', 
    'Beverage', 
    'Breakfast', 
    'Lunch', 
    'Dinner'
  ];

  constructor() {
    // Mock data for demonstration
    this.recipes = [
      {
        id: 'R001',
        name: 'Spaghetti Bolognese',
        category: 'Main Course',
        yield: 4,
        yieldUnit: 'servings',
        ingredientCount: 8,
        costPerUnit: 3.75,
        status: 'Active'
      },
      {
        id: 'R002',
        name: 'Caesar Salad',
        category: 'Appetizer',
        yield: 2,
        yieldUnit: 'servings',
        ingredientCount: 6,
        costPerUnit: 2.50,
        status: 'Active'
      },
      {
        id: 'R003',
        name: 'Chocolate Cake',
        category: 'Dessert',
        yield: 8,
        yieldUnit: 'slices',
        ingredientCount: 10,
        costPerUnit: 1.25,
        status: 'Active'
      },
      {
        id: 'R004',
        name: 'Fruit Smoothie',
        category: 'Beverage',
        yield: 2,
        yieldUnit: 'glasses',
        ingredientCount: 4,
        costPerUnit: 1.80,
        status: 'Active'
      },
      {
        id: 'R005',
        name: 'Pancakes',
        category: 'Breakfast',
        yield: 6,
        yieldUnit: 'pancakes',
        ingredientCount: 5,
        costPerUnit: 0.75,
        status: 'Active'
      },
      {
        id: 'R006',
        name: 'Chicken Curry',
        category: 'Main Course',
        yield: 4,
        yieldUnit: 'servings',
        ingredientCount: 12,
        costPerUnit: 4.25,
        status: 'Active'
      },
      {
        id: 'R007',
        name: 'Tiramisu',
        category: 'Dessert',
        yield: 8,
        yieldUnit: 'slices',
        ingredientCount: 7,
        costPerUnit: 2.10,
        status: 'Inactive'
      }
    ];
    
    this.filteredRecipes = [...this.recipes];
  }

  ngOnInit(): void {
  }

  applyFilter(): void {
    this.filteredRecipes = this.recipes.filter(recipe => {
      const matchesSearch = this.searchTerm === '' || 
        recipe.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        recipe.id.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesCategory = this.selectedCategory === '' || 
        recipe.category === this.selectedCategory;
      
      return matchesSearch && matchesCategory;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedCategory = '';
    this.filteredRecipes = [...this.recipes];
  }

  addRecipe(): void {
    console.log('Add recipe clicked');
  }

  editRecipe(recipe: Recipe): void {
    console.log('Edit recipe clicked', recipe);
  }

  deleteRecipe(recipe: Recipe): void {
    console.log('Delete recipe clicked', recipe);
  }

  viewRecipeDetails(recipe: Recipe): void {
    console.log('View recipe details clicked', recipe);
  }

  duplicateRecipe(recipe: Recipe): void {
    console.log('Duplicate recipe clicked', recipe);
  }

  toggleRecipeStatus(recipe: Recipe): void {
    console.log('Toggle recipe status clicked', recipe);
    const index = this.recipes.findIndex(r => r.id === recipe.id);
    if (index !== -1) {
      this.recipes[index].status = this.recipes[index].status === 'Active' ? 'Inactive' : 'Active';
      this.filteredRecipes = [...this.filteredRecipes]; // Trigger change detection
    }
  }
}
