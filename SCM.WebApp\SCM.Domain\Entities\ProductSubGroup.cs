using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class ProductSubGroup : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? GroupId { get; set; }
    
    // Navigation properties
    public virtual ProductGroup? Group { get; set; }
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}
