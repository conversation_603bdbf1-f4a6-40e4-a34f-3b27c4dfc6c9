using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class ProductGroupsController : ApiControllerBase
{
    private readonly IProductGroupService _productGroupService;

    public ProductGroupsController(IProductGroupService productGroupService)
    {
        _productGroupService = productGroupService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ProductGroupDto>>> GetAll()
    {
        var productGroups = await _productGroupService.GetAllProductGroupsAsync();
        return Ok(productGroups);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ProductGroupDto>> GetById(int id)
    {
        var productGroup = await _productGroupService.GetProductGroupByIdAsync(id);
        if (productGroup == null)
            return NotFound();

        return Ok(productGroup);
    }

    [HttpGet("department/{departmentId}")]
    public async Task<ActionResult<IEnumerable<ProductGroupDto>>> GetByDepartmentId(int departmentId)
    {
        var productGroups = await _productGroupService.GetProductGroupsByDepartmentIdAsync(departmentId);
        return Ok(productGroups);
    }

    [HttpGet("{id}/with-subgroups")]
    public async Task<ActionResult<ProductGroupDto>> GetWithSubGroups(int id)
    {
        var productGroup = await _productGroupService.GetProductGroupWithSubGroupsAsync(id);
        if (productGroup == null)
            return NotFound();

        return Ok(productGroup);
    }

    [HttpPost]
    public async Task<ActionResult<ProductGroupDto>> Create(CreateProductGroupDto createProductGroupDto)
    {
        var productGroup = await _productGroupService.CreateProductGroupAsync(createProductGroupDto);
        return CreatedAtAction(nameof(GetById), new { id = productGroup.Id }, productGroup);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateProductGroupDto updateProductGroupDto)
    {
        if (id != updateProductGroupDto.Id)
            return BadRequest();

        try
        {
            await _productGroupService.UpdateProductGroupAsync(updateProductGroupDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _productGroupService.DeleteProductGroupAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
