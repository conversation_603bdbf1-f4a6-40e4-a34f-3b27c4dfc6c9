import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  PurchaseOrder, 
  PurchaseOrderListItem, 
  CreatePurchaseOrder, 
  UpdatePurchaseOrder,
  UpdatePurchaseOrderDetail
} from '../models/purchase-order.model';

@Injectable({
  providedIn: 'root'
})
export class PurchaseOrderService {
  private readonly path = 'purchaseorders';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<PurchaseOrderListItem[]> {
    return this.apiService.get<PurchaseOrderListItem[]>(this.path);
  }

  getById(id: number): Observable<PurchaseOrder> {
    return this.apiService.get<PurchaseOrder>(`${this.path}/${id}`);
  }

  getByCostCenter(costCenterId: number): Observable<PurchaseOrderListItem[]> {
    return this.apiService.get<PurchaseOrderListItem[]>(`${this.path}/costcenter/${costCenterId}`);
  }

  getBySupplier(supplierId: number): Observable<PurchaseOrderListItem[]> {
    return this.apiService.get<PurchaseOrderListItem[]>(`${this.path}/supplier/${supplierId}`);
  }

  getByStatus(status: string): Observable<PurchaseOrderListItem[]> {
    return this.apiService.get<PurchaseOrderListItem[]>(`${this.path}/status/${status}`);
  }

  create(purchaseOrder: CreatePurchaseOrder): Observable<PurchaseOrder> {
    return this.apiService.post<PurchaseOrder, CreatePurchaseOrder>(this.path, purchaseOrder);
  }

  update(id: number, purchaseOrder: UpdatePurchaseOrder): Observable<void> {
    return this.apiService.put<void, UpdatePurchaseOrder>(`${this.path}/${id}`, purchaseOrder);
  }

  updateDetail(id: number, detail: UpdatePurchaseOrderDetail): Observable<void> {
    return this.apiService.put<void, UpdatePurchaseOrderDetail>(`${this.path}/details/${id}`, detail);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  deleteDetail(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/details/${id}`);
  }

  approve(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/approve`, {});
  }

  reject(id: number, reason: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/reject`, { reason });
  }

  cancel(id: number, reason: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/cancel`, { reason });
  }
}
