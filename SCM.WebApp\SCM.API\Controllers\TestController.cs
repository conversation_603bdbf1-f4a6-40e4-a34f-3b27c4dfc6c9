using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SCM.Infrastructure.Data;

namespace SCM.API.Controllers;

public class TestController : ApiControllerBase
{
    private readonly ApplicationDbContext _dbContext;

    public TestController(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpGet("database")]
    public async Task<IActionResult> TestDatabaseConnection()
    {
        try
        {
            // Test if we can connect to the database
            bool canConnect = await _dbContext.Database.CanConnectAsync();
            
            if (canConnect)
            {
                // Get some basic stats about the database
                var stats = new
                {
                    DatabaseName = _dbContext.Database.GetDbConnection().Database,
                    ConnectionString = _dbContext.Database.GetConnectionString(),
                    ProductCount = await _dbContext.Products.CountAsync(),
                    DepartmentCount = await _dbContext.Departments.CountAsync(),
                    UnitCount = await _dbContext.Units.CountAsync(),
                    StoreCount = await _dbContext.Stores.CountAsync()
                };
                
                return Ok(new
                {
                    Success = true,
                    Message = "Successfully connected to the database",
                    Stats = stats
                });
            }
            else
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "Could not connect to the database"
                });
            }
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                Success = false,
                Message = "An error occurred while testing the database connection",
                Error = ex.Message,
                InnerError = ex.InnerException?.Message
            });
        }
    }
}
