using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class ProductSubGroupService : IProductSubGroupService
{
    private readonly IRepository<ProductSubGroup> _productSubGroupRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public ProductSubGroupService(
        IRepository<ProductSubGroup> productSubGroupRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _productSubGroupRepository = productSubGroupRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<ProductSubGroupDto>> GetAllProductSubGroupsAsync()
    {
        var productSubGroups = await _dbContext.ProductSubGroups
            .Include(psg => psg.Group)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<ProductSubGroupDto>>(productSubGroups);
    }

    public async Task<ProductSubGroupDto?> GetProductSubGroupByIdAsync(int id)
    {
        var productSubGroup = await _dbContext.ProductSubGroups
            .Include(psg => psg.Group)
            .FirstOrDefaultAsync(psg => psg.Id == id);
            
        return productSubGroup != null ? _mapper.Map<ProductSubGroupDto>(productSubGroup) : null;
    }

    public async Task<IEnumerable<ProductSubGroupDto>> GetProductSubGroupsByGroupIdAsync(int groupId)
    {
        var productSubGroups = await _dbContext.ProductSubGroups
            .Include(psg => psg.Group)
            .Where(psg => psg.GroupId == groupId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<ProductSubGroupDto>>(productSubGroups);
    }

    public async Task<ProductSubGroupDto> CreateProductSubGroupAsync(CreateProductSubGroupDto createProductSubGroupDto)
    {
        var productSubGroup = _mapper.Map<ProductSubGroup>(createProductSubGroupDto);
        await _productSubGroupRepository.AddAsync(productSubGroup);
        
        // Reload the product sub-group with the group
        var createdProductSubGroup = await _dbContext.ProductSubGroups
            .Include(psg => psg.Group)
            .FirstOrDefaultAsync(psg => psg.Id == productSubGroup.Id);
            
        return _mapper.Map<ProductSubGroupDto>(createdProductSubGroup);
    }

    public async Task UpdateProductSubGroupAsync(UpdateProductSubGroupDto updateProductSubGroupDto)
    {
        var productSubGroup = await _productSubGroupRepository.GetByIdAsync(updateProductSubGroupDto.Id);
        if (productSubGroup == null)
            throw new KeyNotFoundException($"ProductSubGroup with ID {updateProductSubGroupDto.Id} not found.");

        _mapper.Map(updateProductSubGroupDto, productSubGroup);
        await _productSubGroupRepository.UpdateAsync(productSubGroup);
    }

    public async Task DeleteProductSubGroupAsync(int id)
    {
        var productSubGroup = await _productSubGroupRepository.GetByIdAsync(id);
        if (productSubGroup == null)
            throw new KeyNotFoundException($"ProductSubGroup with ID {id} not found.");

        // Check if there are any products using this product sub-group
        var hasProducts = await _dbContext.Products.AnyAsync(p => p.SubGroupId == id);
        if (hasProducts)
            throw new InvalidOperationException($"Cannot delete ProductSubGroup with ID {id} because it is being used by one or more products.");

        await _productSubGroupRepository.DeleteAsync(productSubGroup);
    }
}
