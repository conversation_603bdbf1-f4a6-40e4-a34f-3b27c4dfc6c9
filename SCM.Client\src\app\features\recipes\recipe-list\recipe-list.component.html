<div class="page-container">
  <div class="page-header">
    <h1>Recipes</h1>
    <button mat-raised-button color="primary" (click)="addRecipe()">
      <mat-icon>add</mat-icon> Add Recipe
    </button>
  </div>
  
  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name or ID">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Category</mat-label>
          <mat-select [(ngModel)]="selectedCategory" (selectionChange)="applyFilter()">
            <mat-option value="">All Categories</mat-option>
            <mat-option *ngFor="let category of categories" [value]="category">
              {{category}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>
  
  <div class="table-container mat-elevation-z2">
    <table mat-table [dataSource]="filteredRecipes" class="recipe-table">
      <!-- ID Column -->
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef>ID</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.id}}</td>
      </ng-container>
      
      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef>Name</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.name}}</td>
      </ng-container>
      
      <!-- Category Column -->
      <ng-container matColumnDef="category">
        <th mat-header-cell *matHeaderCellDef>Category</th>
        <td mat-cell *matCellDef="let recipe">
          <mat-chip-option selected disableRipple>{{recipe.category}}</mat-chip-option>
        </td>
      </ng-container>
      
      <!-- Yield Column -->
      <ng-container matColumnDef="yield">
        <th mat-header-cell *matHeaderCellDef>Yield</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.yield}} {{recipe.yieldUnit}}</td>
      </ng-container>
      
      <!-- Ingredient Count Column -->
      <ng-container matColumnDef="ingredientCount">
        <th mat-header-cell *matHeaderCellDef>Ingredients</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.ingredientCount}}</td>
      </ng-container>
      
      <!-- Cost Per Unit Column -->
      <ng-container matColumnDef="costPerUnit">
        <th mat-header-cell *matHeaderCellDef>Cost Per Unit</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.costPerUnit | currency}}</td>
      </ng-container>
      
      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let recipe" [ngClass]="{'status-active': recipe.status === 'Active', 'status-inactive': recipe.status === 'Inactive'}">
          {{recipe.status}}
        </td>
      </ng-container>
      
      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let recipe">
          <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="viewRecipeDetails(recipe)">
              <mat-icon>visibility</mat-icon>
              <span>View Details</span>
            </button>
            <button mat-menu-item (click)="editRecipe(recipe)">
              <mat-icon>edit</mat-icon>
              <span>Edit</span>
            </button>
            <button mat-menu-item (click)="duplicateRecipe(recipe)">
              <mat-icon>content_copy</mat-icon>
              <span>Duplicate</span>
            </button>
            <button mat-menu-item (click)="toggleRecipeStatus(recipe)">
              <mat-icon>{{ recipe.status === 'Active' ? 'toggle_off' : 'toggle_on' }}</mat-icon>
              <span>{{ recipe.status === 'Active' ? 'Deactivate' : 'Activate' }}</span>
            </button>
            <button mat-menu-item (click)="deleteRecipe(recipe)">
              <mat-icon>delete</mat-icon>
              <span>Delete</span>
            </button>
          </mat-menu>
        </td>
      </ng-container>
      
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
    
    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
