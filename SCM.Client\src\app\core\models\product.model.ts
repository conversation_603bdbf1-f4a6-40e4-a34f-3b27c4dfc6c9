export interface Product {
  id: number;
  code: string;
  name: string;
  brandId?: number;
  brandName?: string;
  unitId?: number;
  unitName?: string;
  unitGroupId?: number;
  unitGroupName?: string;
  departmentId?: number;
  departmentName?: string;
  groupId?: number;
  groupName?: string;
  subGroupId?: number;
  subGroupName?: string;
  costPrice?: number;
  averageCost?: number;
  salesPrice?: number;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  notes?: string;
  isStockItem: boolean;
  isRecipe: boolean;
  hasExpiry: boolean;
  isProduction: boolean;
  isSaleable: boolean;
  taxId?: number;
  taxName?: string;
  taxRate?: number;
  salesUnitId?: number;
  salesUnitName?: string;
  salesUnitConversionFactor?: number;
  allowDiscount: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  barcodes?: Barcode[];
}

export interface CreateProduct {
  code: string;
  name: string;
  brandId?: number;
  unitId?: number;
  unitGroupId?: number;
  departmentId?: number;
  groupId?: number;
  subGroupId?: number;
  costPrice?: number;
  salesPrice?: number;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  notes?: string;
  isStockItem: boolean;
  isRecipe: boolean;
  hasExpiry: boolean;
  isProduction: boolean;
  isSaleable: boolean;
  taxId?: number;
  salesUnitId?: number;
  salesUnitConversionFactor?: number;
  allowDiscount: boolean;
}

export interface UpdateProduct {
  id: number;
  code: string;
  name: string;
  brandId?: number;
  unitId?: number;
  unitGroupId?: number;
  departmentId?: number;
  groupId?: number;
  subGroupId?: number;
  costPrice?: number;
  salesPrice?: number;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  notes?: string;
  isStockItem: boolean;
  isRecipe: boolean;
  hasExpiry: boolean;
  isProduction: boolean;
  isSaleable: boolean;
  taxId?: number;
  salesUnitId?: number;
  salesUnitConversionFactor?: number;
  allowDiscount: boolean;
  isActive: boolean;
}

export interface Barcode {
  id: number;
  productId: number;
  code: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt?: Date;
}
