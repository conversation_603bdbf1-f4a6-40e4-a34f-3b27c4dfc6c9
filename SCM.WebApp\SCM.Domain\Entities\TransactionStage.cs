using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionStage : BaseEntity
{
    public int TransactionHeaderId { get; set; }
    public int TransactionStageTypeId { get; set; }
    public string Status { get; set; } = "Pending"; // Pending, Completed, Cancelled
    public DateTime? CompletedAt { get; set; }
    public int? CompletedById { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual TransactionHeader TransactionHeader { get; set; } = null!;
    public virtual TransactionStageType TransactionStageType { get; set; } = null!;
    public virtual User? CompletedBy { get; set; }
}
