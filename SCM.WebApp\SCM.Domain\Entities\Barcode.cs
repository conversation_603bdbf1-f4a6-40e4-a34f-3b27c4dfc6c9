using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Barcode : BaseEntity
{
    public int ProductId { get; set; }
    public string BarcodeValue { get; set; } = string.Empty;
    public string BarcodeType { get; set; } = "EAN13";
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; } = 1;
    public bool IsPrimary { get; set; } = false;
    
    // Navigation properties
    public virtual Product Product { get; set; } = null!;
    public virtual Unit? Unit { get; set; }
}
