import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON>rray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTabsModule } from '@angular/material/tabs';
import { Observable, map, startWith } from 'rxjs';

interface Product {
  id: string;
  name: string;
  unitSize: string;
  sellingPrice: number;
  currentStock: number;
}

interface Customer {
  id: number;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
}

interface Sale {
  id: string;
  date: Date;
  customer: string;
  costCenter: string;
  status: string;
  total: number;
}

@Component({
  selector: 'app-sales',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatTabsModule
  ],
  templateUrl: './sales.component.html',
  styleUrls: ['./sales.component.scss']
})
export class SalesComponent implements OnInit {
  salesForm!: FormGroup;
  invoiceNo: string = 'INV-00001';
  currentDate: Date = new Date();
  
  displayedColumns: string[] = [
    'productId', 
    'productName', 
    'unitSize',
    'availableStock',
    'quantity', 
    'unitPrice', 
    'discount',
    'total',
    'actions'
  ];
  
  historyColumns: string[] = [
    'id',
    'date',
    'customer',
    'costCenter',
    'status',
    'total',
    'actions'
  ];
  
  products: Product[] = [
    { id: 'P001', name: 'Rice', unitSize: '25kg', sellingPrice: 55.00, currentStock: 120 },
    { id: 'P002', name: 'Flour', unitSize: '10kg', sellingPrice: 28.50, currentStock: 85 },
    { id: 'P003', name: 'Soft Drinks', unitSize: '24x330ml', sellingPrice: 45.00, currentStock: 45 },
    { id: 'P004', name: 'Cleaning Liquid', unitSize: '5L', sellingPrice: 22.75, currentStock: 32 },
    { id: 'P005', name: 'Light Bulbs', unitSize: '10pcs', sellingPrice: 52.00, currentStock: 15 },
    { id: 'P006', name: 'Cigarettes', unitSize: '200pcs', sellingPrice: 150.00, currentStock: 25 },
    { id: 'P007', name: 'Paper Towels', unitSize: '12 rolls', sellingPrice: 30.50, currentStock: 40 },
    { id: 'P008', name: 'Coffee', unitSize: '1kg', sellingPrice: 78.00, currentStock: 18 }
  ];
  
  customers: Customer[] = [
    { 
      id: 1, 
      name: 'Hotel Grand', 
      contactPerson: 'John Smith', 
      email: '<EMAIL>', 
      phone: '************', 
      address: '123 Main St, City, Country' 
    },
    { 
      id: 2, 
      name: 'Restaurant Elite', 
      contactPerson: 'Jane Doe', 
      email: '<EMAIL>', 
      phone: '************', 
      address: '456 Oak St, City, Country' 
    },
    { 
      id: 3, 
      name: 'Cafe Central', 
      contactPerson: 'Mike Johnson', 
      email: '<EMAIL>', 
      phone: '************', 
      address: '789 Pine St, City, Country' 
    }
  ];
  
  sales: Sale[] = [
    { id: 'INV-00001', date: new Date(2025, 4, 5), customer: 'Hotel Grand', costCenter: '1 - Food Store', status: 'Completed', total: 2500.00 },
    { id: 'INV-00002', date: new Date(2025, 4, 3), customer: 'Restaurant Elite', costCenter: '2 - Beverage Store', status: 'Pending', total: 1800.50 },
    { id: 'INV-00003', date: new Date(2025, 4, 1), customer: 'Cafe Central', costCenter: '3 - General Store', status: 'Completed', total: 3200.75 },
    { id: 'INV-00004', date: new Date(2025, 3, 28), customer: 'Hotel Grand', costCenter: '1 - Food Store', status: 'Completed', total: 1950.25 }
  ];
  
  filteredProducts: Observable<Product[]>[] = [];
  
  costCenters = [
    { id: 1, name: '1 - Food Store' },
    { id: 2, name: '2 - Beverage Store' },
    { id: 3, name: '3 - General Store' },
    { id: 4, name: '4 - Engineering Store' },
    { id: 5, name: '5 - S.O.E. Store' },
    { id: 6, name: '6 - Tobacco' }
  ];
  
  paymentMethods = [
    { id: 1, name: 'Cash' },
    { id: 2, name: 'Credit Card' },
    { id: 3, name: 'Bank Transfer' },
    { id: 4, name: 'Check' },
    { id: 5, name: 'On Account' }
  ];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.salesForm = this.fb.group({
      saleDate: [this.currentDate, Validators.required],
      customer: ['', Validators.required],
      costCenter: ['', Validators.required],
      paymentMethod: ['', Validators.required],
      reference: [''],
      notes: [''],
      items: this.fb.array([])
    });
    
    // Add an empty row by default
    this.addItem();
  }

  get items(): FormArray {
    return this.salesForm.get('items') as FormArray;
  }

  addItem(): void {
    const itemForm = this.fb.group({
      productId: ['', Validators.required],
      productName: [''],
      unitSize: [''],
      availableStock: [{ value: 0, disabled: true }],
      quantity: [1, [Validators.required, Validators.min(1)]],
      unitPrice: [0, [Validators.required, Validators.min(0)]],
      discount: [0, [Validators.min(0), Validators.max(100)]],
      total: [{ value: 0, disabled: true }]
    });
    
    // Set up product autocomplete filtering
    const index = this.items.length;
    this.setupProductAutocomplete(itemForm, index);
    
    // Auto-calculate total when quantity, unit price, or discount changes
    itemForm.get('quantity')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(itemForm);
      this.validateQuantity(itemForm);
    });
    
    itemForm.get('unitPrice')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(itemForm);
    });
    
    itemForm.get('discount')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(itemForm);
    });
    
    this.items.push(itemForm);
  }

  setupProductAutocomplete(itemForm: FormGroup, index: number): void {
    const productIdControl = itemForm.get('productId');
    
    this.filteredProducts[index] = productIdControl!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterProducts(value || ''))
    );
    
    productIdControl?.valueChanges.subscribe(productId => {
      const product = this.products.find(p => p.id === productId);
      if (product) {
        itemForm.patchValue({
          productName: product.name,
          unitSize: product.unitSize,
          availableStock: product.currentStock,
          unitPrice: product.sellingPrice
        });
        this.calculateItemTotal(itemForm);
        this.validateQuantity(itemForm);
      } else {
        itemForm.patchValue({
          productName: '',
          unitSize: '',
          availableStock: 0,
          unitPrice: 0
        });
      }
    });
  }

  private _filterProducts(value: string): Product[] {
    const filterValue = value.toLowerCase();
    return this.products.filter(product => 
      product.id.toLowerCase().includes(filterValue) || 
      product.name.toLowerCase().includes(filterValue)
    );
  }

  calculateItemTotal(itemForm: FormGroup): void {
    const quantity = itemForm.get('quantity')?.value || 0;
    const unitPrice = itemForm.get('unitPrice')?.value || 0;
    const discount = itemForm.get('discount')?.value || 0;
    
    const total = quantity * unitPrice * (1 - discount / 100);
    
    itemForm.get('total')?.setValue(total);
    this.calculateOrderTotal();
  }

  validateQuantity(itemForm: FormGroup): void {
    const availableStock = itemForm.get('availableStock')?.value || 0;
    const quantity = itemForm.get('quantity')?.value || 0;
    
    if (quantity > availableStock) {
      itemForm.get('quantity')?.setErrors({ exceedsStock: true });
    } else {
      const errors = itemForm.get('quantity')?.errors;
      if (errors) {
        delete errors['exceedsStock'];
        if (Object.keys(errors).length === 0) {
          itemForm.get('quantity')?.setErrors(null);
        } else {
          itemForm.get('quantity')?.setErrors(errors);
        }
      }
    }
  }

  calculateOrderTotal(): number {
    let total = 0;
    for (const item of this.items.controls) {
      total += item.get('total')?.value || 0;
    }
    return total;
  }

  removeItem(index: number): void {
    this.items.removeAt(index);
    this.filteredProducts.splice(index, 1);
    this.calculateOrderTotal();
  }

  displayProductFn(productId: string): string {
    if (!productId) return '';
    const product = this.products.find(p => p.id === productId);
    return product ? `${product.id} - ${product.name}` : '';
  }

  saveSale(): void {
    if (this.salesForm.valid) {
      console.log('Sale data:', this.salesForm.getRawValue());
      
      this.snackBar.open('Sale saved successfully', 'Close', {
        duration: 3000
      });
      
      // Reset form after successful save
      this.initForm();
    } else {
      this.markFormGroupTouched(this.salesForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  viewSale(sale: Sale): void {
    console.log('View sale:', sale);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
