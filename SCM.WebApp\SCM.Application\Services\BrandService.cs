using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class BrandService : IBrandService
{
    private readonly IRepository<Brand> _brandRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public BrandService(
        IRepository<Brand> brandRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _brandRepository = brandRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<BrandDto>> GetAllBrandsAsync()
    {
        var brands = await _brandRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<BrandDto>>(brands);
    }

    public async Task<BrandDto?> GetBrandByIdAsync(int id)
    {
        var brand = await _brandRepository.GetByIdAsync(id);
        return brand != null ? _mapper.Map<BrandDto>(brand) : null;
    }

    public async Task<BrandDto> CreateBrandAsync(CreateBrandDto createBrandDto)
    {
        var brand = _mapper.Map<Brand>(createBrandDto);
        await _brandRepository.AddAsync(brand);
        return _mapper.Map<BrandDto>(brand);
    }

    public async Task UpdateBrandAsync(UpdateBrandDto updateBrandDto)
    {
        var brand = await _brandRepository.GetByIdAsync(updateBrandDto.Id);
        if (brand == null)
            throw new KeyNotFoundException($"Brand with ID {updateBrandDto.Id} not found.");

        _mapper.Map(updateBrandDto, brand);
        await _brandRepository.UpdateAsync(brand);
    }

    public async Task DeleteBrandAsync(int id)
    {
        var brand = await _brandRepository.GetByIdAsync(id);
        if (brand == null)
            throw new KeyNotFoundException($"Brand with ID {id} not found.");

        // Check if there are any products using this brand
        var hasProducts = await _dbContext.Products.AnyAsync(p => p.BrandId == id);
        if (hasProducts)
            throw new InvalidOperationException($"Cannot delete Brand with ID {id} because it is being used by one or more products.");

        await _brandRepository.DeleteAsync(brand);
    }
}
