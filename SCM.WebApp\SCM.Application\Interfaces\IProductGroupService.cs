using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IProductGroupService
{
    Task<IEnumerable<ProductGroupDto>> GetAllProductGroupsAsync();
    Task<ProductGroupDto?> GetProductGroupByIdAsync(int id);
    Task<IEnumerable<ProductGroupDto>> GetProductGroupsByDepartmentIdAsync(int departmentId);
    Task<ProductGroupDto?> GetProductGroupWithSubGroupsAsync(int id);
    Task<ProductGroupDto> CreateProductGroupAsync(CreateProductGroupDto createProductGroupDto);
    Task UpdateProductGroupAsync(UpdateProductGroupDto updateProductGroupDto);
    Task DeleteProductGroupAsync(int id);
}
