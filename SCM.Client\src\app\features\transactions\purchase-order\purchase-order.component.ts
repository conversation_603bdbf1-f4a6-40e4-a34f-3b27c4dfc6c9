import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTabsModule } from '@angular/material/tabs';
import { Observable, map, startWith } from 'rxjs';

interface Product {
  id: string;
  name: string;
  unitSize: string;
  lastPurchasePrice: number;
}

interface Supplier {
  id: number;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
}

interface PurchaseOrder {
  id: string;
  date: Date;
  supplier: string;
  costCenter: string;
  status: string;
  total: number;
}

@Component({
  selector: 'app-purchase-order',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatTabsModule
  ],
  templateUrl: './purchase-order.component.html',
  styleUrls: ['./purchase-order.component.scss']
})
export class PurchaseOrderComponent implements OnInit {
  poForm!: FormGroup;
  poNo: string = 'PO-00001';
  currentDate: Date = new Date();
  
  displayedColumns: string[] = [
    'productId', 
    'productName', 
    'unitSize',
    'quantity', 
    'unitPrice', 
    'total',
    'actions'
  ];
  
  historyColumns: string[] = [
    'id',
    'date',
    'supplier',
    'costCenter',
    'status',
    'total',
    'actions'
  ];
  
  products: Product[] = [
    { id: 'P001', name: 'Rice', unitSize: '25kg', lastPurchasePrice: 45.50 },
    { id: 'P002', name: 'Flour', unitSize: '10kg', lastPurchasePrice: 22.75 },
    { id: 'P003', name: 'Soft Drinks', unitSize: '24x330ml', lastPurchasePrice: 36.00 },
    { id: 'P004', name: 'Cleaning Liquid', unitSize: '5L', lastPurchasePrice: 18.25 },
    { id: 'P005', name: 'Light Bulbs', unitSize: '10pcs', lastPurchasePrice: 42.00 },
    { id: 'P006', name: 'Cigarettes', unitSize: '200pcs', lastPurchasePrice: 120.00 },
    { id: 'P007', name: 'Paper Towels', unitSize: '12 rolls', lastPurchasePrice: 24.50 },
    { id: 'P008', name: 'Coffee', unitSize: '1kg', lastPurchasePrice: 65.00 }
  ];
  
  suppliers: Supplier[] = [
    { 
      id: 1, 
      name: 'ABC Supplies', 
      contactPerson: 'John Smith', 
      email: '<EMAIL>', 
      phone: '************', 
      address: '123 Main St, City, Country' 
    },
    { 
      id: 2, 
      name: 'XYZ Foods', 
      contactPerson: 'Jane Doe', 
      email: '<EMAIL>', 
      phone: '************', 
      address: '456 Oak St, City, Country' 
    },
    { 
      id: 3, 
      name: 'Global Beverages', 
      contactPerson: 'Mike Johnson', 
      email: '<EMAIL>', 
      phone: '************', 
      address: '789 Pine St, City, Country' 
    }
  ];
  
  purchaseOrders: PurchaseOrder[] = [
    { id: 'PO-00001', date: new Date(2025, 4, 5), supplier: 'ABC Supplies', costCenter: '1 - Food Store', status: 'Pending', total: 2500.00 },
    { id: 'PO-00002', date: new Date(2025, 4, 3), supplier: 'XYZ Foods', costCenter: '2 - Beverage Store', status: 'Approved', total: 1800.50 },
    { id: 'PO-00003', date: new Date(2025, 4, 1), supplier: 'Global Beverages', costCenter: '3 - General Store', status: 'Completed', total: 3200.75 },
    { id: 'PO-00004', date: new Date(2025, 3, 28), supplier: 'ABC Supplies', costCenter: '1 - Food Store', status: 'Completed', total: 1950.25 }
  ];
  
  filteredProducts: Observable<Product[]>[] = [];
  
  costCenters = [
    { id: 1, name: '1 - Food Store' },
    { id: 2, name: '2 - Beverage Store' },
    { id: 3, name: '3 - General Store' },
    { id: 4, name: '4 - Engineering Store' },
    { id: 5, name: '5 - S.O.E. Store' },
    { id: 6, name: '6 - Tobacco' }
  ];
  
  paymentTerms = [
    { id: 1, name: 'Cash on Delivery' },
    { id: 2, name: 'Net 15' },
    { id: 3, name: 'Net 30' },
    { id: 4, name: 'Net 45' },
    { id: 5, name: 'Net 60' }
  ];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.poForm = this.fb.group({
      poDate: [this.currentDate, Validators.required],
      supplier: ['', Validators.required],
      costCenter: ['', Validators.required],
      paymentTerms: ['', Validators.required],
      expectedDeliveryDate: ['', Validators.required],
      notes: [''],
      items: this.fb.array([])
    });
    
    // Add an empty row by default
    this.addItem();
  }

  get items(): FormArray {
    return this.poForm.get('items') as FormArray;
  }

  addItem(): void {
    const itemForm = this.fb.group({
      productId: ['', Validators.required],
      productName: [''],
      unitSize: [''],
      quantity: [1, [Validators.required, Validators.min(1)]],
      unitPrice: [0, [Validators.required, Validators.min(0)]],
      total: [{ value: 0, disabled: true }]
    });
    
    // Set up product autocomplete filtering
    const index = this.items.length;
    this.setupProductAutocomplete(itemForm, index);
    
    // Auto-calculate total when quantity or unit price changes
    itemForm.get('quantity')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(itemForm);
    });
    
    itemForm.get('unitPrice')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(itemForm);
    });
    
    this.items.push(itemForm);
  }

  setupProductAutocomplete(itemForm: FormGroup, index: number): void {
    const productIdControl = itemForm.get('productId');
    
    this.filteredProducts[index] = productIdControl!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterProducts(value || ''))
    );
    
    productIdControl?.valueChanges.subscribe(productId => {
      const product = this.products.find(p => p.id === productId);
      if (product) {
        itemForm.patchValue({
          productName: product.name,
          unitSize: product.unitSize,
          unitPrice: product.lastPurchasePrice
        });
        this.calculateItemTotal(itemForm);
      } else {
        itemForm.patchValue({
          productName: '',
          unitSize: '',
          unitPrice: 0
        });
      }
    });
  }

  private _filterProducts(value: string): Product[] {
    const filterValue = value.toLowerCase();
    return this.products.filter(product => 
      product.id.toLowerCase().includes(filterValue) || 
      product.name.toLowerCase().includes(filterValue)
    );
  }

  calculateItemTotal(itemForm: FormGroup): void {
    const quantity = itemForm.get('quantity')?.value || 0;
    const unitPrice = itemForm.get('unitPrice')?.value || 0;
    const total = quantity * unitPrice;
    
    itemForm.get('total')?.setValue(total);
    this.calculateOrderTotal();
  }

  calculateOrderTotal(): number {
    let total = 0;
    for (const item of this.items.controls) {
      total += item.get('total')?.value || 0;
    }
    return total;
  }

  removeItem(index: number): void {
    this.items.removeAt(index);
    this.filteredProducts.splice(index, 1);
    this.calculateOrderTotal();
  }

  displayProductFn(productId: string): string {
    if (!productId) return '';
    const product = this.products.find(p => p.id === productId);
    return product ? `${product.id} - ${product.name}` : '';
  }

  savePurchaseOrder(): void {
    if (this.poForm.valid) {
      console.log('Purchase Order data:', this.poForm.getRawValue());
      
      this.snackBar.open('Purchase Order saved successfully', 'Close', {
        duration: 3000
      });
      
      // Reset form after successful save
      this.initForm();
    } else {
      this.markFormGroupTouched(this.poForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  viewPurchaseOrder(po: PurchaseOrder): void {
    console.log('View purchase order:', po);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
