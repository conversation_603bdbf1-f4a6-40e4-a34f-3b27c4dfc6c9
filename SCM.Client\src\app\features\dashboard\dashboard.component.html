<div class="dashboard-container">
  <div class="dashboard-header">
    <h1>Dashboard</h1>
    <div class="date-display">
      {{ 'Today: ' + (today | date:'fullDate') }}
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div *ngIf="!isLoading" class="summary-cards">
    <mat-card class="summary-card">
      <mat-card-content>
        <div class="card-icon">
          <mat-icon>inventory_2</mat-icon>
        </div>
        <div class="card-data">
          <div class="card-value">{{ inventorySummary.totalProducts }}</div>
          <div class="card-label">Total Products</div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="summary-card warning">
      <mat-card-content>
        <div class="card-icon">
          <mat-icon>warning</mat-icon>
        </div>
        <div class="card-data">
          <div class="card-value">{{ inventorySummary.lowStockItems }}</div>
          <div class="card-label">Low Stock Items</div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="summary-card info">
      <mat-card-content>
        <div class="card-icon">
          <mat-icon>pending_actions</mat-icon>
        </div>
        <div class="card-data">
          <div class="card-value">{{ inventorySummary.pendingRequests }}</div>
          <div class="card-label">Pending Requests</div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="summary-card info">
      <mat-card-content>
        <div class="card-icon">
          <mat-icon>swap_horiz</mat-icon>
        </div>
        <div class="card-data">
          <div class="card-value">{{ inventorySummary.pendingTransfers }}</div>
          <div class="card-label">Pending Transfers</div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="summary-card success">
      <mat-card-content>
        <div class="card-icon">
          <mat-icon>attach_money</mat-icon>
        </div>
        <div class="card-data">
          <div class="card-value">{{ inventorySummary.totalValue | currency }}</div>
          <div class="card-label">Total Inventory Value</div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <div *ngIf="!isLoading" class="dashboard-actions">
    <button mat-raised-button color="primary" (click)="createStockRequest()">
      <mat-icon>add</mat-icon> Create Stock Request
    </button>
  </div>

  <div *ngIf="!isLoading" class="dashboard-sections">
    <mat-card class="dashboard-section">
      <mat-card-header>
        <mat-card-title>Recent Transactions</mat-card-title>
        <button mat-button color="primary" (click)="viewAllTransactions()">View All</button>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="recentTransactions.length === 0" class="no-data-message">
          No recent transactions found.
        </div>
        <table *ngIf="recentTransactions.length > 0" mat-table [dataSource]="recentTransactions" class="mat-elevation-z0">
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>ID</th>
            <td mat-cell *matCellDef="let transaction">{{ transaction.id }}</td>
          </ng-container>

          <!-- Date Column -->
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef>Date</th>
            <td mat-cell *matCellDef="let transaction">{{ transaction.date | date:'shortDate' }}</td>
          </ng-container>

          <!-- Type Column -->
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef>Type</th>
            <td mat-cell *matCellDef="let transaction">{{ transaction.type }}</td>
          </ng-container>

          <!-- Amount Column -->
          <ng-container matColumnDef="amount">
            <th mat-header-cell *matHeaderCellDef>Amount</th>
            <td mat-cell *matCellDef="let transaction">{{ transaction.amount | currency }}</td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let transaction" [ngClass]="{'status-completed': transaction.status === 'Completed', 'status-pending': transaction.status === 'Pending'}">
              {{ transaction.status }}
            </td>
          </ng-container>

          <!-- Cost Center Column -->
          <ng-container matColumnDef="costCenter">
            <th mat-header-cell *matHeaderCellDef>Cost Center</th>
            <td mat-cell *matCellDef="let transaction">{{ transaction.costCenter }}</td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let transaction">
              <button mat-icon-button color="primary" (click)="viewAllTransactions()">
                <mat-icon>visibility</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="transactionColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: transactionColumns;"></tr>
        </table>
      </mat-card-content>
    </mat-card>

    <mat-card class="dashboard-section">
      <mat-card-header>
        <mat-card-title>Low Stock Items</mat-card-title>
        <button mat-button color="primary" (click)="viewAllLowStockItems()">View All</button>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="lowStockItems.length === 0" class="no-data-message">
          No low stock items found.
        </div>
        <table *ngIf="lowStockItems.length > 0" mat-table [dataSource]="lowStockItems" class="mat-elevation-z0">
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>ID</th>
            <td mat-cell *matCellDef="let item">{{ item.id }}</td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Name</th>
            <td mat-cell *matCellDef="let item">{{ item.name }}</td>
          </ng-container>

          <!-- Current Stock Column -->
          <ng-container matColumnDef="currentStock">
            <th mat-header-cell *matHeaderCellDef>Current Stock</th>
            <td mat-cell *matCellDef="let item" class="stock-level-warning">{{ item.currentStock }}</td>
          </ng-container>

          <!-- Min Level Column -->
          <ng-container matColumnDef="minLevel">
            <th mat-header-cell *matHeaderCellDef>Min Level</th>
            <td mat-cell *matCellDef="let item">{{ item.minLevel }}</td>
          </ng-container>

          <!-- Cost Center Column -->
          <ng-container matColumnDef="costCenter">
            <th mat-header-cell *matHeaderCellDef>Cost Center</th>
            <td mat-cell *matCellDef="let item">{{ item.costCenter }}</td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let item">
              <button mat-icon-button color="primary" (click)="createStockRequest()">
                <mat-icon>add_shopping_cart</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="lowStockColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: lowStockColumns;"></tr>
        </table>
      </mat-card-content>
    </mat-card>
  </div>
</div>
